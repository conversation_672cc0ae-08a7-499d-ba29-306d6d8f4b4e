import fetchAgent from '../../../services/agent/fetchAgent';
import { json, Request, Response } from 'express';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import { requestValidatorWithCb } from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import errorResponse from '../../../schema/errorResponse';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import LeadsModel from '../../../model/LeadsModel';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import aggregatesAgent from '../../../helpers/leads/aggregatesAgent';
import successResponse from '../../../schema/successResponse';
import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import { errorLogsAddLeads } from '../../../helpers/leads/errorLogs';
import leadsAlreadyAcquiredCheck from '../../../middlewares/leads/leadsAlreadyAcquiredCheck';
import afterAddLeads from '../../../helpers/leads/afterAddLeads';

interface ReqBody {
  agentCode: string;
  title: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  provinceName: string;
  provinceCode: string;
  cityName: string;
  cityCode: string;
  vehicleUsage: 'individual' | 'shared' | 'corporate';
  paymentPlan: 'cash' | 'credit';
  downPaymentPlan: number;
  hasVehicleLoan: boolean;
  vehicleOptions: {
    brand: {
      name: string;
    };
    model: {
      name: string;
    };
    variant: {
      code: string;
      name: string;
    };
    color: {
      code: string;
      name: string;
    };
  }[];
  source: string;
  source2: string;
  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition';
  nextTotalVehicleOwnerShip: string;
  notes: string;

  idCard_number: string | null;
  driverLicense_number: string | null;
}

const addLeadHandler = {
  middlewares: [
    triForceAuthChecker,
    json(),
    body('agentCode')
      .notEmpty()
      .trim()
      .withMessage('Kode Agen dibutuhkan')
      .custom((input, { req }) => {
        return new Promise((resolve, reject) => {
          fetchAgent(input)
            .then(fetch => {
              const validUserType = ['affiliate', 'internal'];
              if (!validUserType.includes(fetch.user_type.toLowerCase())) {
                reject('Kode agen tidak terdaftar sebagai internal ataupun affiliate.');
              } else {
                resolve(fetch);
              }
            })
            .catch(() => {
              reject('Kode Agen tidak terdaftar.');
            });
        })
          .then(value => {
            req.res.locals.agent = value;
          })
          .catch(reason => {
            throw new Error(reason);
          });
      }),
    body('title').notEmpty().withMessage('Title is required'),
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName')
      .optional()
      .customSanitizer(input => (!input ? '' : input)),
    body('phoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('email').optional({ values: 'falsy' }).isEmail().withMessage('Invalid email address'),
    body('provinceName').notEmpty().withMessage('Province name is required'),
    body('provinceCode').notEmpty().withMessage('Province code is required'),
    body('cityName').notEmpty().withMessage('City name is required'),
    body('cityCode').notEmpty().withMessage('City code is required'),
    body('vehicleUsage')
      .notEmpty()
      .withMessage('Vehicle usage is required')
      .isIn(['individual', 'shared', 'corporate'])
      .withMessage('Invalid vehicle usage'),
    body('paymentPlan')
      .notEmpty()
      .withMessage('Payment plan is required')
      .isIn(['cash', 'credit'])
      .withMessage('Invalid payment plan'),
    body('downPaymentPlan').optional({ values: 'falsy' }).isNumeric(),
    body('hasVehicleLoan')
      .optional()
      .default(false)
      .isBoolean()
      .withMessage('Invalid hasVehicleLoan format'),
    body('vehicleOptions')
      .isArray({ min: 1 })
      .withMessage('At least one vehicle option is required')
      .custom((value: ReqBody['vehicleOptions']) => {
        for (const option of value) {
          if (
            !option.brand?.name ||
            !option.model?.name ||
            !option.variant?.code ||
            !option.variant?.name ||
            !option.color?.code ||
            !option.color?.name
          ) {
            throw new Error('Invalid vehicle option format');
          }
        }
        return true;
      }),
    body('source2').notEmpty().withMessage('Source2 is required'),
    body('source').notEmpty().withMessage('Source is required'),
    body('purchasePlan')
      .notEmpty()
      .isIn(['firstVehicle', 'vehicleReplacement', 'vehicleAddition'])
      .withMessage('purchasePlan is required'),
    body('nextTotalVehicleOwnerShip').notEmpty(),
    body('notes')
      .notEmpty()
      .withMessage('Notes dibutuhkan')
      .isLength({ min: 30, max: 600 })
      .withMessage('Catatan Minimal 30 s.d 600 karakter.'),
    body('idCard_number')
      .optional()
      .customSanitizer(input => (!input ? null : input)),
    body('driverLicense_number')
      .optional()
      .customSanitizer(input => (!input ? null : input)),

    requestValidatorWithCb({
      notValidCb: errorLogsAddLeads,
    }),

    leadsAlreadyAcquiredCheck,
    // coldLeadsAllowAddCheck,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response) => {
    const org = req.body.source2;
    const collections = myFirestore.collection('leads');
    const docRef = collections.doc(`${org}-${req.body.phoneNumber}`);

    let agentName: string | null = null;
    let agentPhoneNumbers: string[] = [];
    try {
      const fetch = await fetchAgent(req.body.agentCode);
      agentName = fetch.name;
      agentPhoneNumbers = fetch.phones.map(value => value.phone);
    } catch (e) {
      console.log(e);
      agentName = 'NO_NAME';
    }
    const notes: ILeadsNotesDocument<Date> = {
      notes: req.body.notes,
      updatedAt: new Date(),
      updatedByUser: null,
      statusLevel: 0,
      agentCode: req.body.agentCode,
      event: 'init',
      phoneNumber: req.body.phoneNumber,
      organization: req.body.source2,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      agentName: agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: 0,
      },
      totalUpdateNotes: 1,
    };

    const leadsDoc = new LeadsModel({
      agentCode: req.body.agentCode,
      agentName: agentName,
      phoneNumberAgent: agentPhoneNumbers,
      createdAt: new Date(),
      domicile: {
        cityCode: req.body.cityCode,
        cityName: req.body.cityName,
        provinceCode: req.body.provinceCode,
        provinceName: req.body.provinceName,
      },
      email: req.body.email || null,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      hasVehicleLoan: req.body.hasVehicleLoan,
      paymentPlan: req.body.paymentPlan,
      phoneNumber: req.body.phoneNumber,
      ref: docRef,
      source: req.body.source,
      organization: req.body.source2,
      title: req.body.title,
      vehicleOptions: req.body.vehicleOptions,
      vehicleUsage: req.body.vehicleUsage,
      statusLevel: 0,

      purchasePlan: req.body.purchasePlan,
      nextTotalVehicleOwnerShip: req.body.nextTotalVehicleOwnerShip,

      isTracking: true,
      notes: req.body.notes,

      updateHistories: [
        {
          ...notes,
        },
      ],

      idCard_number: req.body.idCard_number ?? null,
      driverLicense_number: req.body.driverLicense_number ?? null,
      downPaymentPlan: req.body.downPaymentPlan || null,
    });

    try {
      const batch = myFirestore.batch();
      batch.set(leadsDoc.ref.withConverter(LeadsModel.converter), leadsDoc);
      batch.set(myFirestore.collection('leads_notes').doc(), notes);
      await batch.commit();

      await aggregatesAgent(req.body.agentCode, org).then();
    } catch (e: any) {
      errorLogsAddLeads(req, e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }

    afterAddLeads(leadsDoc)
      .then()
      .catch(e => {
        console.log('ERROR_AFTER_ADD_LEADS', e);
      });

    res.send(
      successResponse({
        data: leadsDoc.toJsonResponse(),
        type: 'CREATED',
      })
    );
  },
};

export default addLeadHandler;
