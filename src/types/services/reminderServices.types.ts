import { ISendTemplateParams } from '../../services/sendTemplate/sendTemplateParams';

export interface ICreateReminderParams {
  media_code: 'WHATSAPP_AMARTAHONDA';
  scheduled_time: Date;
  title: string | null;
  message: string | null;
  target_account: string;
  target_name: string;
  attachments: null;
  metadata: Pick<ISendTemplateParams, 'template_name' | 'components'>;
  priority: 'high';
}

export interface ICreateReminderSuccessResponse {
  success: boolean;
  data: {
    notification_code: string;
  };
}
