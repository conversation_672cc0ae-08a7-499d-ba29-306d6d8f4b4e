import { json, RequestHandler } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import ClientModel from '../../../model/ClientModel';
import moment from 'moment';
import { firestore } from 'firebase-admin';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import ChatRoom from '../../../model/ChatRoom';
import { IFirestoreMessageEntity } from '../../../types/firestore/messages/message_types.ver2';
import adminModel from '../../../model/AdminModel';
import chatRoom from '../../../model/ChatRoom';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';

interface RequestBody {
  name: string;
  phoneNumber: string;
  text?: {
    body: string;
  };
  type: 'text' | 'image';
}

const receiveMessageAmartaVipHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, RequestBody>;
} = {
  middlewares: [
    json(),
    body('name').trim().notEmpty(),
    body('phoneNumber')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62'))
      .notEmpty()
      .trim()
      .isMobilePhone('id-ID'),
    body('text.body')
      .if((input, meta) => {
        return meta.req.body.type === 'text';
      })
      .notEmpty(),

    requestValidator,
  ],
  handler: async (req, res, next) => {
    const docProject = myFirestore.collection('projects').doc('KHJVdpUgu9cNBjizxSQm');

    const clientCollections = myFirestore.collection('clients');

    const findClientWhatsapp = await clientCollections
      .where('contacts.whatsapp', '==', req.body.phoneNumber)
      .withConverter(ClientModel.converter)
      .get();

    const findClientPhoneNumber = await clientCollections
      .where('contacts.whatsapp', '==', req.body.phoneNumber)
      .withConverter(ClientModel.converter)
      .get();

    let typeContacts: 'phoneNumber' | 'whatsapp';
    let clientModel!: ClientModel;

    const now = moment();

    if (!findClientWhatsapp.empty || !findClientPhoneNumber.empty) {
      if (!findClientWhatsapp.empty) {
        typeContacts = 'whatsapp';
        findClientWhatsapp.forEach(r => {
          clientModel = r.data();
        });
      } else if (!findClientPhoneNumber.empty) {
        typeContacts = 'phoneNumber';
        findClientPhoneNumber.forEach(r => {
          clientModel = r.data();
        });
      }
    } else {
      clientModel = new ClientModel();
      clientModel.contacts = {
        phoneNumber: req.body.phoneNumber,
        whatsapp: '',
        email: '',
      };
      clientModel.created_time = firestore.Timestamp.fromDate(now.toDate());
      clientModel.profile = {
        name: req.body.name,
        phone_number: req.body.phoneNumber,
      };
      clientModel.details = {
        owner_phone_number: req.body.phoneNumber,
        guarantor_phone_number: req.body.phoneNumber,
        order_maker_phone_number: req.body.phoneNumber,
        selfie: null,
        familyRegister: null,
        idCardGuarantor: null,
        idCardGuarantorSpouse: null,
        idCardOrderMaker: null,
        idCardOwner: null,
      };
      clientModel.doc_project_origin = docProject;
    }

    const chatRoomsCollection = docProject.collection('chat_rooms');

    const findChatRoom = await chatRoomsCollection
      .where('clients', 'array-contains', clientModel.ref)
      .get();

    let chatRoomDocRef!: firestore.DocumentReference;
    let chatRoomDoc!: ChatRoom;

    if (!findChatRoom.empty) {
      findChatRoom.forEach(r => {
        chatRoomDocRef = r.ref;
      });
      const get = await chatRoomDocRef.withConverter(ChatRoom.converter).get();
      chatRoomDoc = get.data()!;
    } else {
      chatRoomDocRef = chatRoomsCollection.doc(clientModel.ref.id);
      chatRoomDoc = new ChatRoom({
        exclusive_admin: null,
        ref: chatRoomDocRef,
        blocked: false,
        blockReason: null,
        cityGroup: null,
        cityGroupUpdatedAt: null,
        cityGroupUpdatedBy: null,
        organization: null,
        organizationGroup: null,
        organizationUpdatedBy: null,
        organizationUpdatedAt: null,
        clients: [clientModel.ref],
        contacts: [req.body.phoneNumber],
        created_at: firestore.Timestamp.fromDate(now.toDate()),
        doc_department: null,
        dream_vehicle: null,
        headers: {
          title: req.body.name,
        },
        label: null,
        label_updated_at: null,
        last_inbound: firestore.Timestamp.fromDate(now.toDate()),
        last_message_type: 'IN',
        recent_chat: null,
        wait_for_answer: null,
      });
    }

    chatRoomDoc.recent_chat = {
      contact: req.body.phoneNumber,
      direction: 'IN',
      display_name: clientModel.profile.name,
      read: false,
      statuses: {
        failed: null,
        sent: null,
        read: null,
        delivered: null,
      },
      text: req.body.text?.body || '',
      timestamp: firestore.Timestamp.fromDate(now.toDate()),
      type: 'text',
      unixtime: now.unix(),
    };

    const chatsCollection = chatRoomDocRef.collection('chats');

    const firestoreMessageEntityDoc = chatsCollection.doc();
    let message: IFirestoreMessageEntity = {
      origin: {
        id: clientModel.ref.path,
        display_name: clientModel.profile.name,
        reference: clientModel.ref,
      },
      message: {
        id: firestoreMessageEntityDoc.id,
        type: req.body.type,
        unixtime: now.unix(),
        timestamp: firestore.Timestamp.fromDate(now.toDate()),
        direction: 'IN',
      },
      bindContextAndDocuments: [],
      bindDocuments: [],
      error: null,
      statuses: {
        failed: null,
        delivered: null,
        sent: null,
        read: null,
      },
      session_id: null,
    };

    const batch = myFirestore.batch();

    batch.set(clientModel.ref.withConverter(ClientModel.converter), clientModel);

    batch.set(chatRoomDocRef.withConverter(ChatRoom.converter), chatRoomDoc);

    batch.set(firestoreMessageEntityDoc, message);

    try {
      await batch.commit();
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Gagal menyimpan pesan',
        })
      );
    }

    return res.send(
      successResponse({
        type: 'FETCHED',
        data: null,
      })
    );
  },
};

export default receiveMessageAmartaVipHandler;
