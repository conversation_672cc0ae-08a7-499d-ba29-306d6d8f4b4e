import { body } from 'express-validator';
import authChecker from '../../../middlewares/authChecker';
import { HandlerTypes } from '../../../types/handler.types';
import requestValidator from '../../../middlewares/requestValidator';
import { ICreateAdRequest } from '../../../services/trimobi/trimobi.services.types';
import successResponse from '../../../schema/successResponse';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { isAxiosError } from 'axios';
import trimobiService from '../../../services/trimobi/trimobi.services';
import errorResponse from '../../../schema/errorResponse';
import amartavipService from '../../../services/amartavip/amartavip.services';
import { json } from 'express';
import { autotrimitraServices } from '../../../services/autotrimitraServices';

interface Location {
  code: string;
  name: string;
}

interface Brand {
  name: string;
  uuid: string;
}

interface Model {
  uuid: string;
  category: string;
  name: string;
}

interface Image {
  uuid: string;
  url: string;
}

interface AdListingRequest {
  vehicleType: string;
  planCode: string;
  fullName: string;
  phoneNumber: string;
  province: Location;
  city: Location;
  district: Location;
  subDistrict: Location;
  address: string;
  postalCode: string;
  brand: Brand;
  model: Model;
  variant: string;
  licensePlate: string;
  fuelType: string;
  transmission: string;
  engineCapacity: string;
  year: string;
  mileage: string;
  completeVehicleDocuments: boolean;
  activeVehicleDocuments: boolean;
  ownerNameMatchDocuments: boolean;
  allowBuyerPriceOffer: boolean;
  title: string;
  description: string;
  price: number;
  images: Image[];
}

function generateTrimobiUrl(vehicleUuid: string, vehicleType: string) {
  const vehicleCategory = vehicleType === 'car' ? '4w' : '2w';
  return `https://tri.mobi/${vehicleCategory}/used/vehicle/${vehicleUuid}`;
}

const trimobiAddNewListingHandler: HandlerTypes<any, ResponseSchema, AdListingRequest> = {
  middlewares: [
    authChecker,
    json(),
    function (req, res, next) {
      amartavipService.setEmail(res.locals.account.email);
      next();
    },
    body('planCode').notEmpty().withMessage('Kode paket wajib diisi'),
    body('title').notEmpty().withMessage('Judul iklan wajib diisi'),
    body('vehicleType').notEmpty().withMessage('Jenis kendaraan wajib diisi'),
    body('fullName').notEmpty().withMessage('Nama lengkap wajib diisi'),
    body('phoneNumber')
      .notEmpty()
      .isMobilePhone('id-ID')
      .withMessage('Nomor telepon Indonesia tidak valid'),
    body('province').notEmpty().isObject().withMessage('Data provinsi wajib diisi'),
    body('province.code').notEmpty().withMessage('Kode provinsi wajib diisi'),
    body('province.name').notEmpty().withMessage('Nama provinsi wajib diisi'),
    body('city').notEmpty().isObject().withMessage('Data kota wajib diisi'),
    body('city.code').notEmpty().withMessage('Kode kota wajib diisi'),
    body('city.name').notEmpty().withMessage('Nama kota wajib diisi'),
    body('district').notEmpty().isObject().withMessage('Data kecamatan wajib diisi'),
    body('district.code').notEmpty().withMessage('Kode kecamatan wajib diisi'),
    body('district.name').notEmpty().withMessage('Nama kecamatan wajib diisi'),
    body('subDistrict').notEmpty().isObject().withMessage('Data kelurahan wajib diisi'),
    body('subDistrict.code').notEmpty().withMessage('Kode kelurahan wajib diisi'),
    body('subDistrict.name').notEmpty().withMessage('Nama kelurahan wajib diisi'),
    body('address').notEmpty().withMessage('Alamat lengkap wajib diisi'),
    body('postalCode').notEmpty().isPostalCode('ID').withMessage('Kode pos Indonesia tidak valid'),
    body('brand').notEmpty().isObject().withMessage('Data merek wajib diisi'),
    body('brand.name').notEmpty().withMessage('Nama merek wajib diisi'),
    body('brand.uuid').notEmpty().withMessage('UUID merek wajib diisi'),
    body('model').notEmpty().isObject().withMessage('Data model wajib diisi'),
    body('model.uuid').notEmpty().withMessage('UUID model wajib diisi'),
    body('model.category').notEmpty().withMessage('Kategori model wajib diisi'),
    body('model.name').notEmpty().withMessage('Nama model wajib diisi'),
    body('variant').notEmpty().withMessage('Varian kendaraan wajib diisi'),
    body('licensePlate').notEmpty().withMessage('Nomor polisi wajib diisi'),
    body('fuelType').notEmpty().withMessage('Jenis bahan bakar wajib diisi'),
    body('transmission').notEmpty().withMessage('Transmisi wajib diisi'),
    body('year')
      .notEmpty()
      .isInt({ min: 1900, max: new Date().getFullYear() })
      .withMessage('Tahun kendaraan tidak valid'),
    body('mileage').notEmpty().isInt({ min: 0 }).withMessage('Kilometer harus angka positif'),
    body('completeVehicleDocuments')
      .isBoolean()
      .withMessage('Status dokumen kendaraan lengkap tidak valid'),
    body('activeVehicleDocuments')
      .isBoolean()
      .withMessage('Status dokumen aktif kendaraan tidak valid'),
    body('ownerNameMatchDocuments')
      .isBoolean()
      .withMessage('Status kecocokan nama pemilik tidak valid'),
    body('allowBuyerPriceOffer').isBoolean().withMessage('Status penawaran harga tidak valid'),
    body('description').notEmpty().withMessage('Deskripsi iklan wajib diisi'),
    body('price').notEmpty().isInt({ min: 0 }).withMessage('Harga harus angka positif'),
    body('images').isArray({ min: 1 }).withMessage('Minimal satu gambar diperlukan'),
    body('images.*.uuid').notEmpty().withMessage('UUID gambar wajib diisi'),
    body('images.*.url').notEmpty().isURL().withMessage('URL gambar tidak valid'),
    requestValidator,
  ],
  handler: async (req, res) => {
    const { body } = req;

    const getModel = await autotrimitraServices.getVehicleModelBrand({
      modelUuid: body.model.uuid,
    });

    if (getModel.data.data.length === 0) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Model tidak ditemukan',
        })
      );
    }

    const model = getModel.data.data[0];

    const payload: ICreateAdRequest = {
      plan_code: body.planCode,
      company: body.vehicleType === 'car' ? 'trimobi' : 'trimotor',
      source: 'trimobi',
      category: body.vehicleType,
      caption: body.title,
      description: body.description,
      price: body.price,
      area: body.province.name,
      city_group: body.city.name,
      name: body.fullName,
      phone_number: body.phoneNumber,
      listing_option: 'GLOBAL',
      permitted_make_offer: body.allowBuyerPriceOffer,
      vehicle_condition: {
        year: body.year,
        mileage: body.mileage,
      },
      vehicle_certificate: {
        year: body.year,
        license_plate: body.licensePlate,
        ownership: 1,
        ownership_certificate: body.activeVehicleDocuments,
        registration_certificate: body.ownerNameMatchDocuments,
        active_certificate: body.ownerNameMatchDocuments,
        owner_as_certificate: body.ownerNameMatchDocuments,
      },
      vehicle_data: {
        class: body.vehicleType,
        brand_uuid: body.brand.uuid,
        brand_name: body.brand.name,
        model_uuid: body.model.uuid,
        model_name: body.model.name,
        model_other_name: '',
        variant_uuid: '',
        variant_code: '',
        variant_name: body.variant,
        color_code: '',
        color_name: '',
        image_url: '',
        fuel: body.fuelType,
        seat: '',
        transmission: body.transmission,
        engine_capacity: model.engine_capacity || '0',
      },
      id_card: {
        full_name: body.fullName,
        id_card_number: '',
        birth_date: '',
        birth_place: '',
        sex: '',
        marital_status: '',
        occupation: '',
        occupation_code: '',
        full_address: body.address,
        id_card_image: '',
      },
      address: {
        full_address: body.address,
        province_name: body.province.name,
        province_code: body.province.code,
        city_name: body.city.name,
        city_code: body.city.code,
        district_name: body.district.name,
        district_code: body.district.code,
        sub_district_name: body.subDistrict.name,
        sub_district_code: body.subDistrict.code,
        zip_code: body.postalCode,
      },
      vehicle_address: {
        full_address: body.address,
        province_name: body.province.name,
        province_code: body.province.code,
        city_name: body.city.name,
        city_code: body.city.code,
        district_name: body.district.name,
        district_code: body.district.code,
        sub_district_name: body.subDistrict.name,
        sub_district_code: body.subDistrict.code,
        zip_code: body.postalCode,
      },
      vehicle_images: body.images.map(image => ({
        image_url: image.url,
        caption: '',
      })),
    };

    let vehicleUuid: string = '';
    try {
      const response = await trimobiService.createAd(payload);
      console.log(response);
      vehicleUuid = response.meta.used_vehicle_uid;
    } catch (error) {
      let errorMessages = 'Error creating ad listing';
      if (isAxiosError(error)) {
        errorMessages = error.response?.data?.error?.message;
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: `Error creating ad listing: ${errorMessages}`,
          })
        );
      }
    }

    res.status(200).json(
      successResponse({
        data: {
          trimobiUrl: generateTrimobiUrl(vehicleUuid, body.vehicleType),
        },
        type: 'CREATED',
      })
    );
  },
};
export default trimobiAddNewListingHandler;
