import { Request, Response } from 'express';
import multer from 'multer';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import { File } from '@google-cloud/storage';
import { ISelfieFields } from '../../../types/services/profile_service_types';
import { profileService } from '../../../services/profileService';
import ClientModel from '../../../model/ClientModel';
import { ISelfie } from '../../../types/firestore/slefie-document-types';
import moment from 'moment';

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export interface SetSelfieReqBody {
  clientRef: string;
}

export default class SetSelfieV2Handler {
  public static middlewares = [
    formDataParse.single('image'),
    body('clientRef').notEmpty(),
    requestValidator,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, SetSelfieReqBody>,
    res: Response<ResponseSchema>
  ) {
    const globalVar: {
      file: File | null;
      currentSelfieData: ISelfie | null;
    } = {
      file: null,
      currentSelfieData: null,
    };

    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.withConverter(ClientModel.converter).get();
    const clientData = getClient.data()!;
    globalVar.currentSelfieData = clientData.details?.selfie || null;

    if (req.file?.buffer) {
      globalVar.file = bucketTrimitraIdeal.file(`client-documents/${clientDocRef.id}/selfie.jpg`);
      await globalVar.file.save(req.file.buffer, {
        public: true,
      });
    }

    const now = moment();

    const selfieData: ISelfie = {
      selfieImage: globalVar.file?.publicUrl() || globalVar.currentSelfieData?.selfieImage || '',
      updatedAt: now.toDate(),
    };

    try {
      await clientDocRef.update({
        'details.selfie': selfieData,
      });
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }

    res.send(
      successResponse({
        type: 'UPDATED',
        data: selfieData,
      })
    );
  }
}
