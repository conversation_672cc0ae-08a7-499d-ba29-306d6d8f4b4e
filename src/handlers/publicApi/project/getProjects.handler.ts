import successResponse from '../../../schema/successResponse';
import { myFirestore } from '../../../services/firebaseAdmin';
import { HandlerTypes } from '../../../types/handler.types';

const publicApiGetProjectHandler: HandlerTypes = {
  middlewares: [],
  handler: async (req, res) => {
    const projectCollection = myFirestore.collection('projects');
    const get = await projectCollection.where('active', '==', true).get();

    let datas: any[] = [];

    get.forEach(result => {
      datas.push({
        ...result.data(),
        projectId: result.id,
      });
    });

    datas = datas.map(d => {
      return {
        name: d.legal_name,
        phoneNumber: d.phone_number,
        provider: d.provider,
        projectId: d.projectId,
        brands: d.brands,
        group: d.group,
      };
    });

    res.json(
      successResponse({
        type: 'FETCHED',
        data: datas,
      })
    );
  },
};

export default publicApiGetProjectHandler;
