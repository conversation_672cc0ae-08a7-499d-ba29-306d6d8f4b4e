import { myFirestore } from '../../../src/services/firebaseAdmin';
import FreeLeadsModel from '../../../src/model/FreeLeadsModel';
import sendTemplate from '../../../src/services/sendTemplate/sendTemplate';

const otolmsSendMessage = async () => {
  const logPath =
    '/leads_logs/add_free_leads/otocom_upload_file_xls/08d3afcf-df33-4149-b99b-29a74f2397eb';
  const logDoc = myFirestore.doc(logPath);

  let phoneNumbers: string[] = [];

  const getLog = await logDoc.get();
  phoneNumbers = getLog.get('allLeadsPhoneNumbersInFile');

  console.log(phoneNumbers);

  for (const phoneNumber of phoneNumbers) {
    const docName = `amartahonda-${phoneNumber}`;
    const getFreeLead = await myFirestore
      .collection('free_leads')
      .doc(docName)
      .withConverter(FreeLeadsModel.converter)
      .get();

    const data = getFreeLead.data()!;

    await sendTemplate
      .sendTemplateMetaV2({
        projectId: 'WLdKug7hau0MbRzKcnqg',
        bindDocuments: [
          {
            path: data.ref.path,
            context: 'add_leads',
          },
        ],
        template_name: 'leads1',
        components: [
          {
            type: 'body',
            parameters: [
              {
                type: 'text',
                text: data.firstName.toUpperCase(),
              },
              {
                type: 'text',
                text: 'OTOLMS',
              },
              {
                type: 'text',
                text: data.vehicleOptions[0].model.name,
              },
            ],
          },
        ],
        contactName: data.firstName.toUpperCase(),
        target: phoneNumber,
        area: data.area || undefined,
        vehicle: {
          model_name: data.vehicleOptions[0].model.name || '',
          variant_name: data.vehicleOptions[0].variant.name || '',
          variant_code: data.vehicleOptions[0].variant.code || '',
          color_code: data.vehicleOptions[0].color.code || '',
          color_name: data.vehicleOptions[0].color.name || '',
          year: '',
        },
      })
      .then()
      .catch(reason => {
        console.log('FAILED_SEND_TEMPLATE_LEADS', phoneNumber, reason);
      });
  }
};

otolmsSendMessage();
