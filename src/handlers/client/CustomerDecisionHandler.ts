import { json, <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import ClientModel from '../../model/ClientModel';
import moment from 'moment';
import { firestore } from 'firebase-admin';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import reminderService from '../../services/ReminderService';
import { AxiosError } from 'axios';
import authChecker from '../../middlewares/authChecker';
import { IFirestoreAdminEntity } from '../../types/firestore/messages/message_types';

interface ReqBody {
  clientRefPath: string;
  finalResult: string;
  followUp: {
    date: string;
    time: string;
    message: {
      template: string;
      variables: string[];
    };
  };
}

const customerDecisionHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, ReqBody, any, { account: IFirestoreAdminEntity }>;
} = {
  middlewares: [
    json(),
    authChecker,
    body('clientRefPath').notEmpty(),
    body('finalResult').notEmpty(),
    body('followUp.date')
      .if((input, meta) => {
        return meta.req.body.finalResult === 'needToFollowUp';
      })
      .notEmpty()
      .withMessage('Tanggal untuk mengirim template diperlukan')
      .custom(input => {
        return moment(input, 'YYYY-MM-DD').isValid();
      })
      .withMessage('Tanggal tidak valid'),
    body('followUp.time')
      .if((input, meta) => {
        return meta.req.body.finalResult === 'needToFollowUp';
      })
      .notEmpty()
      .withMessage('Jam untuk mengirim template diperlukan'),
    body('followUp.message.template')
      .if((input, meta) => {
        return meta.req.body.finalResult === 'needToFollowUp';
      })
      .notEmpty()
      .withMessage('Template pesan diperlukan'),
    requestValidator,
  ],
  handler: async (req, res) => {
    const getClient = await myFirestore
      .doc(req.body.clientRefPath)
      .withConverter(ClientModel.converter)
      .get();

    const now = moment();

    const client = getClient.data()!;

    client.finalDecision = {
      result: req.body.finalResult,
      followUp: null,
      updatedAt: firestore.Timestamp.fromDate(now.toDate()),
      createdBy: {
        uid: res.locals.account.ref?.id || '',
        email: res.locals.account.email,
      },
    };

    if (req.body.followUp && req.body.finalResult === 'needToFollowUp') {
      const dateTime = moment(
        `${req.body.followUp.date} ${req.body.followUp.time}`,
        'YYYY-MM-DD HH:mm'
      );
      client.finalDecision.followUp = {
        dateTime: firestore.Timestamp.fromDate(dateTime.toDate()),
        message: {
          template: req.body.followUp?.message.template || '',
          variables: req.body.followUp?.message.variables || [],
        },
        followUpCode: null,
      };
    }

    if (req.body.finalResult === 'needToFollowUp') {
      try {
        const createReminder = await reminderService.createReminder({
          media_code: 'WHATSAPP_AMARTAHONDA',
          scheduled_time: client.finalDecision.followUp!.dateTime.toDate(),
          title: null,
          message: null,
          target_account: client.contacts.whatsapp,
          target_name: client.profile.name,
          attachments: null,
          metadata: {
            template_name: client.finalDecision.followUp?.message.template || '',
            components: [
              {
                type: 'body',
                parameters: client.finalDecision.followUp!.message.variables.map(value => {
                  return {
                    type: 'text',
                    text: value,
                  };
                }),
              },
            ],
          },
          priority: 'high',
        });

        client.finalDecision.followUp!.followUpCode = createReminder.data.data.notification_code;
      } catch (e) {
        const error = e as AxiosError;
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: 'Terjadi kesalahan ketika membuat reminder',
            data: error.response?.data,
          })
        );
      }
    }

    try {
      await client.ref.withConverter(ClientModel.converter).set(client);
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi kesalahan',
        })
      );
    }

    return res.send(
      successResponse({
        data: null,
      })
    );
  },
};

export default customerDecisionHandler;
