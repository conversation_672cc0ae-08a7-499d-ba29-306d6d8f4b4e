import { query } from 'express-validator';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { HandlerTypes } from '../../../types/handler.types';
import triForce<PERSON>uth<PERSON>hecker from '../../../middlewares/triforceAuthChecker';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';

interface ResponseData {
  agentCode: string;
  agentName: string;
  phoneNumber: string;
  fullName: string;
  province: {
    code: string;
    name: string;
  };
  city: {
    code: string;
    name: string;
  };
  vehicle: {
    brand: {
      name: string;
    };
    model: {
      name: string;
    };
    variant: {
      code: string;
      name: string;
    } | null;
    color: {
      code: string;
      name: string;
    } | null;
  } | null;
  notes: string | null;
  refCode: string;
  createdAt: FirebaseFirestore.Timestamp;
  updatedAt: FirebaseFirestore.Timestamp;
  organization: string;
  source: string;
  isLeadsSent: boolean;
}

const getAgentRefCodeHandler: HandlerTypes<any, ResponseSchema, any, { refCode: string }> = {
  middlewares: [
    triForceAuthChecker,
    query('refCode')
      .notEmpty()
      .withMessage('Kode referensi wajib diisi')
      .isString()
      .withMessage('Kode referensi harus berupa teks'),
    requestValidator,
  ],
  handler: async (req, res) => {
    const agentRefCodeCollections = myFirestore.collection('agent_ref_codes');
    const { refCode } = req.query;

    const docRef = await agentRefCodeCollections.doc(refCode as string).get();

    if (!docRef.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'NOT_FOUND',
          messages: 'Data tidak ditemukan',
        })
      );
    }

    const data = docRef.data() as ResponseData;

    res.send(
      successResponse({
        type: 'FETCHED',
        data: data,
      })
    );
  },
};

export default getAgentRefCodeHandler;
