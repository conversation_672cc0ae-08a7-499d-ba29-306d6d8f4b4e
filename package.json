{"name": "autotrimitra-backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "dev": "tsx watch -r dotenv/config index.ts", "start": "tsx -r dotenv/config ./build/index.js", "build:zip": "node build-zip.js"}, "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.18.0", "@jest/globals": "^29.7.0", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/qrcode": "^1.5.5", "@types/sanitize-html": "^2.13.0", "@types/stopword": "^2.0.3", "@types/uuid": "^9.0.7", "csv-parser": "^3.0.0", "eslint": "^9.18.0", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "3.5.3", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "typescript-eslint": "^8.21.0"}, "dependencies": {"@fastify/busboy": "^3.1.1", "@google-cloud/bigquery": "^7.3.0", "@google-cloud/storage": "^7.7.0", "@types/node": "^22.5.1", "axios": "^1.6.5", "collect.js": "^4.36.1", "compression": "^1.7.4", "cors": "^2.8.5", "dayjs": "^1.11.12", "dotenv": "^16.3.1", "express": "^4.18.2", "express-validator": "^7.0.1", "firebase-admin": "^12.0.0", "mime-types": "^2.1.35", "moment": "^2.29.4", "multer": "github:emadalam/multer", "qrcode": "^1.5.4", "sanitize-html": "^2.14.0", "stopword": "^3.1.1", "string-similarity-js": "^2.1.4", "typescript": "^5.5.4", "uuid": "^9.0.1"}}