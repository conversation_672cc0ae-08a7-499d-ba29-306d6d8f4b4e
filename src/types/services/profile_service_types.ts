import { EMaritalStatus } from '../client/document/marital-status-types';
import { EFamilyRelation } from '../firestore/family_registration_document_types';

export interface IOwnedVehicle {
  Brand: {
    Uuid: string;
    Name: string;
  };
  Model: {
    Uuid: string;
    Name: string;
  };
  Variant: {
    Code: string;
    Uuid: string;
    Name: string;
  };
  Year: string;
}

export interface CommonRegionFields {
  Code: string;
  Name: string;
}

export interface FamilyMember {
  IdCardNumber: string;
  FullName: string;
  FamilyRelation: EFamilyRelation;
  MaritalStatus: EMaritalStatus;
}

export interface IdCardFields {
  IdCardNumber: string;
  FullName: string;
  BirthMother: string;
  PlaceOfBirth: string;
  DateOfBirth: Date;
  Occupation: string;
  MaritalStatus: string;
  LastEducation: string;
  FullAddress: string;
  ZipCode: string;
  Province: CommonRegionFields;
  City: CommonRegionFields;
  District: CommonRegionFields;
  SubDistrict: CommonRegionFields;
  IdCardImage?: string;
  Neighbourhood: string;
  Hamlet: string;

  DomicileFullAddress: string;
  DomicileZipCode: string;
  DomicileProvince: CommonRegionFields | null;
  DomicileCity: CommonRegionFields | null;
  DomicileDistrict: CommonRegionFields | null;
  DomicileSubDistrict: CommonRegionFields | null;
  DomicileNeighbourhood: string;
  DomicileHamlet: string;
}
export type IdCardSpouseGuarantorFields = Omit<
  IdCardFields,
  | 'DomicileFullAddress'
  | 'DomicileCity'
  | 'DomicileDistrict'
  | 'DomicileProvince'
  | 'DomicileSubDistrict'
  | 'DomicileHamlet'
  | 'DomicileNeighbourhood'
  | 'DomicileZipCode'
>;

export interface IFamilyRegisterFields {
  FamilyRegisterNumber: string;
  FullAddress: string;
  ZipCode: string;

  Province: CommonRegionFields;
  City: CommonRegionFields;
  District: CommonRegionFields;
  SubDistrict: CommonRegionFields;

  FamilyRegisterImage?: string | null;

  Members: FamilyMember[];
}

export interface IBusinessDocumentFields {
  DocumentTypes: string;
  DocumentNumber: string;
  ExpiredDate: string | Date | null;
  BusinessDocumentImage?: string;
}

export interface IIncomeDocumentFields {
  IncomeType: string;
  DocumentType: string;
  IncomeAmount: string;
  AdditionalInfo: string;
  BankName: string;
  BankAccountNumber: string;
  ImageIncomeDocument?: string;
}

export interface IPlaceToStayFields {
  FullAddress: string;
  Latitude: string;
  Longitude: string;
  PlaceToStayImage?: string;
  PlaceToStayStatus: string;
}

export interface IPlaceOfBusinessFields {
  FullAddress: string;
  Latitude: string;
  Longitude: string;
  BusinessSize: string;
  BusinessType: string;
  PlaceOfBusinessImage?: string;
}

export interface ISelfieFields {
  FullAddress: string | null;
  Latitude: string | null;
  Longitude: string | null;
  Description: string | null;
  SelfieImage?: string;
}

export interface IOtherDocumentFields {
  DocumentName: string;
  ExpiredDate: Date | string | null;
  Notes: string;
  ImageOtherDocument?: string;
}

export interface IDocumentRequestPayload {
  idCard?: IdCardFields | null;
  idCardSpouse?: IdCardSpouseGuarantorFields | null;
  familyRegister?: IFamilyRegisterFields | null;
  incomeDocument?: IIncomeDocumentFields | null;
  businessDocument?: IBusinessDocumentFields | null;
  placeToStay?: IPlaceToStayFields | null;
  placeOfBusiness?: IPlaceOfBusinessFields | null;
  selfie: ISelfieFields | null;
  otherDocument: IOtherDocumentFields | null;
}

export interface IProfileRequestPayload {
  customerName: string;
  phone: string;
  cityGroup?: string | null;
}

export interface IOtherDetailRequestPayload {
  ownedVehicles: IOwnedVehicle[] | null;
}

export type TProfileRequestPayload = IProfileRequestPayload &
  Partial<IDocumentRequestPayload & IOtherDetailRequestPayload>;

export interface IUntouchableResponse {
  TrackingId: string;
  Source: string;
  SK: string;
  PK: string;
  CreatedAt: string;
  UpdatedAt: string;
  Status: string;
}

export interface IDocumentsResponse {
  IdCard: IdCardFields;
  IdCardSpouse?: IdCardFields | null;
  FamilyRegister?: IFamilyRegisterFields | null;
  IncomeDocument?: IIncomeDocumentFields | null;
  BusinessDocument?: IBusinessDocumentFields | null;
  PlaceToStay: IPlaceToStayFields | null;
  PlaceOfBusiness: IPlaceOfBusinessFields | null;
  Selfie: ISelfieFields | null;
  OtherDocument: IOtherDocumentFields | null;
}

export interface IProfileResponse {
  Phone: string;
  CustomerName: string;
  CityGroup?: string;
  OwnedVehicles?: IOwnedVehicle[];
}

export type TProfileResponse = IProfileResponse &
  Partial<IDocumentsResponse> &
  IUntouchableResponse;
