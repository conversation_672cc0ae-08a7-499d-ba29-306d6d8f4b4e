import { j<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { ResponseSchema } from '../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../middlewares/requestValidator';
import { BigQueryTimestamp } from '@google-cloud/bigquery';
import { v4 } from 'uuid';
import { bigQuery, priceListTable } from '../services/bigQueryService';
import successResponse from '../schema/successResponse';
import errorResponse from '../schema/errorResponse';

interface BigQueryRecord {
  down_payment?: number | null;
  tenor?: number | null;
  installment?: number | null;
  discount_down_payment?: number | null;
  discount_tenor?: number | null;
  discount_installment?: number | null;
}

interface ReqBody {
  event: string;
  phone_number?: string | null;
  name?: string | null;
  city_group?: string | null;
  vehicle?: {
    model_name?: string | null;
    variant_name?: string | null;
    variant_code?: string | null;
    variant_color_code?: string | null;
    variant_color_name?: string | null;
  } | null;
  discount_promo?: number | null;
  otr?: number | null;
  pricelists: BigQueryRecord[];
  admin_id?: string | null;
  credit?: {
    offer_code?: string | null;
    down_payment?: number | null;
    discount_down_payment?: number | null;
    tenor?: number | null;
    discount_tenor?: number | null;
    installment?: number | null;
    discount_installment?: number | null;
  } | null;
}

interface BigQueryData {
  event: string;
  uuid?: string | null;
  phone_number?: string | null;
  name?: string | null;
  city_group?: string | null;
  vehicle?: {
    model_name?: string | null;
    variant_name?: string | null;
    variant_code?: string | null;
    variant_color_code?: string | null;
    variant_color_name?: string | null;
  } | null;
  discount_promo?: number | null;
  otr?: number | null;
  pricelists?: BigQueryRecord[] | null;
  admin_id?: string | null;
  created_at?: BigQueryTimestamp; // assuming TIMESTAMP is represented as string
  credit?: {
    offer_code?: string | null;
    down_payment?: number | null;
    discount_down_payment?: number | null;
    tenor?: number | null;
    discount_tenor?: number | null;
    installment?: number | null;
    discount_installment?: number | null;
  } | null;
}

const insertLogPricelistToBqHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    json(),
    body('event').notEmpty().isString(),
    body('phone_number').notEmpty().isString(),
    body('name').notEmpty().isString(),
    body('city_group').notEmpty().isString(),
    body('vehicle').notEmpty().isObject(),
    body('vehicle.model_name').notEmpty().isString(),
    body('vehicle.variant_name').notEmpty().isString(),
    body('vehicle.variant_code').notEmpty().isString(),
    body('vehicle.variant_color_code').optional({ values: 'falsy' }).isString(),
    body('vehicle.variant_color_name').optional({ values: 'falsy' }).isString(),
    body('discount_promo').optional({ values: 'falsy' }).isFloat(),
    body('otr').notEmpty().isFloat(),
    body('pricelists').isArray(),
    body('pricelists.*.down_payment').notEmpty().isFloat(),
    body('pricelists.*.tenor').notEmpty().isInt(),
    body('pricelists.*.installment').notEmpty().isFloat(),
    body('pricelists.*.discount_down_payment').optional({ values: 'falsy' }).isFloat(),
    body('pricelists.*.discount_tenor').optional({ values: 'falsy' }).isInt(),
    body('pricelists.*.discount_installment').optional({ values: 'falsy' }).isFloat(),
    body('admin_id').optional({ values: 'falsy' }).isString(),
    body('credit').optional({ values: 'falsy' }).isObject(),
    body('credit.offer_code').optional({ values: 'falsy' }).isString(),
    body('credit.down_payment').optional({ values: 'falsy' }).isFloat(),
    body('credit.discount_down_payment').optional({ values: 'falsy' }).isFloat(),
    body('credit.tenor').optional({ values: 'falsy' }).isInt(),
    body('credit.discount_tenor').optional({ values: 'falsy' }).isInt(),
    body('credit.installment').optional({ values: 'falsy' }).isFloat(),
    body('credit.discount_installment').optional({ values: 'falsy' }).isFloat(),

    requestValidator,
  ],
  handler: async (req, res) => {
    const dataBigquery: BigQueryData[] = [];

    const date = new Date();

    for (const pricelist of req.body.pricelists) {
      const uuidV4 = v4();
      const data: BigQueryData = {
        event: req.body.event,
        uuid: uuidV4,
        phone_number: req.body.phone_number,
        name: req.body.name,
        city_group: req.body.city_group,
        vehicle: {
          model_name: req.body.vehicle?.model_name?.toUpperCase(),
          variant_name: req.body.vehicle?.variant_name?.toUpperCase(),
          variant_code: req.body.vehicle?.variant_code?.toUpperCase(),
          variant_color_code: req.body.vehicle?.variant_color_code?.toUpperCase() || null,
          variant_color_name: req.body.vehicle?.variant_color_name?.toUpperCase() || null,
        },
        discount_promo: req.body.discount_promo || null,
        otr: req.body.otr || 0,
        pricelists: [
          {
            down_payment: pricelist.down_payment,
            tenor: pricelist.tenor,
            installment: pricelist.installment,
            discount_down_payment: null,
            discount_installment: null,
            discount_tenor: null,
          },
        ],
        admin_id: req.body.admin_id,
        created_at: bigQuery.timestamp(date),
        credit: {
          offer_code: req.body.credit?.offer_code,
          down_payment: req.body.credit?.down_payment || 0,
          discount_down_payment: req.body.credit?.discount_down_payment || 0,
          tenor: req.body.credit?.tenor || 0,
          discount_tenor: req.body.credit?.discount_tenor || 0,
          installment: req.body.credit?.installment || 0,
          discount_installment: req.body.credit?.discount_installment || 0,
        },
      };

      dataBigquery.push(data);
    }

    try {
      await priceListTable.insert(dataBigquery);
    } catch (e) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e?.toString(),
        })
      );
    }

    res.send(
      successResponse({
        data: dataBigquery,
      })
    );
  },
};

export default insertLogPricelistToBqHandler;
