import { firestore } from 'firebase-admin';

export type IConversationFlowStartAt = 'leadsCredit' | 'referralSourceId' | 'newCustomer';
export type IConversationFlowMessageType = 'text' | 'priceList' | 'button' | 'internalTemplate';
export type IConversationFlowMessageOnCustomerReplied = 'saveName';

export interface IConversationFlow {
  startAt: IConversationFlowStartAt;
  referralSourceId: {
    dealCode: string | null;
    sourceId: string;
  };
  messages: IConversationFlowMessage[];
  active: boolean;
}

export interface IConversationFlowMessage {
  parent: string | null;
  uuid: string;
  type: IConversationFlowMessageType | null;
  text: {
    body: string;
  };
  priceList: {
    text: string;
  };
  button: {
    text: string;
    buttons: { id: string; text: string }[];
  };
  internalTemplate: {
    templateId: string;
  };
  mediaTrimitraArt: {
    artCode: string;
    text: string;
  };
  onReplied: IConversationFlowMessageOnCustomerReplied | null;
}

export interface IConversationFlowFirestoreDoc<TIMESTAMP = firestore.Timestamp>
  extends IConversationFlow {
  id: string;
  dealCode?: {
    cityGroup: string;
    modelName: string;
    endPeriod: TIMESTAMP;
  } | null;
  updatedAt: TIMESTAMP;
  createdAt: TIMESTAMP;
  createdBy: string;
}
