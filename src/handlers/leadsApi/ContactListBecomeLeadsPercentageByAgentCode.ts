import { Request<PERSON>and<PERSON> } from 'express';
import { param, query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import { getAgent } from '../../services/agent/fetchAgent';
import { IAgentContactTypes } from '../../types/onboarding/agentContact.types';
import successResponse from '../../schema/successResponse';
import LeadsModel from '../../model/LeadsModel';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import errorResponse from '../../schema/errorResponse';

interface ReqQuery {
  contactsOnly?: boolean;
}

const contactListBecomeLeadsPercentageByAgentCode: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, any, ReqQuery>;
} = {
  middlewares: [
    query('contactsOnly').optional({ values: 'falsy' }).isBoolean().toBoolean(),
    param('agentCode').notEmpty().trim(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const collection = myFirestore.collection('agent_contacts');
    const myLeadsCollection = myFirestore.collection('leads');

    const agent = await getAgent(req.params.agentCode);

    if (!agent) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Kode agen tidak ditemukan',
        })
      );
    }

    const agentPhoneNumber = agent.phoneNumbers[0];

    const get = await collection.where('agentPhoneNumber', '==', agentPhoneNumber).get();

    const contacts: IAgentContactTypes[] = [];
    const contactsBecomeLeads: LeadsModel[] = [];

    get.forEach(result => {
      const data = result.data()! as any;
      contacts.push(data);
    });
    if (!req.query.contactsOnly) {
      for (const contact of contacts) {
        const get = await myLeadsCollection
          .where('agentCode', '==', req.params.agentCode)
          .where('phoneNumber', '==', contact.phoneNumber)
          .withConverter(LeadsModel.converter)
          .limit(1)
          .get();
        if (!get.empty) {
          get.forEach(result => {
            const data = result.data();
            contactsBecomeLeads.push(data);
          });
        }
      }
    }
    let count = contactsBecomeLeads.length;
    const percentage = (count / contacts.length) * 100;

    let response: any = {};

    if (req.query.contactsOnly) {
      response = {
        agentCode: req.params.agentCode,
        contactsExported: contacts.length,
      };
    } else {
      response = {
        agentCode: req.params.agentCode,
        contactsExported: contacts.length,
        contactsBecomeLeads: count,
        contactsBecomeLeadsPercentage: percentage,
        leads: contactsBecomeLeads.map(value => {
          return {
            firstName: value.firstName,
            lastName: value.lastName,
            phoneNumber: value.phoneNumber,
            organization: value.organization,
            createdAt: value.createdAt,
          };
        }),
      };
    }

    res.send(
      successResponse({
        data: response,
        type: 'FETCHED',
      })
    );
  },
};

export default contactListBecomeLeadsPercentageByAgentCode;
