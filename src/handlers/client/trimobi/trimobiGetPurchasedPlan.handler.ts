import { isAxiosError } from 'axios';
import authChecker from '../../../middlewares/authChecker';
import successResponse from '../../../schema/successResponse';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import amartavipService from '../../../services/amartavip/amartavip.services';
import { HandlerTypes } from '../../../types/handler.types';
import errorResponse from '../../../schema/errorResponse';
import AdminModel from '../../../model/AdminModel';

const trimobiGetPurchasedPlanHandler: HandlerTypes<
  any,
  ResponseSchema,
  any,
  any,
  {
    account: AdminModel;
  }
> = {
  middlewares: [
    authChecker,
    function (req, res, next) {
      amartavipService.setEmail(res.locals.account.email);
      next();
    },
  ],
  handler: async (req, res) => {
    try {
      const response = await amartavipService.getPurchasedPlan();
      return res.status(200).json(
        successResponse({
          type: 'FETCHED',
          data: response.data,
        })
      );
    } catch (error) {
      let errorMessage = 'Terjadi kesalahan saat mengambil data paket yang dibeli';
      if (isAxiosError(error)) {
        errorMessage = 'Error API get purchased plan: ' + error.response?.data.message;
      }
      return res.status(500).json(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: errorMessage,
        })
      );
    }
  },
};

export default trimobiGetPurchasedPlanHandler;
