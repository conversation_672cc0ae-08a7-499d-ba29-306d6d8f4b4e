import axios from 'axios';

interface DefaultAddress {
  city_code: string;
  city_name: string;
  province_code: string;
  province_name: string;
}

interface Variant {
  code: string;
  custom_thumb_image: string;
  custom_image: string;
  variant_name: string;
  variant_color_code: string;
  variant_code: string;
  variant_color_name: string;
}

interface Vehicle {
  variant_custom: Variant[];
  variant_alternative_color: {
    name: string;
    code: string;
  };
  brand_name: string;
  model_name: string;
  models_name: string[];
  brand_uuid: string;
}

interface CreditOption {
  dp_amount: string;
  tenor: string[];
  installment_amount: string;
  finco_name: string;
  finco_branch: string;
  finco_code: string;
  otr: number;
}

interface DataEntryOption {
  name: string;
  show: boolean;
  require: boolean;
  type: string;
  validation: boolean;
}

interface PromoCode {
  promo_codes: string[];
  total_promo_discount: number;
}

interface EventPoint {
  name: string;
  key: string;
  point: number;
}

export interface ResponseGetDealCode {
  success: boolean;
  data: {
    company: string;
    deal_code: string;
    url_image: string;
    url_thumb_image: string;
    start_period: string;
    end_period: string;
    active: boolean;
    show: boolean;
    caption: string;
    notes: string;
    purchase_method: string;
    area: string[];
    default_address: DefaultAddress;
    custom_price: number;
    custom_price_range_up: number;
    custom_price_range_down: number;
    take_vehicle_in_dealer: string;
    agent_code: string;
    promo_codes: string[];
    total_promo_discount: number;
    vehicle: Vehicle;
    credit: CreditOption[];
    data_entry_option: DataEntryOption[];
    image_campaign_custom: string[];
    event_points: EventPoint[];
    default_finco_id: string;
    default_dealer_code: string;
    default_dealer_name: string;
  };
}

const getDealCodeData = (dealCode: string) => {
  return axios.get<ResponseGetDealCode>(
    'https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1/deal/amarta/' + dealCode,
    {
      headers: {
        'x-api-key': '5ukoYcllpl6lIeKsbeIPI4hOZGDszFVk1dDBddHi',
      },
    }
  );
};

export default getDealCodeData;
