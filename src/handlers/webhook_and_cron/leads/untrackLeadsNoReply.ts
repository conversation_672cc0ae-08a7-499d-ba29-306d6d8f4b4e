import { Request, Response } from 'express';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import checkLeadsInteractionWithAgent from '../../../helpers/leads/checkLeadsInteractionWithAgent';

const untrackLeadsNoReply = {
  middlewares: [],
  handler: async (req: Request, res: Response) => {
    try {
      await checkLeadsInteractionWithAgent();
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e?.toString() ?? JSON.stringify(e),
        })
      );
    }
  },
};

export default untrackLeadsNoReply;
