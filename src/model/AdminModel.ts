import { IAdminDocument } from '../types/firestore/admin_document.types';
import { IRegisterAccountBody } from '../types/account/RegisterAccountHandlerTypes';
import { firestore } from 'firebase-admin';
import FirestoreDataConverter = firestore.FirestoreDataConverter;

class AdminModel implements IAdminDocument {
  active: IAdminDocument['active'];
  address: IAdminDocument['address'];
  created_time: IAdminDocument['created_time'];
  doc_admin: IAdminDocument['doc_admin'];
  doc_department: IAdminDocument['doc_department'] | null;
  doc_project: IAdminDocument['doc_project'];
  project: IAdminDocument['project'];
  department: IAdminDocument['department'];
  email: IAdminDocument['email'];
  last_session_active: IAdminDocument['last_session_active'];
  level: IAdminDocument['level'];
  name: IAdminDocument['name'];
  phone_number: IAdminDocument['phone_number'];
  createdBy: IAdminDocument['createdBy'];
  createdSource: IAdminDocument['createdSource'];
  amartaVip: IAdminDocument['amartaVip'];
  ref!: IAdminDocument['ref'];

  constructor(params: {
    active?: IAdminDocument['active'];
    address?: IAdminDocument['address'];
    created_time?: IAdminDocument['created_time'];
    doc_admin: IAdminDocument['doc_admin'];
    ref: IAdminDocument['ref'];
    doc_department: IAdminDocument['doc_department'];
    department: IAdminDocument['department'];
    last_session_active?: IAdminDocument['last_session_active'];
    doc_project: IAdminDocument['doc_project'];
    project: IAdminDocument['project'];
    email: IAdminDocument['email'];
    level: IAdminDocument['level'];
    name: IAdminDocument['name'];
    phone_number?: IAdminDocument['phone_number'];
    createdBy?: IAdminDocument['createdBy'];
    createdSource?: IAdminDocument['createdSource'];
    amartaVip?: IAdminDocument['amartaVip'];
  }) {
    this.active = params.active || false;
    this.address = params.address || '';
    this.created_time = params.created_time || new Date();
    this.doc_admin = params.doc_admin;
    this.ref = params.ref;
    this.doc_project = params.doc_project;
    this.project = params.project;
    this.doc_department = params.doc_department || null;
    this.department = params.department || null;
    this.email = params.email;
    this.level = params.level;
    this.name = params.name;
    this.phone_number = params.phone_number || '';
    this.last_session_active = params.last_session_active || 0;
    this.createdBy = params.createdBy || null;
    this.createdBy = params.createdBy || null;
    this.createdSource = params.createdSource || null;
    this.amartaVip = params.amartaVip || null;
  }

  public static converter: FirestoreDataConverter<AdminModel> = {
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot): AdminModel {
      return new AdminModel({
        name: snapshot.get('name'),
        email: snapshot.get('email'),
        active: snapshot.get('active'),
        address: snapshot.get('address'),
        created_time: snapshot.get('created_time').toDate(),
        doc_admin: snapshot.ref,
        ref: snapshot.ref,
        doc_department: snapshot.get('doc_department'),
        doc_project: snapshot.get('doc_project'),
        project: snapshot.get('project') || null,
        department: snapshot.get('department') || null,
        last_session_active: snapshot.get('last_session_active'),
        level: snapshot.get('level'),
        phone_number: snapshot.get('phone_number'),
        createdBy: snapshot.get('createdBy'),
        createdSource: snapshot.get('createdSource'),
        amartaVip: snapshot.get('amartaVip'),
      });
    },
    toFirestore(
      modelObject:
        | FirebaseFirestore.WithFieldValue<AdminModel>
        | FirebaseFirestore.PartialWithFieldValue<AdminModel>,
      options?: FirebaseFirestore.SetOptions
    ): any {
      return {
        active: modelObject.active,
        address: modelObject.address,
        created_time: modelObject.created_time,
        doc_admin: modelObject.doc_admin,
        ref: modelObject.ref,
        doc_department: modelObject.doc_department,
        doc_project: modelObject.doc_project,
        project: modelObject.project || null,
        department: modelObject.department,
        email: modelObject.email,
        last_session_active: modelObject.last_session_active,
        level: modelObject.level,
        name: modelObject.name,
        phone_number: modelObject.phone_number,
        createdBy: modelObject.createdBy || null,
        createdSource: modelObject.createdSource || null,
        amartaVip: modelObject.amartaVip || null,
      };
    },
  };

  toJsonResponse(): IAdminDocument<Date, string> {
    return {
      ...this,
      ref: this.ref.path,
      doc_admin: this.doc_admin.path,
      doc_project: this.doc_project.path,
      doc_department: this.doc_department?.path,
    };
  }
}

export default AdminModel;
