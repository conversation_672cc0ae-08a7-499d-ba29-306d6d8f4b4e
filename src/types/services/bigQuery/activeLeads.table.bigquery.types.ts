import { BigQueryTimestamp } from '@google-cloud/bigquery';

export interface ActiveLeadsTableTypes {
  uuid: string;
  agent_name: string | null;
  agent_code: string | null;
  organization: string | null;
  city_group: string | null;
  leads_phone_number: string | null;
  leads_full_name: string | null;
  leads_salutation: string | null;
  domicile: {
    province_code: string | null;
    province_name: string | null;
    city_code: string | null;
    city_name: string | null;
  } | null;
  vehicle: {
    brand: {
      name: string | null;
    } | null;
    model: {
      name: string | null;
    } | null;
    variant: {
      name: string | null;
      code: string | null;
    } | null;
    color: {
      name: string | null;
      code: string | null;
    } | null;
  } | null;
  source: string | null;
  bq_created_at_timestamp: BigQueryTimestamp;
  leads_created_at_timestamp: BigQueryTimestamp;
}
