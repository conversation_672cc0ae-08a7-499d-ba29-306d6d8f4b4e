import { j<PERSON>, <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import triF<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../../middlewares/triforceAuthChecker';
import requestValidator from '../../../middlewares/requestValidator';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';

interface ReqBody {
  agentCode: string;
  phoneNumber: string;
  organization: string;

  title: string;
  provinceName: string;
  provinceCode: string;
  cityName: string;
  cityCode: string;
  vehicleUsage: 'individual' | 'shared' | 'corporate';
  paymentPlan: 'cash' | 'credit';
  hasVehicleLoan: boolean;
  vehicleOptions: {
    brand: {
      name: string;
    };
    model: {
      name: string;
    };
    variant: {
      code: string;
      name: string;
    };
    color: {
      code: string;
      name: string;
    };
  }[];
  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition';
  nextTotalVehicleOwnerShip: string;

  idCard_number: string | null;
  driverLicense_number: string | null;
}

const updateLeads: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    triForceAuthChecker,
    json(),

    body('agentCode').notEmpty(),
    body('organization').notEmpty().withMessage('Source2 is required'),
    body('phoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),

    body('title').notEmpty().withMessage('Title is required'),
    body('provinceName').notEmpty().withMessage('Province name is required'),
    body('provinceCode').notEmpty().withMessage('Province code is required'),
    body('cityName').notEmpty().withMessage('City name is required'),
    body('cityCode').notEmpty().withMessage('City code is required'),
    body('vehicleUsage')
      .notEmpty()
      .withMessage('Vehicle usage is required')
      .isIn(['individual', 'shared', 'corporate'])
      .withMessage('Invalid vehicle usage'),
    body('paymentPlan')
      .notEmpty()
      .withMessage('Payment plan is required')
      .isIn(['cash', 'credit'])
      .withMessage('Invalid payment plan'),
    body('hasVehicleLoan')
      .optional()
      .default(false)
      .isBoolean()
      .withMessage('Invalid hasVehicleLoan format'),
    body('vehicleOptions')
      .isArray({ min: 1 })
      .withMessage('At least one vehicle option is required')
      .custom((value: ReqBody['vehicleOptions']) => {
        for (const option of value) {
          if (
            !option.brand?.name ||
            !option.model?.name ||
            !option.variant?.code ||
            !option.variant?.name ||
            !option.color?.code ||
            !option.color?.name
          ) {
            throw new Error('Invalid vehicle option format');
          }
        }
        return true;
      }),

    body('purchasePlan')
      .notEmpty()
      .isIn(['firstVehicle', 'vehicleReplacement', 'vehicleAddition'])
      .withMessage('purchasePlan is required'),
    body('nextTotalVehicleOwnerShip').notEmpty(),
    body('idCard_number')
      .optional()
      .customSanitizer(input => (!input ? null : input)),
    body('driverLicense_number')
      .optional()
      .customSanitizer(input => (!input ? null : input)),

    requestValidator,
  ],
  handler: async function (req, res) {
    const leadsCollection = myFirestore.collection('leads');
    const docName = `${req.body.organization}-${req.body.phoneNumber}`;
    const get = await leadsCollection.withConverter(LeadsModel.converter).doc(docName).get();

    if (!get.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
        })
      );
    }

    const data = get.data();

    if (data?.agentCode !== req.body.agentCode) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
        })
      );
    }

    data.title = req.body.title;
    data.domicile = {
      cityCode: req.body.cityCode,
      cityName: req.body.cityName,
      provinceCode: req.body.provinceCode,
      provinceName: req.body.provinceName,
    };
    data.vehicleUsage = req.body.vehicleUsage;
    data.paymentPlan = req.body.paymentPlan;
    data.hasVehicleLoan = req.body.hasVehicleLoan;
    data.vehicleOptions = req.body.vehicleOptions;
    data.purchasePlan = req.body.purchasePlan;
    data.nextTotalVehicleOwnerShip = req.body.nextTotalVehicleOwnerShip;
    data.idCard_number = req.body.idCard_number;
    data.driverLicense_number = req.body.driverLicense_number;
    data.updatedAt = new Date();

    try {
      await data.ref.withConverter(LeadsModel.converter).set(data);
      res.send(
        successResponse({
          type: 'UPDATED',
          data: data.toJsonResponse(),
        })
      );
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }
  },
};

export default updateLeads;
