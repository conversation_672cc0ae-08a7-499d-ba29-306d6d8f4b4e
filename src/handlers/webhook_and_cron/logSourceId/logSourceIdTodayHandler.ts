import { Request<PERSON>and<PERSON> } from 'express';
import moment from 'moment';
import { myFirestore } from '../../../services/firebaseAdmin';
import { ILogAdSourceIdServiceParamsTypes } from '../../../types/LogAdSorceIdService.types';
import { IAdSourceIdLocDocument } from '../../../types/firestore/ad_source_id_log_document.types';
import collect, { Collection } from 'collect.js';
import successResponse from '../../../schema/successResponse';
import logAdSourceIdServices from '../../../services/LogAdSourceIdServices';
import { firestore } from 'firebase-admin';

const logSourceIdTodayHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [],
  handler: async (req, res) => {
    let today = moment();
    let startAt = today.clone().startOf('day');
    let endAt = today.clone().endOf('day');

    const logCollection = myFirestore.collection('ad_source_id_logs');
    const getLogs = await logCollection
      // .where("successSendToAggregateEndpoint", "==", false)
      .where('createdAt', '>=', startAt.toDate())
      .where('createdAt', '<=', endAt.toDate())
      .get();

    const logData: (IAdSourceIdLocDocument & { ref: firestore.DocumentReference })[] = [];

    getLogs.forEach(result => {
      const data = result.data() as any;
      if (!data.successSendToAggregateEndpoint) {
        logData.push({
          ...data,
          ref: result.ref,
        });
      }
    });

    const collectLogDataGroupByDealCode = collect(logData).groupBy('dealCode');

    for (const dealCode in collectLogDataGroupByDealCode.all()) {
      const logsCollection: Collection<
        IAdSourceIdLocDocument & {
          ref: firestore.DocumentReference;
        }
      > = collectLogDataGroupByDealCode.get(dealCode) as any;
      const paramsToSend: ILogAdSourceIdServiceParamsTypes = {
        count: logsCollection.count(),
        count_new_customer: logsCollection.filter(l => l.isNewUser).count(),
        company: 'amarta',
        activity: 'IDEAL_CHAT',
        date: today.format('YYYY-MM-DD'),
        deal_code: dealCode,
        phone_numbers: logsCollection.map(l => l.phoneNumber).toArray(),
        phone_numbers_new_customer: logsCollection
          .filter(l => l.isNewUser)
          .map(l => l.phoneNumber)
          .toArray(),
      };

      try {
        await logAdSourceIdServices.logDealCodeActivity(paramsToSend);
        const batch = myFirestore.batch();
        for (const logDoc of logsCollection.all()) {
          batch.update(logDoc.ref, {
            successSendToAggregateEndpoint: true,
          });
        }
        await batch.commit();
      } catch (e) {
        console.log('ERROR_SEND', e, dealCode);
      }
    }

    res.send(
      successResponse({
        data: null,
      })
    );
  },
};

export default logSourceIdTodayHandler;
