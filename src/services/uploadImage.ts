import axios from 'axios';

function upload(params: {
  payload: {
    documentName: string;
    documentType: string;
    source: string;
    image: File;
  };
  onSuccess: (url: string) => void;
  onError: (error: any) => void;
}) {
  const base = axios.create({
    baseURL: 'https://hbxhwzeej9.execute-api.ap-southeast-1.amazonaws.com/v1/upload-document/',
  });

  const formData = new FormData();
  formData.append('document_name', params.payload.documentName);
  formData.append('document_type', params.payload.documentType);
  formData.append('source', params.payload.source);
  formData.append('image', params.payload.image);

  return base
    .post<{ data: { url_document: string } }>('/document-data', formData)
    .then(res => {
      if (res.status === 200 && res.data) {
        const imageUrl = res.data.data.url_document;
        params.onSuccess(imageUrl);
      } else {
        params.onError(res.data);
      }
    })
    .catch(err => {
      if (axios.isAxiosError(err)) {
        params.onError(err.response?.data);
      } else {
        params.onError(err);
      }
    });
}

const uploadImage = (image: File) => {
  return new Promise<string>((resolve, reject) => {
    upload({
      payload: {
        documentName: image.name.replace(/\.[^/.]+$/, ''),
        documentType: image.type,
        source: 'autotrimitra-backend',
        image: image,
      },
      onSuccess: url => {
        resolve(url);
      },
      onError: error => {
        reject(error);
      },
    });
  });
};

export default uploadImage;
