import { Request<PERSON>and<PERSON> } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import collect from 'collect.js';
import ColdLeadsModel from '../../../model/ColdLeadsModel';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import moment from 'moment';
import successResponse from '../../../schema/successResponse';
import aggregatesAgent from '../../../helpers/leads/aggregatesAgent';

const moveToCloudUnactiveLeadsAllAgent: {
  handler: RequestHandler;
} = {
  handler: async (req, res) => {
    const coldLeadsCollection = myFirestore.collection('cold_leads');
    const coldLeadsHistoriesCollection = myFirestore.collection('cold_leads_histories');

    const getLeadsUnactive = await myFirestore
      .collection('leads')
      .where('isTracking', '==', false)
      .where('organization', 'in', ['amartachery', 'amartahonda', 'amartaneta', 'amartavinfast'])
      .withConverter(LeadsModel.converter)
      .get();

    const leads: LeadsModel[] = [];

    getLeadsUnactive.forEach(result => {
      const data = result.data()!;
      leads.push(data);
    });

    const leadsCollection = collect(leads);
    const leadsChunk = leadsCollection.chunk(120);

    const now = moment();

    let leadsPartCount = 1;
    for (const leadsPart of leadsChunk.all()) {
      const batch = myFirestore.batch();
      for (const leads of leadsPart) {
        const docName = `${leads.organization}-${leads.phoneNumber}`;

        let notes: ILeadsNotesDocument<Date> = {
          agentCode: leads.agentCode,
          event: 'moveToCold',
          notes: 'Move to Cold - ' + 'SYSTEM',
          organization: leads.organization,
          phoneNumber: leads.phoneNumber,
          statusLevel: leads.statusLevel,
          updatedAt: now.toDate(),
          updatedByUser: null,
          firstName: leads.firstName,
          lastName: leads.lastName,
          agentName: leads.agentName,
          moveToCold: true,
          reactivate: {
            currentTotal:
              leads.updateHistories?.[leads.updateHistories.length - 1].reactivate?.currentTotal ??
              0,
          },
          totalUpdateNotes:
            leads.updateHistories?.[leads.updateHistories.length - 1].totalUpdateNotes + 1,
        };

        leads.isTracking = false;
        leads.notes = notes.notes;
        leads.updateHistories.push({
          ...notes,
        });

        const coldLeads = new ColdLeadsModel({
          ...leads,
          tradeIn: null,
          moveToColdAt: now.toDate(),
          moveToColdBy: 'SYSTEM',
          coldNotes: notes.notes,
          moveToColdByUserType: 'admin',
        });

        batch.set(
          coldLeadsCollection.doc(docName).withConverter(ColdLeadsModel.converter),
          coldLeads
        );
        batch.set(
          coldLeadsHistoriesCollection.doc().withConverter(ColdLeadsModel.converter),
          coldLeads
        );
        batch.set(myFirestore.collection('leads_notes').doc(), notes);
        batch.delete(leads.ref);
      }
      try {
        await batch.commit();
        console.log('Part ', leadsPartCount, 'success');
      } catch (e) {
        console.log(e, leadsPart);
      }
      leadsPartCount++;
    }

    const groupBy = leadsCollection.groupBy('agentCode');
    const agentCodes: string[] = groupBy.map((group, key) => key).toArray();

    const batchAggregate = myFirestore.batch();
    for (const agentCode of agentCodes) {
      await aggregatesAgent(agentCode, 'amartachery', batchAggregate);
      await aggregatesAgent(agentCode, 'amartahonda', batchAggregate);
      await aggregatesAgent(agentCode, 'amartaneta', batchAggregate);
      await aggregatesAgent(agentCode, 'amartavinfast', batchAggregate);
    }
    try {
      await batchAggregate.commit();
    } catch (e) {
      console.log('ERROR AGGREGATING', e);
    }

    console.log(`Total leads: ${leads.length}`, `Total agent: ${agentCodes.length}`, agentCodes);

    res.send(
      successResponse({
        data: {
          totalLeads: leads.length,
          totalAgent: agentCodes.length,
          agents: agentCodes,
        },
      })
    );
  },
};

export default moveToCloudUnactiveLeadsAllAgent;
