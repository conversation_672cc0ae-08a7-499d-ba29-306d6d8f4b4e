import { Request, Response } from 'express';
import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';

interface RequestBody {
  phoneNumber: string;
  organization: string;
  agentCode: string;
  idCardNumber: string;
}

const updateIdCard = {
  middlewares: [
    triForceAuthChecker,
    body('phoneNumber').notEmpty(),
    body('organization').notEmpty(),
    body('agentCode').notEmpty(),
    body('idCardNumber').notEmpty(),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, RequestBody>, res: Response) => {
    const collections = myFirestore.collection('leads');
    const find = await collections
      .where('agentCode', '==', req.body.agentCode)
      .where('phoneNumber', '==', req.body.phoneNumber)
      .where('organization', '==', req.body.organization)
      .withConverter(LeadsModel.converter)
      .get();

    if (find.size == 0) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
        })
      );
    }

    let leads!: LeadsModel;

    find.forEach(result => {
      leads = result.data();
    });

    leads.idCard_number = req.body.idCardNumber;

    try {
      const batch = myFirestore.batch();
      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      await batch.commit();

      res.send(
        successResponse({
          type: 'UPDATED',
          data: req.body,
        })
      );
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }
  },
};

export default updateIdCard;
