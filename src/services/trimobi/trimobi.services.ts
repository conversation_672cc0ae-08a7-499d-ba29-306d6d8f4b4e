import axios, { isAxiosError } from 'axios';
import { ICreateAdRequest } from './trimobi.services.types';
import amartavipService from '../amartavip/amartavip.services';

class TrimobiService {
  private baseUrl = 'https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1';
  private apiKey = '3YLfRdqaaPaASF0MzmqH89U1n2SNx5K92kkV6rf8';
  private baseAxios = axios.create({
    baseURL: this.baseUrl,
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': this.apiKey,
    },
  });

  private handleAxiosError(error: unknown, context: string): never {
    if (isAxiosError(error)) {
      console.log(`Error during ${context}:`);
      console.log('HTTP Method:', error.config?.method);
      console.log('Base URL:', error.config?.baseURL);
      console.log('Endpoint:', error.config?.url);
      console.log('Headers:', error.config?.headers);
      console.log('Request Body:', error.config?.data);
      console.log('Response:', error.response?.data);
    }
    throw error;
  }

  public async createAd(payload: ICreateAdRequest) {
    try {
      const response = await this.baseAxios.post<{
        meta: { used_vehicle_uid: string };
      }>('/used-vehicle/listing-vehicle-sale', payload, {
        headers: {
          Authorization: `Bearer ${await amartavipService.getToken()}`,
        },
      });
      return response.data;
    } catch (error) {
      this.handleAxiosError(error, 'create ad');
    }
  }
}

const trimobiService = new TrimobiService();

export default trimobiService;
