import { Request, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { myFirestore } from '../../services/firebaseAdmin';
import successResponse from '../../schema/successResponse';
import { param, query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';

interface ReqQueries {
  agentCodes?: string[] | string;
}

const getLeadCounter = {
  middlewares: [
    leadsAuthorizer,
    param('organization').notEmpty().trim(),
    query('agentCodes')
      .optional()
      .customSanitizer(input => {
        if (!Array.isArray(input)) return [input];
        return input;
      }),
    requestValidator,
  ],
  async handler(
    req: Request<{ organization: string }, ResponseSchema, any, ReqQueries>,
    res: Response
  ) {
    const collections = myFirestore.collection('leads_agent_counter');
    const get = await collections.where('organization', '==', req.params.organization).get();

    const results: any[] = [];

    get.forEach(result => {
      const data = result.data();

      if (req.query.agentCodes) {
        if (req.query.agentCodes?.indexOf(data.agentId) > -1) {
          results.push(data);
        }
      } else {
        results.push(data);
      }
    });

    // for (const result of results) {
    //     await aggregatesAgent(result.agentId, result.organization);
    // }

    res.send(
      successResponse({
        data: results.map(value => {
          return {
            ...value,
            updatedAt: value.updatedAt.toDate(),
          };
        }),
      })
    );
  },
};

export default getLeadCounter;
