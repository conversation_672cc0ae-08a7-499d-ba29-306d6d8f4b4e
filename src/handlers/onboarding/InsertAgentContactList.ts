import { json, RequestHand<PERSON> } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import { IAgentContactTypes } from '../../types/onboarding/agentContact.types';
import moment from 'moment';
import successResponse from '../../schema/successResponse';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import errorResponse from '../../schema/errorResponse';

interface ReqBody {
  agentPhoneNumber: string;
  contacts: {
    phoneNumber: string;
    name: string;
  }[];
}

const insertAgentContactList: {
  middlewares: RequestHandler[];
  handler: RequestHand<PERSON><any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    json(),
    function (req, res, next) {
      console.log('INSERT_AGENT_CONTACTS', JSON.stringify(req.body));
      next();
    },
    body('agentPhoneNumber')
      .notEmpty()
      .withMessage('Nomor telepon agen dibutuhkan')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('contacts.*.phoneNumber')
      .notEmpty()
      .withMessage('Nomor telepon pada daftar kontak agen dibutuhkan')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('contacts.*.name').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const collection = myFirestore.collection('agent_contacts');

    const now = moment();

    const batch = myFirestore.batch();

    for (const contact of req.body.contacts) {
      const check = await collection
        .where('agentPhoneNumber', '==', req.body.agentPhoneNumber)
        .where('phoneNumber', '==', contact.phoneNumber)
        .get();
      if (!check.empty) continue;
      const doc = collection.doc();
      const data: IAgentContactTypes = {
        agentPhoneNumber: req.body.agentPhoneNumber,
        phoneNumber: contact.phoneNumber,
        name: contact.name,
        createdAt: now.toDate(),
        docRef: doc,
      };
      batch.set(doc, data);
    }

    try {
      await batch.commit();
    } catch (e) {
      console.log('ERROR_INSERT_AGENT_CONTACTS', JSON.stringify(req.body), e);
      return res.status(500).send(
        errorResponse({
          data: null,
          type: 'SERVER_ERROR',
          messages: 'Terjadi error pada saat insert kontak',
        })
      );
    }

    res.send(
      successResponse({
        data: null,
        type: 'CREATED',
      })
    );
  },
};

export default insertAgentContactList;
