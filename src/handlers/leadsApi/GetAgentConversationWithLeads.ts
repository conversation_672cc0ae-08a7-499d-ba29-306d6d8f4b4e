import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { param } from 'express-validator';
import { myFirestore } from '../../services/firebaseAdmin';
import successResponse from '../../schema/successResponse';

interface ReqQuery {
  phoneNumber: string;
  organization: string;
}

const getAgentConversationWithLeads: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [
    leadsAuthorizer,
    param('organization').notEmpty().trim(),
    param('phoneNumber').notEmpty().trim(),
  ],
  handler: async (req, res) => {
    const collection = myFirestore.collection('leads_agent_conversations');
    const get = await collection
      .where('phoneNumber', '==', req.query.phoneNumber)
      .where('organization', '==', req.query.organization)
      .orderBy('createdAt', 'asc')
      .get();

    let dataConversations: any[] = [];

    get.forEach(result => {
      dataConversations.push(result.data());
    });

    let jsonResponse: any[] = dataConversations.map(value => {
      return {
        ...value,
        createdAt: value.createdAt?.toDate(),

        endChatAt: value.endChatAt?.toDate(),
        startChatAt: value.startChatAt?.toDate(),
        endInboundChatAt: value.endInboundChatAt?.toDate(),
        startInboundChatAt: value.startInboundChatAt?.toDate(),
        startOutboundChatAt: value.startOutboundChatAt?.toDate(),
        endOutboundChatAt: value.endOutboundChatAt?.toDate(),
        docRef: value.docRef.path,
        leadsDocRef: value.leadsDocRef.path,
        conversations:
          value.conversations?.map((c: any) => {
            return {
              ...c,
              datetime: c.datetime.toDate(),
            };
          }) || [],
      };
    });

    res.send(
      successResponse({
        data: jsonResponse,
      })
    );
  },
};

export default getAgentConversationWithLeads;
