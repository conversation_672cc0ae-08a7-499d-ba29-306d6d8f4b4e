import { CommonRegionFields } from '../../../types/client/document/region-types';
import { Request, Response } from 'express';
import { body } from 'express-validator';
import multer from 'multer';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import requestChecker from '../../../middlewares/requestValidator';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import { File } from '@google-cloud/storage';
import { IIdCard } from '../../../types/firestore/id_card_document_types';
import ClientModel from '../../../model/ClientModel';
import { firestore } from 'firebase-admin';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';
import moment from 'moment';

export interface IdCardCaptureFields {
  idCardNumber: string;
  fullName: string;
  birthMother: string;
  placeOfBirth: string;
  dateOfBirth: Date;

  occupation: string;
  occupationCode: string;

  maritalStatus: string;
  maritalStatusCode: string;

  lastEducation: string;
  lastEducationCode: string;

  fullAddress: string;
  zipCode: string;
  province: CommonRegionFields;
  city: CommonRegionFields;
  district: CommonRegionFields;
  subDistrict: CommonRegionFields;
  neighbourhood: string;
  hamlet: string;

  domicileFullAddress: string;
  domicileZipCode: string;
  domicileProvince: CommonRegionFields;
  domicileCity: CommonRegionFields;
  domicileDistrict: CommonRegionFields;
  domicileSubDistrict: CommonRegionFields;
  domicileNeighbourhood: string;
  domicileHamlet: string;

  clientRef: string;
  type: 'idCardOwner' | 'idCardGuarantor' | 'idCardOrderMaker' | 'idCardGuarantorSpouse';

  imageUrl?: string;
  sameAsOwnerIdCard?: boolean;
}

export interface UpdateIdCardHandlerReqBody extends IdCardCaptureFields {}

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export default class SetIdCardV2Handler {
  public static middlewares = [
    formDataParse.single('image'),

    body('clientRef').notEmpty(),
    body('type').notEmpty(),

    body('idCardNumber')
      .trim()
      .notEmpty()
      .isNumeric()
      .isLength({ max: 16, min: 16 })
      .withMessage('KTP harus 16 digit'),
    body('fullName')
      .trim()
      .notEmpty()
      .customSanitizer(i => i.replace(/[^a-zA-Z0-9\s]/g, '')),
    body('placeOfBirth').trim().notEmpty(),
    body('dateOfBirth').trim().notEmpty().toDate(),
    body('occupation').trim().notEmpty(),
    body('maritalStatus').trim().notEmpty(),

    body('fullAddress').trim().notEmpty(),
    body('zipCode')
      .trim()
      .notEmpty()
      .isLength({ max: 5, min: 5 })
      .withMessage('Kode Pos harus 16 digit'),
    body('province.code').trim().notEmpty(),
    body('province.name').trim().notEmpty(),
    body('city.code').trim().notEmpty(),
    body('city.name').trim().notEmpty(),
    body('district.code').trim().notEmpty(),
    body('district.name').trim().notEmpty(),
    body('subDistrict.code').trim().notEmpty(),
    body('subDistrict.name').trim().notEmpty(),
    body('neighbourhood')
      .trim()
      .isNumeric()
      .withMessage('RT Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('RT Maksimal memiliki 3 digit')
      .notEmpty(),
    body('hamlet')
      .trim()
      .isNumeric()
      .withMessage('RW Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('RW Maksimal memiliki 3 digit')
      .notEmpty(),

    body('birthMother').optional(),
    body('lastEducation').optional(),

    body('domicileProvince.code').optional().trim().notEmpty(),
    body('domicileProvince.name').optional().trim().notEmpty(),
    body('domicileCity.code').optional().trim().notEmpty(),
    body('domicileCity.name').optional().trim().notEmpty(),
    body('domicileDistrict.code').optional().trim().notEmpty(),
    body('domicileDistrict.name').optional().trim().notEmpty(),
    body('domicileSubDistrict.code').optional().trim().notEmpty(),
    body('domicileSubDistrict.name').optional().trim().notEmpty(),
    body('domicileZipCode')
      .optional()
      .trim()
      .isLength({ max: 5, min: 5 })
      .withMessage('Kode Pos harus 16 digit'),
    body('domicileNeighbourhood')
      .optional()
      .trim()
      .isNumeric()
      .withMessage('Domisili RT Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('Domisili RT Maksimal memiliki 3 digit'),
    body('domicileHamlet')
      .optional()
      .trim()
      .isNumeric()
      .withMessage('Domisili RW Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('Domisili RW Maksimal memiliki 3 digit'),

    body('imageUrl').optional(),

    body('sameAsOwnerIdCard').optional().toBoolean(),
    requestChecker,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, UpdateIdCardHandlerReqBody>,
    res: Response<ResponseSchema>
  ) {
    const globalVar: {
      file: File | null;
      currentIdCardData: IIdCard<firestore.Timestamp> | null;
      fieldIndexToUpdate: string;
    } = {
      file: null,
      currentIdCardData: null,
      fieldIndexToUpdate: '',
    };

    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.withConverter(ClientModel.converter).get();
    const dataClient = getClient.data()!;

    switch (req.body.type) {
      case 'idCardOwner':
        globalVar.currentIdCardData = dataClient.details?.idCardOwner || null;
        globalVar.fieldIndexToUpdate = 'idCardOwner';
        break;
      case 'idCardOrderMaker':
        globalVar.currentIdCardData = dataClient.details?.idCardGuarantor || null;
        globalVar.fieldIndexToUpdate = 'idCardOrderMaker';
        break;
      case 'idCardGuarantor':
        globalVar.currentIdCardData = dataClient.details?.idCardOrderMaker || null;
        globalVar.fieldIndexToUpdate = 'idCardGuarantor';
        break;
      case 'idCardGuarantorSpouse':
        globalVar.currentIdCardData = dataClient.details?.idCardGuarantorSpouse || null;
        globalVar.fieldIndexToUpdate = 'idCardGuarantorSpouse';
        break;
    }

    if (req.file?.buffer) {
      globalVar.file = bucketTrimitraIdeal.file(
        `client-documents/${clientDocRef.id}/${req.body.type.toLowerCase()}.jpg`
      );
      await globalVar.file.save(req.file.buffer, {
        public: true,
      });
    }

    const now = moment();

    const idCardData: IIdCard = {
      idCardImage: globalVar.file?.publicUrl() || globalVar.currentIdCardData?.idCardImage || '',
      idCardNumber: req.body.idCardNumber,
      fullName: req.body.fullName,
      birthMother: req.body.birthMother || '',
      dateOfBirth: req.body.dateOfBirth,
      placeOfBirth: req.body.placeOfBirth,

      lastEducation: req.body.lastEducation || '',
      lastEducationCode: req.body.lastEducationCode || '',
      maritalStatus: req.body.maritalStatus || '',
      maritalStatusCode: req.body.maritalStatusCode || '',
      occupation: req.body.occupation || '',
      occupationCode: req.body.occupationCode || '',

      fullAddress: req.body.fullAddress,
      province: req.body.province,
      city: req.body.city,
      district: req.body.district,
      subDistrict: req.body.subDistrict,
      zipCode: req.body.zipCode,
      neighbourhood: req.body.neighbourhood,
      hamlet: req.body.hamlet,

      domicileFullAddress: req.body.domicileFullAddress,
      domicileProvince: req.body.domicileProvince,
      domicileCity: req.body.domicileCity,
      domicileDistrict: req.body.domicileDistrict,
      domicileSubDistrict: req.body.domicileSubDistrict,
      domicileZipCode: req.body.domicileZipCode,
      domicileHamlet: req.body.domicileHamlet,
      domicileNeighbourhood: req.body.domicileNeighbourhood,

      updatedAt: now.toDate(),
    };

    try {
      await clientDocRef.update({
        [`details.${globalVar.fieldIndexToUpdate}`]: idCardData,
      });
    } catch (e) {
      console.log(e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi error pada server',
        })
      );
    }

    res.send(
      successResponse({
        type: 'UPDATED',
        data: idCardData,
      })
    );
  }
}
