import { HandlerTypes } from '../../../types/handler.types';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { PlanCheckoutResponse } from '../../../services/amartavip/amartavip.services.types';
import { isAxiosError } from 'axios';
import { body } from 'express-validator';
import successResponse from '../../../schema/successResponse';
import { generateQrCodeImage } from '../../../helpers/generateQrCodeImage';
import uploadImage from '../../../services/uploadImage';
import errorResponse from '../../../schema/errorResponse';
import requestValidator from '../../../middlewares/requestValidator';
import { json } from 'express';
import authChecker from '../../../middlewares/authChecker';
import amartavipService from '../../../services/amartavip/amartavip.services';

interface ReqBody {
  projectId: string;
  planCode: string;
  adsQuantity: number;
  customerPhoneNumber: string;
}

const trimobiUpgradePlanHandler: HandlerTypes<unknown, ResponseSchema, ReqBody> = {
  middlewares: [
    json(),
    authChecker,
    function (req, res, next) {
      amartavipService.setEmail(res.locals.account.email);
      next();
    },
    body('projectId').isString().notEmpty().withMessage('Project ID is required'),
    body('planCode').isString().notEmpty().withMessage('Plan code is required'),
    body('adsQuantity').isInt({ min: 1 }).withMessage('Ads quantity must be a positive integer'),
    body('customerPhoneNumber')
      .isString()
      .notEmpty()
      .withMessage('Customer phone number is required'),
    requestValidator,
  ],
  handler: async (req, res) => {
    let checkOutResponse: PlanCheckoutResponse | null = null;
    let qrImageUrl = '';

    // Try-catch for checkout process
    try {
      checkOutResponse = await amartavipService.adsPlanUpgradeCheckout({
        source: 'trimobi',
        code: req.body.planCode,
        ads_option_qty: req.body.adsQuantity,
        payment_issuer: 'xendit',
        payment_method: 'qris',
        notes: 'Upgrade paket oleh nomor telepon ' + req.body.customerPhoneNumber,
      });
    } catch (error) {
      let errorMessage = 'Terjadi kesalahan ketika melakukan upgrade plan';
      if (isAxiosError(error)) {
        errorMessage = 'Error API upgrade plan: ' + error.response?.data.message;
      }
      return res.status(500).json(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: errorMessage,
        })
      );
    }

    // Try-catch for QR code generation and upload
    try {
      const generateImage = await generateQrCodeImage(
        checkOutResponse!.data.bill_data,
        'qris-qrcode.png',
        {
          width: 400,
          margin: 2,
        }
      );
      qrImageUrl = await uploadImage(generateImage);
    } catch (error) {
      console.error('Error generating/uploading QR code:', error);
      return res.status(500).json(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi kesalahan saat membuat QR code',
        })
      );
    }

    return res.status(200).json(
      successResponse({
        data: {
          qrisUrl: qrImageUrl,
          checkout: checkOutResponse.data,
        },
      })
    );
  },
};

export default trimobiUpgradePlanHandler;
