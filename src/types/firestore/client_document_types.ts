import { firestore } from 'firebase-admin';
import { IIdCard } from './id_card_document_types';
import { IFamilyRegister } from './family_registration_document_types';
import { ISelfie } from './slefie-document-types';

export interface IClientDocument<TIMESTAMP = firestore.Timestamp> {
  contacts: {
    whatsapp: string;
    email?: string;
    phoneNumber: string;
  };
  created_time: TIMESTAMP;
  doc_admin: string | null;
  doc_id: string | null;
  doc_project_origin: firestore.DocumentReference | null;
  details?: {
    owner_phone_number?: string;
    order_maker_phone_number?: string;
    guarantor_phone_number?: string;
    idCardOwner: IIdCard<TIMESTAMP> | null;
    idCardGuarantor: IIdCard<TIMESTAMP> | null;
    idCardOrderMaker: IIdCard<TIMESTAMP> | null;
    idCardGuarantorSpouse: IIdCard<TIMESTAMP> | null;
    familyRegister: IFamilyRegister | null;
    selfie: <PERSON>elfie | null;
  };
  profile: {
    name: string;
    phone_number: string;
    area?: {
      text: string;
      value: string;
    };
  };

  freeLeadsStatus?: {
    submitted: boolean;
    createdAt: TIMESTAMP | null;
    organization: string;
  };

  acquiredLeadsStatus?: {
    acquired: boolean;
    agentName: string;
    agentCode: string;
    acquiredAt: TIMESTAMP;
    organization: string;
  };

  dream_vehicle?: {
    buy_time: TIMESTAMP;
    color_code: string;
    color_name: string;
    model_name: string;
    variant_code: string;
    variant_name: string;
    year: string;
  } | null;
  survey?: {
    credit_scheme?: {
      down_payment: number;
      down_payment_discount: number;
      installment: number;
      tenor: number;
      discountTenor: number;
      discountInstallment: number;
    } | null;
  } | null;
  leads?: {
    [key: string]: any;
  };

  order_histories?: IClientEntityOrderHistory[];
  finalDecision: {
    result: string;
    updatedAt: TIMESTAMP;
    followUp: {
      dateTime: TIMESTAMP;
      message: {
        template: string;
        variables: string[];
      };
      followUpCode: string | null;
    } | null;
    createdBy: {
      email: string;
      uid: string;
    };
  } | null;
}

export interface IClientEntityOrderHistory {
  offer_code: string;
  created_at: firestore.Timestamp;
  payment_scheme: 'CASH' | 'CREDIT';
  source: 'IDEAL' | 'TRIFORCE';
}
