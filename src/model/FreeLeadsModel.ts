import IFreeLeadsDocument from '../types/firestore/free_leads_document.types';
import { LeadsOrganization } from '../types/firestore/leads_model.types';
import { firestore } from 'firebase-admin';

class FreeLeadsModel implements IFreeLeadsDocument<Date> {
  public static converter: firestore.FirestoreDataConverter<FreeLeadsModel> = {
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot): FreeLeadsModel {
      const data = snapshot.data() as IFreeLeadsDocument;
      return new FreeLeadsModel({
        area: data['area'],
        title: data['title'],
        firstName: data['firstName'],
        lastName: data['lastName'],
        phoneNumber: data['phoneNumber'],
        email: data['email'],
        domicile: data['domicile'],
        vehicleUsage: data['vehicleUsage'],
        paymentPlan: data['paymentPlan'],
        hasVehicleLoan: data['hasVehicleLoan'],
        vehicleOptions: data['vehicleOptions'],
        source: data.source,
        organization: data.organization,
        createdAt: data['createdAt'].toDate(),
        purchasePlan: data['purchasePlan'],
        nextTotalVehicleOwnerShip: data['nextTotalVehicleOwnerShip'],
        idCard_number: data['idCard_number'],
        driverLicense_number: data['driverLicense_number'],
        externalId: data['externalId'],

        isAcquired: data['isAcquired'],
        acquiredAt: data['acquiredAt']?.toDate() ?? null,
        acquiredAgentCode: data['acquiredAgentCode'],
        acquiredAgentName: data['acquiredAgentName'],

        whatsapp: data['whatsapp']
          ? {
              ...data.whatsapp,
              statuses: data.whatsapp.statuses
                ? {
                    delivered: data.whatsapp.statuses.delivered?.toDate() ?? null,
                    read: data.whatsapp.statuses.read?.toDate() ?? null,
                  }
                : null,
              response: data.whatsapp?.response
                ? {
                    ...data.whatsapp.response,
                    repliedAt: data.whatsapp.response.repliedAt.toDate(),
                  }
                : null,
            }
          : null,

        price: data['price'],
        isCustomPrice: data['isCustomPrice'] ?? false,

        notes: data['notes'] ?? '',

        disableWhatsapp: data['disableWhatsapp'] || false,
        createdBy: data['createdBy'] || '',

        ref: snapshot.ref,

        rawLeadsRef: data['rawLeadsRef'] || null,

        ideal: data['ideal'] || null,
        transactionId: data['transactionId'] || null,

        rating: data['rating'] || {
          rating: 0,
          count: 0,
        },
      });
    },
    toFirestore(modelObject): FirebaseFirestore.DocumentData {
      return {
        area: modelObject['area'] || null,
        title: modelObject['title'] || null,
        firstName: modelObject['firstName'],
        lastName: modelObject['lastName'] || '',
        phoneNumber: modelObject['phoneNumber'],
        email: modelObject['email'] || '',
        domicile: modelObject['domicile'],
        vehicleUsage: modelObject['vehicleUsage'],
        paymentPlan: modelObject['paymentPlan'] || null,
        hasVehicleLoan: modelObject['hasVehicleLoan'] || false,
        vehicleOptions: modelObject['vehicleOptions'],
        source: modelObject.source,
        organization: modelObject.organization,
        createdAt: modelObject['createdAt'],
        purchasePlan: modelObject['purchasePlan'] || null,
        nextTotalVehicleOwnerShip: modelObject['nextTotalVehicleOwnerShip'] || null,
        idCard_number: modelObject['idCard_number'] || '',
        driverLicense_number: modelObject['driverLicense_number'] || '',
        externalId: modelObject['externalId'] || '',

        isAcquired: modelObject['isAcquired'],
        acquiredAt: modelObject['acquiredAt'],
        acquiredAgentCode: modelObject['acquiredAgentCode'],
        acquiredAgentName: modelObject['acquiredAgentName'],

        createdBy: modelObject['createdBy'] || '',

        whatsapp: modelObject['whatsapp'] ?? null,

        price: modelObject['price'],
        isCustomPrice: modelObject['isCustomPrice'] ?? false,

        disableWhatsapp: modelObject['disableWhatsapp'] || false,

        notes: modelObject['notes'] ?? '',

        rawLeadsRef: modelObject['rawLeadsRef'] || null,
        ideal: modelObject['ideal'] || null,
        transactionId: modelObject['transactionId'] || null,

        rating: modelObject['rating'] || {
          rating: 0,
          count: 0,
        },
      };
    },
  };
  area: IFreeLeadsDocument<Date>['area'];
  title: IFreeLeadsDocument<Date>['title'];
  firstName!: IFreeLeadsDocument<Date>['firstName'];
  lastName: IFreeLeadsDocument<Date>['lastName'];
  phoneNumber!: IFreeLeadsDocument<Date>['phoneNumber'];
  email: IFreeLeadsDocument<Date>['email'];
  domicile: IFreeLeadsDocument<Date>['domicile'];
  vehicleUsage: IFreeLeadsDocument<Date>['vehicleUsage'];
  paymentPlan: IFreeLeadsDocument<Date>['paymentPlan'];
  hasVehicleLoan: IFreeLeadsDocument<Date>['hasVehicleLoan'];
  vehicleOptions: IFreeLeadsDocument<Date>['vehicleOptions'];
  source: string;
  organization: LeadsOrganization;
  createdAt: IFreeLeadsDocument<Date>['createdAt'];
  createdBy: IFreeLeadsDocument<Date>['createdBy'];
  purchasePlan: IFreeLeadsDocument<Date>['purchasePlan'];
  nextTotalVehicleOwnerShip: IFreeLeadsDocument<Date>['nextTotalVehicleOwnerShip'];
  idCard_number: IFreeLeadsDocument<Date>['idCard_number'];
  driverLicense_number: IFreeLeadsDocument<Date>['driverLicense_number'];
  externalId: IFreeLeadsDocument<Date>['externalId'];
  isAcquired!: IFreeLeadsDocument<Date>['isAcquired'];
  acquiredAt!: IFreeLeadsDocument<Date>['acquiredAt'];
  acquiredAgentCode!: IFreeLeadsDocument<Date>['acquiredAgentCode'];
  acquiredAgentName!: IFreeLeadsDocument<Date>['acquiredAgentName'];
  whatsapp?: IFreeLeadsDocument<Date>['whatsapp'];
  price: IFreeLeadsDocument<Date>['price'];
  isCustomPrice: IFreeLeadsDocument<Date>['isCustomPrice'];
  notes: IFreeLeadsDocument<Date>['notes'];
  disableWhatsapp: IFreeLeadsDocument<Date>['disableWhatsapp'];
  ref!: firestore.DocumentReference;
  rawLeadsRef!: IFreeLeadsDocument<Date>['rawLeadsRef'];
  ideal: IFreeLeadsDocument<Date>['ideal'];
  transactionId: IFreeLeadsDocument<Date>['transactionId'];
  rating: IFreeLeadsDocument<Date>['rating'];

  constructor(params: {
    area: IFreeLeadsDocument<Date>['area'];
    title: IFreeLeadsDocument<Date>['title'];
    firstName: IFreeLeadsDocument<Date>['firstName'];
    lastName: IFreeLeadsDocument<Date>['lastName'];
    phoneNumber: IFreeLeadsDocument<Date>['phoneNumber'];
    email: IFreeLeadsDocument<Date>['email'];
    domicile: IFreeLeadsDocument<Date>['domicile'];
    vehicleUsage: IFreeLeadsDocument<Date>['vehicleUsage'];
    paymentPlan: IFreeLeadsDocument<Date>['paymentPlan'];
    hasVehicleLoan: IFreeLeadsDocument<Date>['hasVehicleLoan'];
    vehicleOptions: IFreeLeadsDocument<Date>['vehicleOptions'];
    source: string;
    organization: LeadsOrganization;
    createdAt: IFreeLeadsDocument<Date>['createdAt'];
    purchasePlan: IFreeLeadsDocument<Date>['purchasePlan'];
    nextTotalVehicleOwnerShip: IFreeLeadsDocument<Date>['nextTotalVehicleOwnerShip'];
    idCard_number: IFreeLeadsDocument<Date>['idCard_number'];
    driverLicense_number: IFreeLeadsDocument<Date>['driverLicense_number'];
    externalId: IFreeLeadsDocument<Date>['externalId'];

    isAcquired: IFreeLeadsDocument<Date>['isAcquired'];
    acquiredAt: IFreeLeadsDocument<Date>['acquiredAt'];
    acquiredAgentCode: IFreeLeadsDocument<Date>['acquiredAgentCode'];
    acquiredAgentName: IFreeLeadsDocument<Date>['acquiredAgentName'];

    whatsapp?: IFreeLeadsDocument<Date>['whatsapp'];

    price: IFreeLeadsDocument<Date>['price'];
    isCustomPrice?: IFreeLeadsDocument<Date>['isCustomPrice'];
    notes?: IFreeLeadsDocument<Date>['notes'];
    disableWhatsapp?: IFreeLeadsDocument['disableWhatsapp'];
    createdBy?: IFreeLeadsDocument<Date>['createdBy'];

    ref: firestore.DocumentReference;
    rawLeadsRef: IFreeLeadsDocument['rawLeadsRef'];

    ideal: IFreeLeadsDocument<Date>['ideal'];
    transactionId?: IFreeLeadsDocument<Date>['transactionId'];

    rating?: IFreeLeadsDocument<Date>['rating'];
  }) {
    this.area = params.area;
    this.title = params.title || '';
    this.firstName = params.firstName;
    this.lastName = params.lastName || '';
    this.phoneNumber = params.phoneNumber;
    this.email = params.email || '';
    this.domicile = params.domicile;
    this.vehicleUsage = params.vehicleUsage || null;
    this.paymentPlan = params.paymentPlan || null;
    this.hasVehicleLoan = params.hasVehicleLoan || false;
    this.vehicleOptions = params.vehicleOptions || [];
    this.source = params.source;
    this.organization = params.organization;
    this.createdAt = params.createdAt;
    this.purchasePlan = params.purchasePlan || null;
    this.nextTotalVehicleOwnerShip = params.nextTotalVehicleOwnerShip || null;
    this.idCard_number = params.idCard_number || '';
    this.driverLicense_number = params.driverLicense_number || '';
    this.externalId = params.externalId || '';

    this.isAcquired = params['isAcquired'];
    this.acquiredAt = params['acquiredAt'];
    this.acquiredAgentCode = params['acquiredAgentCode'];
    this.acquiredAgentName = params['acquiredAgentName'];

    this.whatsapp = params['whatsapp'] ?? null;

    this.price = params['price'];
    this.isCustomPrice = params['isCustomPrice'] ?? false;

    this.notes = params['notes'] ?? '';

    this.disableWhatsapp = params['disableWhatsapp'] || false;

    this.ref = params.ref;
    this.rawLeadsRef = params.rawLeadsRef;
    this.createdBy = params.createdBy || '';
    this.ideal = params.ideal || null;
    this.transactionId = params.transactionId || null;

    this.rating = params.rating || {
      average: 0,
      total: 0,
    };
  }

  toJsonResponse(): IFreeLeadsDocument<Date, string> {
    return {
      ...this,
      ref: this.ref.path,
      documentId: this.ref.id,
      rawLeadsRef: this.rawLeadsRef?.path || null,
      whatsapp: this.whatsapp
        ? {
            ...this.whatsapp,
            idealPath: this.whatsapp.idealPath?.path ?? '',
          }
        : null,
      ideal: this.ideal
        ? {
            ...this.ideal,
            chat_room_ref: this.ideal.chat_room_ref?.path ?? null,
          }
        : null,
    };
  }
}

export default FreeLeadsModel;
