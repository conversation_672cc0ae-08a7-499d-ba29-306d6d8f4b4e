import { Request, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { param, query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import moment, { Moment } from 'moment';
import { IResponseSendMessage } from '../../types/services/meta/metaApi.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import successResponse from '../../schema/successResponse';
import { firestore } from 'firebase-admin';

const getNotesHandler = {
  middlewares: [
    leadsAuthorizer,
    param('organization').notEmpty(),
    query('date')
      .optional()
      .isDate()
      .custom(input => {
        if (!moment(input).isValid()) {
          throw new Error('Tanggal tidak valid.');
        }

        return true;
      })
      .customSanitizer(input => {
        return moment(input);
      }),
    requestValidator,
  ],
  handler: async function (
    req: Request<
      { organization: string },
      ResponseSchema,
      any,
      {
        date: Moment;
      }
    >,
    res: Response
  ) {
    const collection = myFirestore.collection('leads_notes');
    let getQuery = collection
      .where('organization', '==', req.params.organization)
      .orderBy('updatedAt', 'desc');

    if (req.query.date) {
      const startOfDay = req.query.date.clone().startOf('day');
      const endOfDay = req.query.date.clone().endOf('day');
      getQuery = getQuery
        .where('updatedAt', '>=', startOfDay.toDate())
        .where('updatedAt', '<=', endOfDay.toDate());
    }

    const get = await getQuery.get();

    const results: ILeadsNotesDocument<String>[] = [];

    get.forEach(result => {
      const data = result.data() as ILeadsNotesDocument;

      if (data) {
        results.push({
          notes: data.notes,
          updatedByUser: data.updatedByUser,
          statusLevel: data.statusLevel,
          event: data.event,

          phoneNumber: data.phoneNumber,
          organization: data.organization,

          firstName: data.firstName,
          lastName: data.lastName,

          agentCode: data.agentCode,
          agentName: data.agentName ?? 'NO_NAME',
          updatedAt: moment(data.updatedAt.toDate()).format('YYYY-MM-DD HH:mm:ss'),
          moveToCold: false,
          reactivate: {
            currentTotal: data.reactivate?.currentTotal ?? 0,
          },
          totalUpdateNotes: data.totalUpdateNotes ?? 0,
          followUpScheduledAt: data.followUpScheduledAt
            ? moment(data.followUpScheduledAt).format('YYYY-MM-DD HH:mm:ss')
            : null,
        });
      }
    });

    res.send(
      successResponse({
        type: 'FETCHED',
        data: results,
      })
    );
  },
};

export default getNotesHandler;
