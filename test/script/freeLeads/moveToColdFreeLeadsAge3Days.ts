import { myFirestore } from '../../../src/services/firebaseAdmin';
import moment from 'moment';
import FreeLeadsModel from '../../../src/model/FreeLeadsModel';

const moveToColdFreeLeadsAge3Days = async () => {
  const threeDaysAgo = moment().subtract(3, 'days');
  const coldLeadsCollection = myFirestore.collection('cold_leads');
  const coldLeadsHistoriesCollection = myFirestore.collection('cold_leads_histories');
  const collectionFreeLeads = myFirestore.collection('free_leads');

  const find = await collectionFreeLeads
    .where('isAcquired', '==', false)
    .where('organization', '==', 'amartahonda')
    .where('createdAt', '>', threeDaysAgo.toDate())
    .withConverter(FreeLeadsModel.converter)
    .get();

  const freeLeads: FreeLeadsModel[] = [];

  find.forEach(result => {
    const d = result.data();
    console.log(d.phoneNumber, moment(d.createdAt).format('YYYY-MM-DD HH:mm'));
    freeLeads.push(d);
  });
  //
  // const now = moment();
  //
  // const chunk = collect(freeLeads).chunk(100);
  //
  // for (const chunkElement of chunk.all()) {
  //
  //     const batch = myFirestore.batch();
  //     for (const freeLeadsModel of chunkElement) {
  //         const docName = `${freeLeadsModel.organization}-${freeLeadsModel.phoneNumber}`;
  //
  //         const coldLeads = new ColdLeadsModel({
  //             agentCode: null,
  //             area: freeLeadsModel.area,
  //             title: freeLeadsModel['title'],
  //             firstName: freeLeadsModel['firstName'],
  //             lastName: freeLeadsModel['lastName'] || "",
  //             phoneNumber: freeLeadsModel['phoneNumber'],
  //             email: freeLeadsModel['email'],
  //             domicile: freeLeadsModel['domicile'],
  //             vehicleUsage: freeLeadsModel['vehicleUsage'],
  //             paymentPlan: freeLeadsModel['paymentPlan'],
  //             vehicleOptions: freeLeadsModel['vehicleOptions'],
  //             source: freeLeadsModel['source'],
  //             organization: freeLeadsModel['organization'],
  //             createdAt: freeLeadsModel['createdAt'],
  //             hasVehicleLoan: freeLeadsModel['hasVehicleLoan'],
  //             updatedAt: freeLeadsModel["createdAt"],
  //             updateHistories: [],
  //             purchasePlan: freeLeadsModel['purchasePlan'],
  //             nextTotalVehicleOwnerShip: freeLeadsModel['nextTotalVehicleOwnerShip'],
  //             isTracking: false,
  //             notes: freeLeadsModel['notes'],
  //             idCard_number: freeLeadsModel['idCard_number'],
  //             driverLicense_number: freeLeadsModel['driverLicense_number'],
  //             agentName: null,
  //             moveToColdAt: now.toDate(),
  //             moveToColdBy: "SYSTEM",
  //             spk: null,
  //             requestPromo: null,
  //             coldNotes: "",
  //             moveToColdByUserType: "admin",
  //             fromFreeLeads: true,
  //             freeLeadsCreatedAt: freeLeadsModel['createdAt'],
  //
  //             feedBackText: null,
  //             feedBackVoice: null,
  //             feedbackUpdatedAt: null,
  //
  //             ref: coldLeadsCollection.doc(docName),
  //         })
  //
  //         batch.set(
  //             coldLeadsCollection.doc(docName).withConverter(ColdLeadsModel.converter),
  //             coldLeads
  //         );
  //         batch.set(
  //             coldLeadsHistoriesCollection.doc().withConverter(ColdLeadsModel.converter),
  //             coldLeads
  //         );
  //         batch.delete(freeLeadsModel.ref);
  //     }
  //     await batch.commit();
  // }
};

moveToColdFreeLeadsAge3Days();
