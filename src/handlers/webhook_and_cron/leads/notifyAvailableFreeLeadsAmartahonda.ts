import { RequestHand<PERSON> } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import moment from 'moment';
import successResponse from '../../../schema/successResponse';

import 'moment/locale/id';
import telegramServices from '../../../services/telegramServices';
import collect from 'collect.js';

const notifyAvailableFreeLeadsAmartahonda: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [],
  handler: async (req, res) => {
    const now = moment();

    const freeLeadsCollection = myFirestore.collection('free_leads');
    const getFreeLeadsAmartahonda = await freeLeadsCollection
      .where('organization', '==', 'amartahonda')
      .where('isAcquired', '==', false)
      .orderBy('createdAt', 'desc')
      .withConverter(FreeLeadsModel.converter)
      .get();

    console.log(getFreeLeadsAmartahonda.size);

    const itemSendToTelegram: {
      name: string;
      city: string;
      source: string;
      timeRelative: string;
      phoneNumber: string;
    }[] = [];

    getFreeLeadsAmartahonda.forEach(result => {
      const data = result.data();
      const createdAt = moment(data.createdAt);
      itemSendToTelegram.push({
        city: data.domicile?.cityName ?? 'TANPA_KOTA',
        name: data.firstName,
        source: data.source,
        timeRelative: createdAt.fromNow(true),
        phoneNumber: data.phoneNumber,
      });
    });

    const chunk = collect(itemSendToTelegram).chunk(50);

    let part = 1;
    for (const chunkElement of chunk.all()) {
      let messageTelegram =
        `=== FREE LEADS AMARTA HONDA ===` +
        `\nHalaman ${part} dari ${chunk.count()}` +
        `\nData per tanggal: ${now.format('YYYY-MM-DD HH:mm')}` +
        `\nTotal ${getFreeLeadsAmartahonda.size} data` +
        `\n`;
      for (const item of chunkElement) {
        messageTelegram += `\n- ${item.name}, ${item.city}, ${item.source}, ${item.timeRelative}.`;
      }

      // console.log(messageTelegram)

      await telegramServices
        .sendMessage('-4102134525', messageTelegram)
        .then(value => {
          console.log('SUKSES_MENGIRIM_TELEGRAM_FREELEADS_AMARTAHONDA');
        })
        .catch(reason =>
          console.log('GAGAL_MENGIRIM_TELEGRAM_FREELEADS_AMARTAHONDA', reason.toString())
        );

      part++;
    }

    res.send(
      successResponse({
        type: 'FETCHED',
        data: itemSendToTelegram.length,
      })
    );
  },
};

export default notifyAvailableFreeLeadsAmartahonda;
