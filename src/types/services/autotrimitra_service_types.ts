export interface AutotrimitraBaseResponse<DATA = any> {
  data: DATA;
}

export interface AutotrimitrayBaseError {
  message: string;
  code: number;
}

export interface Fuel {
  gasoline: boolean;
}

export interface Transmission {
  manual: boolean;
  automatic?: boolean;
}

export interface IVehicleModel {
  model_uuid: string;
  model_name: string;
  market_name: string;
  brand_uuid: string;
  brand_name: string;
  category: string;
  class: string;
  engine_capacity: number | string;
  fuel: Fuel;
  transmission: Transmission;
  url_image: string;
  url_info: string;
  production_year: number[];
}

export interface IVariant {
  variant_name: string;
  variant_uuid: string;
  distributor_code: string;
  code: string;
  code_vin: string;
  color: string[];
  price?: any;
  engine_capacity?: number;
  engine_code?: any;
  engine_cylinder?: number;
  engine_hp?: number;
  engine_torque?: number;
  engine_valve?: number;
  engine_valve_config: string;
  fuel: string;
  fuel_supply_system: string;
  transmission: string;
  transmission_speed?: any;
  transmission_type?: any;
  url_image: string;
  url_info?: any;
  brand_uuid: string;
  brand_name: string;
  model_uuid: string;
  model_name: string;
  category: string;
  class: string;
  first_production_year?: number;
  latest_production_year?: number;
  generation?: number;
  model_market_name: string;
}

export interface Coordinate {
  _latitude: number;
  _longitude: number;
}

export interface IProvince {
  type: RegionType;
  code: string;
  name: string;
  coordinate: Coordinate;
}

export interface IGeo {
  type: string;
  code: string;
  name: string;
  administrative_type: string;
  coordinate: Coordinate | null;
  province_code: string;
  province_name: string;
  city_code: string;
  city_name: string;
  district_code: string;
  district_name: string;
}

export enum RegionType {
  PROVINCE = 'PROVINCE',
  CITY = 'CITY',
  DISTRICT = 'DISTRICT',
  SUBDISTRICT = 'SUBDISTRICT',
}
