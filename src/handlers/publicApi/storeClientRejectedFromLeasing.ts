import { json, RequestHand<PERSON> } from 'express';
import sendTemplate from '../../services/sendTemplate/sendTemplate';
import errorResponse from '../../schema/errorResponse';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import successResponse from '../../schema/successResponse';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../middlewares/requestValidator';
import axios, { AxiosError } from 'axios';
import moment from 'moment';
import catalogueService from '../../services/catalogueService';
import { VariantCatalogue } from '../../types/services/catalogueServices.types';

interface ReqBody {
  phoneNumber: string;
  name: string;
  leasingName: string;
  vehicleName: string;
  notes: string;
  area: string;
  vehicle: {
    model_name: string;
    variant_name: string;
    variant_code: string;
  };
}

const storeClientRejectedFromLeasing: {
  middlewares: RequestHandler[];
  handler: Request<PERSON>and<PERSON><any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    json(),
    body('phoneNumber')
      .notEmpty()
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('name').notEmpty(),
    body('leasingName').notEmpty(),
    body('vehicleName').notEmpty(),
    body('notes').notEmpty(),
    body('area').trim(),
    requestValidator,
  ],
  handler: async (req, res) => {
    console.log('cdb_template_req_body', JSON.stringify(req.body));

    let cityGroup: any = null;
    let variant: VariantCatalogue | undefined = undefined;

    if (req.body.area) {
      let region: any = null;
      try {
        const getRegion = await axios.get(
          'https://asia-southeast2-ideal-trimitra.cloudfunctions.net/api-open-search/search-region',
          {
            params: {
              q: req.body.area.replace(/[^a-zA-Z0-9]/g, ' '),
            },
          }
        );

        const response = getRegion.data.data;

        if (response.length > 0) {
          const data = response[0];
          region = {
            code: data.code,
            provinceName: data.province_name,
            provinceCode: data.province_code,
            cityName: data.city_name,
            cityCode: data.city_code,
            districtName: data.district_name,
            districtCode: data.district_code,
            regionType: data.region_type,
            cityAdministrativeType: data.city_administrative_type,
          };

          const fetchArea = await catalogueService.getAvailableArea({
            cityCode: region.cityCode,
          });

          if (fetchArea.data?.data) {
            cityGroup = fetchArea.data.data[0].city_group;
          }
        }
      } catch (e) {}
    }

    if (req.body.vehicle.variant_code && cityGroup) {
      try {
        const getVariant = await catalogueService.getVariantByCityGroup({
          cityGroup: cityGroup,
          variantCode: req.body.vehicle.variant_code,
        });

        if (getVariant.data.data.length > 0) {
          variant = getVariant.data.data[0] as VariantCatalogue;
        }
      } catch (e) {}
    }

    try {
      await sendTemplate.sendTemplateQiscus({
        target: req.body.phoneNumber,
        template_name: 'cdb_template_ver3',
        components: [
          {
            type: 'body',
            parameters: [
              {
                type: 'text',
                text: req.body.name,
              },
              {
                type: 'text',
                text: req.body.leasingName,
              },
              {
                type: 'text',
                text: req.body.vehicleName,
              },
            ],
          },
        ],
        contactName: req.body.name,
        notes: req.body.notes,
        area: cityGroup || undefined,
        vehicle: variant
          ? {
              model_name: variant.model_name,
              variant_code: variant.variant_code,
              variant_name: variant.variant_name,
              year: moment().year().toString(),
              color_name: null,
              color_code: null,
            }
          : undefined,
      });
    } catch (e: any) {
      console.log((e as AxiosError).response?.data);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.message,
          data: null,
          meta: null,
        })
      );
    }

    return res.send(
      successResponse({
        type: 'CREATED',
        meta: null,
        data: null,
      })
    );
  },
};

export default storeClientRejectedFromLeasing;
