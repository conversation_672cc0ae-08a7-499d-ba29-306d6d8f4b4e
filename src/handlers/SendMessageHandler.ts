import { body } from 'express-validator';
import { Request, Response } from 'express';
import { myFirestore } from '../services/firebaseAdmin';
import multer from 'multer';
import requestChecker from '../middlewares/requestValidator';
import { bucketKataAi } from '../services/cloudStorage';
import { v4 } from 'uuid';
import path from 'path';
import { File as GoogleStorageFile } from '@google-cloud/storage';
import { firestoreService } from '../services/FirestoreService';
import { firestore } from 'firebase-admin';
import {
  IFirestoreAdminEntity,
  IFirestoreMessageEntity,
} from '../types/firestore/messages/message_types';
import authChecker from '../middlewares/authChecker';
import moment from 'moment';
import sanitizeHtml from 'sanitize-html';
import { ISessionLog, ISessionLogDataClient } from '../types/firestore/session_log_types';
import { IAdminSessionLog } from '../types/firestore/admin_session_log_types';
import { qiscusServiceNew } from '../services/QiscusServiceNew';
import { kataAiServices } from '../services/KataAiServices';
import WebWhatsappServices from '../services/WebWhatsappServices';
import metaServices from '../services/MetaServices';
import { AxiosError } from 'axios';
import errorResponse from '../schema/errorResponse';
import { ResponseSchema } from '../schema/types/ResponseSchema';
import ChatRoom from '../model/ChatRoom';
import { IChatRoomModel } from '../types/firestore/i_chat_room_model';

const fileUpload = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export interface SendMessageHandlerReqBody {
  roomPath: string;
  imageUrl?: string;
  documentUrl?: string;
  documentName?: string;
  text?: string;
  templatePath?: string;
  adminSessionPath: string;
  phoneNumber: string;
}

export default class SendMessageHandler {
  public static middlewares = [
    authChecker,
    fileUpload.single('media'),
    body('roomPath').notEmpty(),
    body('imageUrl').optional(),
    body('documentUrl').optional(),
    body('documentName').optional(),
    body('text')
      .optional()
      .customSanitizer(input => (!input ? '' : input))
      .trim(),
    body('templatePath').optional(),
    body('adminSessionPath').notEmpty(),
    body('phoneNumber').notEmpty(),
    requestChecker,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, SendMessageHandlerReqBody>,
    res: Response<
      any,
      {
        account: IFirestoreAdminEntity;
      }
    >
  ) {
    const roomRef = myFirestore.doc(req.body.roomPath);

    let messageId = v4();

    const rawText = req.body.text ?? '';

    const [
      sanitized,
      fullSanitized,
      roomGet,
      lastInboundMessage,
      file,
      getAdminSession,
      projectGet,
    ] = await Promise.all([
      new Promise<string>(resolve => {
        const sanitized =
          sanitizeHtml(rawText, {
            allowedTags: ['br'],
          }) ?? '';
        resolve(sanitized);
      }),

      new Promise<string>(resolve => {
        const sanitized =
          sanitizeHtml(rawText, {
            allowedTags: [],
          }) ?? '';
        resolve(sanitized);
      }),

      // Get Room
      new Promise<ChatRoom | null>(resolve => {
        roomRef
          .withConverter(ChatRoom.converter)
          .get()
          .then(snapshot => {
            if (snapshot.exists) {
              resolve(snapshot.data()!);
            } else {
              resolve(null);
            }
          });
      }),

      // Get Last Inbound Chat
      new Promise<firestore.QuerySnapshot<firestore.DocumentData>>(resolve => {
        roomRef
          .collection('chats')
          .where('message.direction', '==', 'IN')
          .orderBy('message.timestamp', 'desc')
          .limit(1)
          .get()
          .then(snapshot => {
            resolve(snapshot);
          });
      }),

      // Upload File If Exists - Disimpan dulu di bucket kata.ai
      new Promise<GoogleStorageFile | undefined>((resolve, reject) => {
        if (req.file) {
          const file = bucketKataAi.file(
            'message-images/' + roomRef.id + '/' + v4() + path.extname(req.file.originalname)
          );
          file
            .save(req.file.buffer, {
              public: true,
            })
            .then(() => {
              resolve(file);
            })
            .catch(() => {
              reject(new Error('Failed to upload file'));
            });
        } else {
          resolve(undefined);
        }
      }),

      // Get Admin Session
      new Promise<firestore.DocumentSnapshot<firestore.DocumentData>>(resolve => {
        const adminSession = myFirestore.doc('/' + req.body.adminSessionPath);
        adminSession.get().then(resolve);
      }),

      // Get Project
      new Promise<firestore.DocumentSnapshot<firestore.DocumentData>>(resolve => {
        const projectRef = roomRef.parent.parent;
        if (projectRef) {
          projectRef.get().then(resolve);
        }
      }),
    ]);
    let textSendToProvider = '';
    const textSaveToFirestore = sanitized;

    // Disable dulu untuk proses mengirim trid ketika ada domain amartahonda.com
    // const regex = /https?:\/\/(www\.)?amartahonda\.com\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/gm;
    // let trId= "";
    // if(req.body.text?.match(regex)) {
    //     const get = await profileService.getProfile(req.body.phoneNumber);
    //     trId = get.data[0].TrackingId
    // }

    await Promise.all([
      new Promise(resolve => {
        textSendToProvider = sanitized
          .replace(/(<br>|<\/br>|<br \/>)/gim, '\n')
          .replace(new RegExp('&amp;', 'g'), '&');

        // const matches = textSendToProvider.match(regex);
        // matches?.forEach(string => {
        //     const url = new URL(string);
        //     url.searchParams.set("trid", trId);
        //     textSendToProvider = textSendToProvider.replace(string, `${url.href}`)
        // });
        resolve(true);
      }),

      // new Promise(resolve => {
      //     const matches = sanitized.match(regex);
      //     matches?.forEach(string => {
      //         const url = new URL(string);
      //         url.searchParams.set("trid", trId);
      //         textSaveToFirestore = sanitized.replace(string, `${url.href}`)
      //     });
      //     resolve(true);
      // })
    ]);

    if (!roomGet) {
      return res.status(404).send({
        success: false,
        messages: 'Room not found',
      });
    }

    const roomData = roomGet;

    let sessionId = v4();
    if (lastInboundMessage.size === 1) {
      lastInboundMessage.forEach(result => {
        const data = result.data() as IFirestoreMessageEntity;
        if (
          moment((data.message.timestamp as firestore.Timestamp).toDate())
            .add(24, 'hours')
            .isAfter(moment())
        ) {
          if (data.session_id) sessionId = data.session_id;
        }
      });
    }

    const fileImageUrl = file ? file.publicUrl() : req.body.imageUrl;

    const project = projectGet.data()!;

    try {
      if (project.provider === 'qiscus') {
        const send = await qiscusServiceNew.sendMessage({
          text: textSendToProvider,
          imageUrl: fileImageUrl,
          documentUrl: req.body.documentUrl,
          documentName: req.body.documentName,
          target: roomData.contacts[0],
        });
        messageId = send.data.messages[0].id;
      } else if (project.provider === 'kata.ai') {
        await kataAiServices
          .sendMessage({
            text: textSendToProvider,
            imageUrl: fileImageUrl,
            target: roomData.contacts[0],
          })
          .then(value => {
            messageId = value.data.messages[0].id;
          });
      } else if (project.provider === 'web-whatsapp-js') {
        await WebWhatsappServices.sendMessage({
          chatId: roomData.contacts[0],
          text: textSendToProvider,
        }).then(value => {
          messageId = value.data.data.id.id;
        });
      } else if (project.provider === 'meta') {
        await metaServices
          .sendMessage(
            {
              text: textSendToProvider,
              imageUrl: fileImageUrl,
              documentUrl: req.body.documentUrl,
              documentName: req.body.documentName,
              target: roomData.contacts[0],
            },
            project.meta
          )
          .then(value => {
            messageId = value.data.messages[0].id;
          })
          .catch(reason => {
            const error = reason as AxiosError;
            return res.status(500).send(
              errorResponse({
                type: 'SERVER_ERROR',
                messages: error.response?.data || reason.toString(),
              })
            );
          });
      }
    } catch (e: any) {
      console.log(e);
      file?.delete();
      return res.status(500).send({
        success: false,
        messages: e,
      });
    }

    const messageParams: IFirestoreMessageEntity = {
      message: {
        id: messageId,
        direction: 'OUT',
        unixtime: new Date().getTime() / 1000,
        timestamp: firestore.Timestamp.now(),
        type: 'text',
      },
      statuses: {
        delivered: null,
        read: null,
        sent: firestore.Timestamp.now(),
        failed: null,
      },
      origin: {
        display_name: res.locals.account.name,
        id: res.locals.account.ref!.id,
        reference: res.locals.account!.ref as firestore.DocumentReference,
      },
      session_id: sessionId,
    };

    const recentChat: IChatRoomModel['recent_chat'] = {
      contact: res.locals.account.email ?? 'Admin',
      direction: 'OUT',
      display_name: 'Admin',
      read: false,
      statuses: {
        failed: null,
        sent: firestore.Timestamp.now(),
        read: null,
        delivered: null,
      },
      text: '',
      timestamp: firestore.Timestamp.now(),
      type: 'text',
      unixtime: moment().unix(),
    };

    if (fileImageUrl) {
      messageParams.message.image = {
        link: fileImageUrl,
        caption: textSaveToFirestore || '',
      };
      messageParams.message.type = 'image';
      recentChat.text = '(Gambar) ' + fullSanitized;
    }
    if (req.body.documentUrl) {
      messageParams.message.document = {
        link: req.body.documentUrl,
        caption: textSaveToFirestore || '',
        filename: req.body.documentName || 'File Dokumen',
      };
      messageParams.message.type = 'document';
      recentChat.text = '(Dokumen) ' + fullSanitized;
    } else {
      messageParams.message.text = {
        body: textSaveToFirestore ?? '',
      };

      recentChat.text = fullSanitized;
    }

    if (lastInboundMessage.size === 1) {
      lastInboundMessage.forEach(result => {
        const data = result.data() as IFirestoreMessageEntity;
        if (
          !moment((messageParams.message.timestamp as firestore.Timestamp).toDate()).isBefore(
            moment((data.message.timestamp as firestore.Timestamp).toDate()).add(24, 'hours')
          )
        ) {
          messageParams.error = {
            href: '',
            title:
              'Message failed to send because more than 24 hours have passed since the customer last replied to this number',
            code: 402,
          };
        }
      });
    }

    const clientSessionDoc = myFirestore.collection('client_session_logs').doc(sessionId);
    const getClientSessionDoc = await clientSessionDoc.get();

    const batch = myFirestore.batch();

    const messageRef = await firestoreService.addNewMessage(
      messageParams,
      roomRef,
      messageId,
      batch
    );
    await firestoreService.updateRoomRecentChat(recentChat, roomRef, batch);

    if (!messageParams.error) {
      if (getAdminSession.exists) {
        const dataAdminSession = getAdminSession.data() as IAdminSessionLog;
        batch.update(getAdminSession.ref, {
          messages: [
            ...dataAdminSession.messages,
            {
              origin: messageParams.origin,
              message: messageParams.message,
              ref: messageRef,
            },
          ],
        });
      }
    }

    if (getClientSessionDoc.exists) {
      const sessionLog = getClientSessionDoc.data() as ISessionLog<ISessionLogDataClient>;
      sessionLog.messages.push({
        message: messageParams.message,
        origin: messageParams.origin,
      });

      batch.update(clientSessionDoc, {
        messages: sessionLog.messages,
      });
    }

    // if (req.body.templatePath) {
    //     await firestoreService.updateTemplateSendCounter(myFirestore.doc(req.body.templatePath), batch);
    // }

    try {
      await batch.commit();
    } catch (e: any) {
      file?.delete();
      return res.status(500).send({
        success: false,
        messages: e.toString(),
      });
    }

    res.send({
      success: true,
    });
  }
}
