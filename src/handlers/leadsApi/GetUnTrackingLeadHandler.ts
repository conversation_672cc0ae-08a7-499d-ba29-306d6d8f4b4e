import { Request, Response } from 'express';
import { param } from 'express-validator';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import requestValidator from '../../middlewares/requestValidator';
import LeadsModel from '../../model/LeadsModel';
import successResponse from '../../schema/successResponse';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';

const getUntrackingLeadHandler = {
  middlewares: [
    leadsAuthorizer,
    param('agentCode').notEmpty().trim(),
    param('organization').notEmpty().trim(),
    requestValidator,
  ],
  handler: async function (
    req: Request<{
      agentCode: string;
      organization: string;
    }>,
    res: Response<ResponseSchema>
  ) {
    const collections = myFirestore.collection('leads');
    const getQuery = await collections
      .where('agentCode', '==', req.params.agentCode)
      .where('organization', '==', req.params.organization)
      .where('isTracking', '==', false)
      .orderBy('createdAt', 'desc')
      .withConverter(LeadsModel.converter)
      .get();

    const leads: LeadsModel[] = [];

    getQuery.forEach(result => {
      const data = result.data();
      leads.push(data);
    });

    res.send(
      successResponse({
        data: leads.map(value => value.toJsonResponse()),
        type: 'FETCHED',
      })
    );
  },
};

export default getUntrackingLeadHandler;
