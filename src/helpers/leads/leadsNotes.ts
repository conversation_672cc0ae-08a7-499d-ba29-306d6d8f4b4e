import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import moment from 'moment/moment';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';

async function leadsNotesCounter(leads: LeadsModel) {
  const leadsNotesCollection = myFirestore.collection('leads_notes');

  const notesFromFirestore: ILeadsNotesDocument[] = [];
  const find = await leadsNotesCollection
    .where('phoneNumber', '==', leads.phoneNumber)
    .where('organization', '==', leads.organization)
    .orderBy('updatedAt', 'desc')
    .get();

  find.forEach(result => {
    const data = result.data() as ILeadsNotesDocument;
    notesFromFirestore.push(data);
  });

  const offsetReactivateTrack = moment('2023-07-04', 'YYYY-MM-DD');

  const totalNotes = find.size;
  const totalReactivate = notesFromFirestore.filter(value => {
    const notesDate = moment(value.updatedAt.toDate());
    if (notesDate.isBefore(offsetReactivateTrack)) {
      return value.event === 'track';
    } else if (notesDate.isSameOrAfter(offsetReactivateTrack)) {
      return value.event === 'reactivateTrack';
    }
  }).length;

  return {
    totalNotes,
    totalReactivate,
  };
}

export { leadsNotesCounter };
