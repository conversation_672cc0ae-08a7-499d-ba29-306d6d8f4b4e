import { Router } from 'express';
import GetMessageHandler from '../handlers/publicApi/getMessage/getMessageHandler';
import GetSumMessagesHandler from '../handlers/publicApi/getMessage/getSumMessagesHandler';
import leadFailNotification from '../handlers/webhook_and_cron/leads/leadFailNotification';
import getDataByPhoneNumber from '../handlers/publicApi/getDataByPhoneNumber';
import storeClientRejectedFromLeasing from '../handlers/publicApi/storeClientRejectedFromLeasing';
import createUserHandler from '../handlers/publicApi/user/createUser.handler';
import deleteUserHandler from '../handlers/publicApi/user/deleteUser.handler';
import getAdminHandler from '../handlers/publicApi/user/getAdmin.handler';
import whatsappExportedChatParserHandler from '../handlers/api_test/whatsappExportedChatParser.handler';
import uploadMediaHandler from '../handlers/publicApi/media/uploadMediaHandler';
import getProjectsHandler from '../handlers/publicApi/project/getProjects.handler';
import getMediaHandler from '../handlers/publicApi/media/getMediaHandler';

const router = Router();

export default function (parent: Router) {
  parent.use('/api', router);

  router.get('/get-messages', GetMessageHandler.middlewares, GetMessageHandler.handler as any);
  router.get(
    '/get-message-order-confirm',
    GetSumMessagesHandler.middlewares,
    GetSumMessagesHandler.handler as any
  );

  router.post(
    '/notify-leads-fail',
    leadFailNotification.middlewares,
    leadFailNotification.handler as any
  );

  router.get(
    '/data-by-phone-number',
    getDataByPhoneNumber.middlewares,
    getDataByPhoneNumber.handler as any
  );

  router.post(
    '/send-template-cdb',
    storeClientRejectedFromLeasing.middlewares,
    storeClientRejectedFromLeasing.handler
  );

  router.post('/create-admin', createUserHandler.middlewares, createUserHandler.handler);
  router.post('/delete-admin', deleteUserHandler.middlewares, deleteUserHandler.handler);
  router.get('/get-admin', getAdminHandler.middlewares, getAdminHandler.handler);

  router.post(
    '/whatsapp-exported-chat-parser',
    whatsappExportedChatParserHandler.middlewares,
    whatsappExportedChatParserHandler.handler
  );

  router.post('/media/upload', uploadMediaHandler.middlewares, uploadMediaHandler.handler);
  router.get('/media', getMediaHandler.middlewares, getMediaHandler.handler);
  router.get('/projects', getProjectsHandler.middlewares, getProjectsHandler.handler);
}
