import axios from 'axios';
import { SuccessResponseCheckSession } from '../types/services/webWhatsapp/SuccessResponseCheckSession.types';
import { SuccessResponseGetLeadsChat } from '../types/services/webWhatsapp/SuccessResponseGetLeadsChat.types';

class WebWhatsappServices {
  private base = axios.create({
    baseURL: 'http://*************:8080',
    // baseURL: "http://localhost:8080",
  });

  async sendMessage(params: { chatId: string; text: string }) {
    return await this.base.post('/send-message/' + params.chatId, {
      message: params.text,
    });
  }

  async checkActiveInstance() {
    return await this.base.get<SuccessResponseCheckSession>('/');
  }

  async checkPhoneNumberActive(phoneNumber: string) {
    return await this.base.get('/check-phone-number', {
      params: {
        phoneNumber: phoneNumber,
      },
      timeout: 3000,
    });
  }

  async getChatFromLeads(params: { agentCode: string; leadsPhoneNumber: string }) {
    return await this.base.get<SuccessResponseGetLeadsChat>(
      `/message/${params.agentCode}/${params.leadsPhoneNumber}`,
      {
        timeout: 5 * 1000,
      }
    );
  }
}

export default new WebWhatsappServices();
