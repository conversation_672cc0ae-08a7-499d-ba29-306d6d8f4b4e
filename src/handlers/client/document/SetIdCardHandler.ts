import { EMaritalStatus } from '../../../types/client/document/marital-status-types';
import { CommonRegionFields } from '../../../types/client/document/region-types';
import { Request, Response } from 'express';
import { body } from 'express-validator';
import multer from 'multer';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { ImageCaptureTypes } from '../../../types/client/document/capture-types';
import { myFirestore } from '../../../services/firebaseAdmin';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import requestChecker from '../../../middlewares/requestValidator';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import { File } from '@google-cloud/storage';
import { IdCardFields } from '../../../types/services/profile_service_types';
import { profileService } from '../../../services/profileService';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';

export interface IdCardCaptureFields {
  idCardNumber: string;
  fullName: string;
  birthMother: string;
  placeOfBirth: string;
  dateOfBirth: Date;
  occupation: string;
  maritalStatus: EMaritalStatus;
  lastEducation: string;

  fullAddress: string;
  zipCode: string;
  province: CommonRegionFields;
  city: CommonRegionFields;
  district: CommonRegionFields;
  subDistrict: CommonRegionFields;
  neighbourhood: string;
  hamlet: string;

  domicileFullAddress: string;
  domicileZipCode: string;
  domicileProvince: CommonRegionFields;
  domicileCity: CommonRegionFields;
  domicileDistrict: CommonRegionFields;
  domicileSubDistrict: CommonRegionFields;
  domicileNeighbourhood: string;
  domicileHamlet: string;

  phoneNumber: string;

  clientRef: string;
  type:
    | ImageCaptureTypes.ID_CARD_OWNER
    | ImageCaptureTypes.ID_CARD_GUARANTOR
    | ImageCaptureTypes.ID_CARD_ORDER_MAKER;

  imageUrl?: string;
  sameAsOwnerIdCard?: boolean;
}

export interface UpdateIdCardHandlerReqBody extends IdCardCaptureFields {}

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export default class SetIdCardHandler {
  public static middlewares = [
    formDataParse.single('image'),

    body('clientRef').notEmpty(),
    body('type').notEmpty(),

    body('phoneNumber')
      .notEmpty()
      .customSanitizer(input => {
        return phoneNumberCountryCodeSanitizer(input, '62', '62');
      }),
    body('idCardNumber')
      .trim()
      .notEmpty()
      .isNumeric()
      .isLength({ max: 16, min: 16 })
      .withMessage('KTP harus 16 digit'),
    body('fullName').trim().notEmpty(),
    body('placeOfBirth').trim().notEmpty(),
    body('dateOfBirth').trim().notEmpty().toDate(),
    body('occupation').trim().notEmpty(),
    body('maritalStatus').trim().notEmpty(),

    body('fullAddress').trim().notEmpty(),
    body('zipCode')
      .trim()
      .notEmpty()
      .isLength({ max: 5, min: 5 })
      .withMessage('Kode Pos harus 16 digit'),
    body('province.code').trim().notEmpty(),
    body('province.name').trim().notEmpty(),
    body('city.code').trim().notEmpty(),
    body('city.name').trim().notEmpty(),
    body('district.code').trim().notEmpty(),
    body('district.name').trim().notEmpty(),
    body('subDistrict.code').trim().notEmpty(),
    body('subDistrict.name').trim().notEmpty(),
    body('neighbourhood')
      .trim()
      .isNumeric()
      .withMessage('RT Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('RT Maksimal memiliki 3 digit')
      .notEmpty(),
    body('hamlet')
      .trim()
      .isNumeric()
      .withMessage('RW Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('RW Maksimal memiliki 3 digit')
      .notEmpty(),

    body('birthMother').optional(),
    body('lastEducation').optional(),

    body('domicileProvince.code').optional().trim().notEmpty(),
    body('domicileProvince.name').optional().trim().notEmpty(),
    body('domicileCity.code').optional().trim().notEmpty(),
    body('domicileCity.name').optional().trim().notEmpty(),
    body('domicileDistrict.code').optional().trim().notEmpty(),
    body('domicileDistrict.name').optional().trim().notEmpty(),
    body('domicileSubDistrict.code').optional().trim().notEmpty(),
    body('domicileSubDistrict.name').optional().trim().notEmpty(),
    body('domicileZipCode')
      .optional()
      .trim()
      .isLength({ max: 5, min: 5 })
      .withMessage('Kode Pos harus 16 digit'),
    body('domicileNeighbourhood')
      .optional()
      .trim()
      .isNumeric()
      .withMessage('Domisili RT Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('Domisili RT Maksimal memiliki 3 digit'),
    body('domicileHamlet')
      .optional()
      .trim()
      .isNumeric()
      .withMessage('Domisili RW Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('Domisili RW Maksimal memiliki 3 digit'),

    body('imageUrl').optional(),

    body('sameAsOwnerIdCard').optional().toBoolean(),
    requestChecker,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, UpdateIdCardHandlerReqBody>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);
    let phoneNumberIdCardField = '';

    switch (req.body.type) {
      case ImageCaptureTypes.ID_CARD_OWNER:
        phoneNumberIdCardField = 'details.owner_phone_number';
        break;
      case ImageCaptureTypes.ID_CARD_GUARANTOR:
        phoneNumberIdCardField = 'details.guarantor_phone_number';
        break;
      case ImageCaptureTypes.ID_CARD_ORDER_MAKER:
        phoneNumberIdCardField = 'details.order_maker_phone_number';
        break;
    }

    let file: File | null = null;
    if (req.file?.buffer) {
      file = bucketTrimitraIdeal.file(
        `client-documents/${clientDocRef.id}/${req.body.type.toLowerCase()}.jpg`
      );
    }

    try {
      if (file) {
        if (req.file) {
          await file.save(req.file.buffer, {
            public: true,
          });
        }
      }

      const getProfile = await profileService.getProfile(req.body.phoneNumber);
      if (getProfile.data.length === 0) {
        await profileService.updateProfile(
          {
            customerName: req.body.fullName,
            phone: req.body.phoneNumber,
          },
          req.body.phoneNumber
        );
      }

      const idCardPayload: IdCardFields = {
        DateOfBirth: req.body.dateOfBirth,
        FullName: req.body.fullName.toUpperCase(),
        IdCardNumber: req.body.idCardNumber,
        PlaceOfBirth: req.body.placeOfBirth,
        MaritalStatus: req.body.maritalStatus,
        Occupation: req.body.occupation,

        IdCardImage: file?.publicUrl() ?? req.body.imageUrl,
        // IdCardImage: "https://storage.googleapis.com/chat-trimitra-ideal/client-documents/7e9ca9cf-0f1d-4fda-8fe2-79867d5d8a3d/id_card_order_maker.jpg",

        FullAddress: req.body.fullAddress.toUpperCase(),
        Province: {
          Code: req.body.province.code,
          Name: req.body.province.name.toUpperCase(),
        },
        City: {
          Code: req.body.city.code,
          Name: req.body.city.name.toUpperCase(),
        },
        District: {
          Code: req.body.district.code,
          Name: req.body.district.name.toUpperCase(),
        },
        SubDistrict: {
          Code: req.body.subDistrict.code,
          Name: req.body.subDistrict.name.toUpperCase(),
        },
        ZipCode: req.body.zipCode,
        Neighbourhood: req.body.neighbourhood,
        Hamlet: req.body.hamlet,

        BirthMother: req.body.birthMother?.toUpperCase() ?? null,
        LastEducation: req.body.lastEducation ?? null,

        DomicileFullAddress: req.body.domicileFullAddress?.toUpperCase() ?? '',
        DomicileProvince: req.body.domicileProvince
          ? {
              Code: req.body.domicileProvince.code,
              Name: req.body.domicileProvince.name.toUpperCase(),
            }
          : null,
        DomicileCity: req.body.domicileCity
          ? {
              Code: req.body.domicileCity.code,
              Name: req.body.domicileCity.name.toUpperCase(),
            }
          : null,
        DomicileDistrict: req.body.domicileCity
          ? {
              Code: req.body.domicileDistrict.code,
              Name: req.body.domicileDistrict.name.toUpperCase(),
            }
          : null,
        DomicileSubDistrict: req.body.domicileSubDistrict
          ? {
              Code: req.body.domicileSubDistrict.code,
              Name: req.body.domicileSubDistrict.name.toUpperCase(),
            }
          : null,
        DomicileZipCode: req.body.domicileZipCode ?? '',
        DomicileNeighbourhood: req.body.domicileNeighbourhood ?? '',
        DomicileHamlet: req.body.domicileHamlet ?? '',
      };

      const update = await profileService.updateIdCard(
        {
          ...idCardPayload,
        },
        req.body.phoneNumber
      );

      const preprocessFirebaseData: any = {
        [phoneNumberIdCardField]: req.body.phoneNumber,
      };

      if (req.body.type === ImageCaptureTypes.ID_CARD_ORDER_MAKER) {
        preprocessFirebaseData['details.guarantor_phone_number'] = req.body.phoneNumber;
      }

      if (!!req.body.sameAsOwnerIdCard)
        preprocessFirebaseData['details.owner_phone_number'] = req.body.phoneNumber;

      await clientDocRef.update({
        ...preprocessFirebaseData,
      });

      res.send(
        successResponse({
          type: 'UPDATED',
          data: update?.data,
        })
      );
    } catch (e: any) {
      if (file)
        await file.delete({
          ignoreNotFound: true,
        });
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
