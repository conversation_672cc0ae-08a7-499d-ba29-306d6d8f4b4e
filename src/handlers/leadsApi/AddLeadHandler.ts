import { json, Request, Response } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import { myFirestore } from '../../services/firebaseAdmin';
import requestValidator from '../../middlewares/requestValidator';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import LeadsModel from '../../model/LeadsModel';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';
import fetchAgent from '../../services/agent/fetchAgent';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import leadsAlreadyAcquiredCheck from '../../middlewares/leads/leadsAlreadyAcquiredCheck';
import { firestore } from 'firebase-admin';

interface ReqBody {
  agentCode: string;
  agentName: string;
  title: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  provinceName: string;
  provinceCode: string;
  cityName: string;
  cityCode: string;
  vehicleUsage: 'individual' | 'shared' | 'corporate';
  paymentPlan: 'cash' | 'credit';
  hasVehicleLoan: boolean;
  vehicleOptions: {
    brand: {
      name: string;
    };
    model: {
      name: string;
    };
    variant?: {
      code?: string;
      name?: string;
    } | null;
    color?: {
      code?: string;
      name?: string;
    } | null;
  }[];
  source: string;
  source2: string;
  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition';
  nextTotalVehicleOwnerShip: string;
  notes: string;
  idealChatRoomRef: string;

  idCard_number: string | null;
  driverLicense_number: string | null;
}

const addLeadHandler = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('agentCode').notEmpty().trim().withMessage('Kode Agen dibutuhkan'),
    body('agentName').optional(),
    body('title').notEmpty().withMessage('Title is required'),
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName')
      .optional()
      .customSanitizer(input => (!input ? '' : input)),
    body('phoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('email').optional({ values: 'falsy' }).isEmail().withMessage('Invalid email address'),
    body('provinceName').notEmpty().withMessage('Province name is required'),
    body('provinceCode').notEmpty().withMessage('Province code is required'),
    body('cityName').notEmpty().withMessage('City name is required'),
    body('cityCode').notEmpty().withMessage('City code is required'),
    body('vehicleUsage')
      .notEmpty()
      .withMessage('Vehicle usage is required')
      .isIn(['individual', 'shared', 'corporate'])
      .withMessage('Invalid vehicle usage'),
    body('paymentPlan')
      .notEmpty()
      .withMessage('Payment plan is required')
      .isIn(['cash', 'credit'])
      .withMessage('Invalid payment plan'),
    body('hasVehicleLoan')
      .optional()
      .default(false)
      .isBoolean()
      .withMessage('Invalid hasVehicleLoan format'),
    body('vehicleOptions')
      .isArray({ min: 1 })
      .withMessage('At least one vehicle option is required')
      .custom((value: ReqBody['vehicleOptions']) => {
        for (const option of value) {
          if (!option.brand?.name || !option.model?.name) {
            throw new Error('Brand name and model name are required');
          }
          // Variant and color are now optional, no validation needed
        }
        return true;
      }),
    body('source2').notEmpty().withMessage('Source2 is required'),
    body('source').notEmpty().withMessage('Source is required'),
    body('purchasePlan')
      .notEmpty()
      .isIn(['firstVehicle', 'vehicleReplacement', 'vehicleAddition'])
      .withMessage('purchasePlan is required'),
    body('nextTotalVehicleOwnerShip').notEmpty(),
    body('notes')
      .notEmpty()
      .withMessage('Notes dibutuhkan')
      .isLength({ min: 30, max: 600 })
      .withMessage('Catatan Minimal 30 s.d 600 karakter.'),
    body('idealChatRoomRef').optional(),
    body('idCard_number')
      .optional()
      .customSanitizer(input => (!input ? null : input)),
    body('driverLicense_number')
      .optional()
      .customSanitizer(input => (!input ? null : input)),

    requestValidator,

    leadsAlreadyAcquiredCheck,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response) => {
    const organization = req.body.source2;
    const collections = myFirestore.collection('leads');
    const docRef = collections.doc(`${organization}-${req.body.phoneNumber}`);

    let agentName: string | null;
    let agentPhoneNumbers: string[] = [];

    try {
      const fetch = await fetchAgent(req.body.agentCode);
      agentName = fetch.name;
      agentPhoneNumbers = fetch.phones.map(value => value.phone);
    } catch (e: any) {
      agentName = 'NO_NAME';
    }

    const notes: ILeadsNotesDocument<Date> = {
      notes: req.body.notes,
      updatedAt: new Date(),
      updatedByUser: null,
      statusLevel: 0,
      agentCode: req.body.agentCode,
      event: 'init',
      phoneNumber: req.body.phoneNumber,
      organization: req.body.source2,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      agentName: agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: 0,
      },
      totalUpdateNotes: 1,
    };

    const leadsDoc = new LeadsModel({
      agentCode: req.body.agentCode,
      agentName: agentName,
      phoneNumberAgent: agentPhoneNumbers,
      createdAt: new Date(),
      domicile: {
        cityCode: req.body.cityCode,
        cityName: req.body.cityName,
        provinceCode: req.body.provinceCode,
        provinceName: req.body.provinceName,
      },
      email: req.body.email,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      hasVehicleLoan: req.body.hasVehicleLoan,
      paymentPlan: req.body.paymentPlan,
      downPaymentPlan: null,
      phoneNumber: req.body.phoneNumber,
      ref: docRef,
      source: req.body.source,
      organization: req.body.source2 as any,
      title: req.body.title,
      vehicleOptions: req.body.vehicleOptions.map(value => {
        return {
          brand: {
            name: value.brand.name,
          },
          model: {
            name: value.model.name,
          },
          variant: {
            name: value.variant?.name || '',
            code: value.variant?.code || '',
          },
          color: {
            name: value.color?.name || '',
            code: value.color?.code || '',
          },
        };
      }),
      vehicleUsage: req.body.vehicleUsage,
      statusLevel: 0,

      purchasePlan: req.body.purchasePlan,
      nextTotalVehicleOwnerShip: req.body.nextTotalVehicleOwnerShip,

      isTracking: true,
      notes: req.body.notes,

      updateHistories: [
        {
          ...notes,
        },
      ],

      idCard_number: req.body.idCard_number ?? null,
      driverLicense_number: req.body.driverLicense_number ?? null,
    });

    try {
      const batch = myFirestore.batch();
      batch.set(leadsDoc.ref.withConverter(LeadsModel.converter), leadsDoc);
      batch.set(myFirestore.collection('leads_notes').doc(), notes);
      await batch.commit();

      await aggregatesAgent(req.body.agentCode, organization as any);
    } catch (e: any) {
      console.log('ERROR_ADD_LEADS', e.toString(), JSON.stringify(e), JSON.stringify(req.body));
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }

    if (req.body.source === 'triforce-qrcode') {
      try {
        const getClient = await myFirestore
          .collection('clients')
          .where('contacts.whatsapp', '==', req.body.phoneNumber)
          .get();

        if (!getClient.empty) {
          getClient.forEach((doc: any) => {
            doc.ref.update({
              acquiredLeadsStatus: {
                agentCode: req.body.agentCode,
                agentName: agentName,
                acquired: true,
                acquiredAt: firestore.Timestamp.fromDate(new Date()),
                organization: req.body.source2,
              },
            });
          });
        }
      } catch (e: any) {
        console.log(
          'ERROR_UPDATE_CLIENT_ACQUIRED_LEADS',
          e.toString(),
          JSON.stringify(e),
          JSON.stringify(req.body)
        );
      }
    }

    res.send(
      successResponse({
        data: leadsDoc.toJsonResponse(),
        type: 'CREATED',
      })
    );
  },
};

export default addLeadHandler;
