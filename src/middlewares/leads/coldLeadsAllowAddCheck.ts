import { NextFunction, Request, Response } from 'express';
import { myFirestore } from '../../services/firebaseAdmin';
import ColdLeadsModel from '../../model/ColdLeadsModel';
import moment from 'moment/moment';
import errorResponse from '../../schema/errorResponse';

async function coldLeadsAllowAddCheck(req: Request, res: Response, next: NextFunction) {
  const collection = myFirestore.collection('cold_leads');
  const get = await collection
    .doc(`${req.body.organization}-${req.body.phoneNumber}`)
    .withConverter(ColdLeadsModel.converter)
    .get();

  if (!get.exists) {
    next();
  } else {
    let data: ColdLeadsModel = get.data()!;
    const offsetDay = 45;
    const movedToColdAt = data.moveToColdAt;
    const offsetDateMoment = moment(movedToColdAt).add(offsetDay, 'days');
    const momentNow = moment();

    if (momentNow.isBefore(offsetDateMoment)) {
      return res.status(500).send(
        errorResponse({
          type: 'OTHER_ERROR',
          messages: `Nomor telepon ini belum dapat diakuisisi kembali`,
          data: JSON.stringify({
            phoneNumber: req.body.phoneNumber,
            organization: req.body.source2,
          }),
        })
      );
    } else {
      next();
    }
  }
}

export default coldLeadsAllowAddCheck;
