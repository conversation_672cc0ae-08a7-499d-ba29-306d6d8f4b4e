import { firestore } from 'firebase-admin';

export interface RawLeads<DATE = firestore.Timestamp, DocRef = firestore.DocumentReference> {
  externalId: string;
  phoneNumber: string;
  fullName: string;
  city: string;
  vehicleModel: string;
  vehicleVariant: string;
  organization: string;
  externalData:
    | {
        vehicle: {
          variantId: string;
        };
        city: {
          slug: string;
          name: string;
        };
      }
    | any;
  createdAt: DATE;
  duplicated: boolean;
  mappedData: {
    domicile: {
      provinceName: string;
      provinceCode: string;
      cityName: string;
      cityCode: string;
    };
    vehicle: {
      color: {
        code: string;
        name: string;
      };
      brand: {
        name: string;
      };
      variant: {
        name: string;
        code: string;
      };
      model: {
        name: string;
      };
    };
    area: string;
    paymentPlan?: null | 'credit' | 'cash';
  };

  whatsapp?: {
    idealPath: DocRef | null;
    messageId: string;
    statuses: {
      delivered: DATE;
      read: DATE;
    };
    response?: {
      text: string;
      repliedAt: string;
    } | null;
  };

  inFreeLeads: boolean;
  freeLeadsPath: DocRef | null;
  freeLeadsCreatedAt: DATE | null;

  shouldPushToFreeLeadsAt: DATE;

  shouldPushToBigQueryAt: DATE | null;
  donePushToBigQuery: boolean;
  donePushToBigQueryAt: DATE | null;

  alreadyInMyLeads: boolean;

  source: string;

  id: string;
}
