import { json, Request, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import moment, { Moment } from 'moment';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import hidePhoneNumber from '../../helpers/hidePhoneNumber';
import telegramServices from '../../services/telegramServices';
import successResponse from '../../schema/successResponse';
import { leadsNotesCounter } from '../../helpers/leads/leadsNotes';

interface ReqBody {
  leadsPhoneNumber: string;

  idTestDrive: string;
  date: Moment;
  model: string;

  notes: string;
  organization: string;
}

const addLeadsTestDrive = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('leadsPhoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('idTestDrive').notEmpty(),
    body('date')
      .notEmpty()
      .custom(input => {
        const m = moment(input, 'YYYY-MM-DD HH:mm');
        return m.isValid();
      })
      .customSanitizer(input => {
        return moment(input, 'YYYY-MM-DD HH:mm');
      }),
    body('model').notEmpty(),
    body('notes').optional(),
    body('organization').optional(),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response) => {
    const organization = req.body.organization || 'amartachery';
    const collection = myFirestore.collection('leads');
    const getLeads = await collection
      .withConverter(LeadsModel.converter)
      .doc(`${organization}-${req.body.leadsPhoneNumber}`)
      .get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    const { idTestDrive, date, model, notes } = req.body;

    const notesCounter = await leadsNotesCounter(leads);

    let _notes: ILeadsNotesDocument<Date> = {
      agentCode: leads.agentCode,
      event: 'updateTestDrive',
      notes: !notes
        ? `Leads sudah melakukan Test Drive model ${req.body.model.toUpperCase()} pada tanggal ${date.format('YYYY-MM-DD HH:mm')}. ID Test Drive: ${idTestDrive}`
        : notes,
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: null,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: leads.agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.testDrive = {
      idTestDrive: idTestDrive,
      date: date.toDate(),
      model: model,
      notes: _notes.notes,
    };

    leads.notes = _notes.notes;
    leads.updateHistories.push({
      ..._notes,
    });

    try {
      const batch = myFirestore.batch();
      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      if (notes) {
        batch.set(myFirestore.collection('leads_notes').doc(), _notes);
      }
      await batch.commit();

      // if (leads.organization === "amartachery") {
      //     let messageTelegram = `=== LEADS UPDATE NOTES - TEST DRIVE ===` +
      //         `\n` +
      //         `\nNama Depan: ${leads.firstName}` +
      //         `\nNama Akhir: ${leads.lastName}` +
      //         `\nNomor Telepon: ${hidePhoneNumber(leads.phoneNumber)}` +
      //         `\nNama Agen: ${leads.agentName}` +
      //         `\nKode Agen: ${leads.agentCode}` +
      //         `\nNotes: ${leads.notes}` +
      //         `\nModel: ${leads.vehicleOptions.map(value => value.model.name).join(", ")}` +
      //         `\nID Test Drive: ${idTestDrive}` +
      //         `\nModel Test Drive: ${model}`
      //     ;
      //
      //     telegramServices.sendMessage("-1002118167928", messageTelegram)
      //         .then()
      //         .catch()
      // }

      res.send(
        successResponse({
          type: 'UPDATED',
          data: req.body,
        })
      );
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }
  },
};

export default addLeadsTestDrive;
