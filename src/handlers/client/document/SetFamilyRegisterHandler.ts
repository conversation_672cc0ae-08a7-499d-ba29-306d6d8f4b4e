import { CommonRegionFields } from '../../../types/client/document/region-types';
import { EMaritalStatus } from '../../../types/client/document/marital-status-types';
import { EFamilyRelation } from '../../../types/firestore/family_registration_document_types';
import { NextFunction, Request, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import multer from 'multer';
import requestChecker from '../../../middlewares/requestValidator';
import successResponse from '../../../schema/successResponse';
import { myFirestore } from '../../../services/firebaseAdmin';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import errorResponse from '../../../schema/errorResponse';
import { File } from '@google-cloud/storage';
import { IFamilyRegisterFields } from '../../../types/services/profile_service_types';
import { profileService } from '../../../services/profileService';

export interface IFamilyRegisterCaptureFields {
  familyRegisterNumber: string;
  fullAddress: string;
  zipCode: string;

  province: CommonRegionFields;
  city: CommonRegionFields;
  district: CommonRegionFields;
  subDistrict: CommonRegionFields;

  members: FamilyMember[];

  imageUrl?: string;
}

export interface FamilyMember {
  idCardNumber: string;
  fullName: string;
  familyRelation: EFamilyRelation;
  maritalStatus: EMaritalStatus;
}

export interface FamilyRegisterReqBody extends IFamilyRegisterCaptureFields {
  clientRef: string;
}

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export default class SetFamilyRegisterHandler {
  public static middlewares = [
    formDataParse.single('image'),
    body('familyRegisterNumber')
      .notEmpty()
      .withMessage('Nomor Register Keluarga tidak boleh kosong')
      .isNumeric()
      .isLength({ min: 16, max: 16 })
      .withMessage('Nomor KK harus 16 digit'),
    body('fullAddress').notEmpty().withMessage('Alamat lengkap tidak boleh kosong'),
    body('zipCode').notEmpty().withMessage('Kode Pos tidak boleh kosong'),
    body('province.code').notEmpty().withMessage('Kode Provinsi tidak boleh kosong'),
    body('province.name').notEmpty().withMessage('Nama Provinsi tidak boleh kosong'),
    body('city.code').notEmpty().withMessage('Kode Kota tidak boleh kosong'),
    body('city.name').notEmpty().withMessage('Nama Kota tidak boleh kosong'),
    body('district.code').notEmpty().withMessage('Kode Kecamatan tidak boleh kosong'),
    body('district.name').notEmpty().withMessage('Nama Kecamatan tidak boleh kosong'),
    body('subDistrict.code').notEmpty().withMessage('Kode Kelurahan tidak boleh kosong'),
    body('subDistrict.name').notEmpty().withMessage('Nama Kelurahan tidak boleh kosong'),

    body('members').optional({ values: 'falsy' }).isArray(),
    body('members.*.idCardNumber').notEmpty(),
    body('members.*.fullName').notEmpty(),
    body('members.*.maritalStatus').notEmpty(),
    body('members.*.familyRelation').notEmpty(),

    body('imageUrl').optional(),

    body('clientRef').notEmpty(),
    requestChecker,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, FamilyRegisterReqBody>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.get();

    const payload: IFamilyRegisterFields = {
      FamilyRegisterNumber: req.body.familyRegisterNumber,
      FullAddress: req.body.fullAddress.toUpperCase(),
      Members: req.body.members?.map(value => {
        return {
          FamilyRelation: value.familyRelation,
          FullName: value.fullName.toUpperCase(),
          IdCardNumber: value.idCardNumber,
          MaritalStatus: value.maritalStatus,
        };
      }),
      ZipCode: req.body.zipCode,
      Province: {
        Code: req.body.province.code,
        Name: req.body.province.name.toUpperCase(),
      },
      City: {
        Code: req.body.city.code,
        Name: req.body.city.name.toUpperCase(),
      },
      District: {
        Code: req.body.district.code,
        Name: req.body.district.name.toUpperCase(),
      },
      SubDistrict: {
        Code: req.body.subDistrict.code,
        Name: req.body.subDistrict.name.toUpperCase(),
      },
    };

    let file: File | null = null;
    if (req.file?.buffer) {
      file = bucketTrimitraIdeal.file(`client-documents/${clientDocRef.id}/family-register.jpg`);
    }

    try {
      if (file) {
        if (req.file) {
          await file.save(req.file.buffer, {
            public: true,
          });
        }
        payload.FamilyRegisterImage = file.publicUrl();
      } else {
        payload.FamilyRegisterImage = req.body.imageUrl;
      }

      const update = await profileService.updateFamilyRegister(
        payload,
        getClient.data()!.details.owner_phone_number
      );

      res.send(
        successResponse({
          type: 'CREATED',
          data: update?.data,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
