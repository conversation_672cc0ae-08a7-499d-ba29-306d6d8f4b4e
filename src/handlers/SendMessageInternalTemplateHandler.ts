import { body } from 'express-validator';
import { json, Request, Response } from 'express';
import { myFirestore } from '../services/firebaseAdmin';
import requestChecker from '../middlewares/requestValidator';
import { v4 } from 'uuid';
import { firestoreService } from '../services/FirestoreService';
import { firestore } from 'firebase-admin';
import {
  IFirestoreAdminEntity,
  IFirestoreMessageEntity,
} from '../types/firestore/messages/message_types';
import authChecker from '../middlewares/authChecker';
import moment from 'moment';
import sanitizeHtml from 'sanitize-html';
import { ISessionLog, ISessionLogDataClient } from '../types/firestore/session_log_types';
import { IAdminSessionLog } from '../types/firestore/admin_session_log_types';
import { qiscusServiceNew } from '../services/QiscusServiceNew';
import { kataAiServices } from '../services/KataAiServices';
import metaServices from '../services/MetaServices';
import {
  IPayloadQiscusPostMessage,
  TAvailablePostMessageContext,
} from '../types/services/qiscus/qiscuss_service_new_types';
import axios from 'axios';
import { IChatRoomModel } from '../types/firestore/i_chat_room_model';
import ChatRoom from '../model/ChatRoom';

export interface SendMessageHandlerReqBody {
  roomPath: string;
  templatePath: string;
  adminSessionPath: string;
  phoneNumber: string;
}

export default class SendMessageInternalTemplateHandler {
  public static middlewares = [
    authChecker,
    json(),
    body('roomPath').notEmpty(),
    body('templatePath').optional(),
    body('adminSessionPath').notEmpty(),
    body('phoneNumber').notEmpty(),
    requestChecker,
  ];

  public static async handler(
    req: Request<any, any, SendMessageHandlerReqBody>,
    res: Response<
      any,
      {
        account: IFirestoreAdminEntity;
      }
    >
  ) {
    const roomRef = myFirestore.doc(req.body.roomPath);
    const templateRef = myFirestore.doc(req.body.templatePath);

    // --- Step 1: Retrieve template and sanitize HTML ---
    let sanitized: string;
    let templateData: firestore.DocumentSnapshot<firestore.DocumentData>;
    try {
      templateData = await templateRef.get();
      const rawText = templateData.get('text') || '';
      sanitized = sanitizeHtml(rawText, {
        allowedTags: ['br'],
      });
    } catch (error: any) {
      return res.status(500).send({
        success: false,
        messages: `Error retrieving or sanitizing template: ${error.toString()}`,
      });
    }

    // --- Step 2: Retrieve Firestore documents concurrently ---
    let roomGet: ChatRoom | null;
    let lastInboundMessage: firestore.QuerySnapshot<firestore.DocumentData>;
    let getAdminSession: firestore.DocumentSnapshot<firestore.DocumentData>;
    let projectGet: firestore.DocumentSnapshot<firestore.DocumentData>;
    try {
      [roomGet, lastInboundMessage, getAdminSession, projectGet] = await Promise.all([
        new Promise<ChatRoom | null>(async resolve => {
          const get = await roomRef.withConverter(ChatRoom.converter).get();
          if (get.exists) {
            resolve(get.data()!);
          } else {
            resolve(null);
          }
        }),
        roomRef
          .collection('chats')
          .where('message.direction', '==', 'IN')
          .orderBy('message.timestamp', 'desc')
          .limit(1)
          .get(),
        myFirestore.doc(req.body.adminSessionPath).get(),
        roomRef.parent && roomRef.parent.parent
          ? roomRef.parent.parent.get()
          : Promise.reject(new Error('Project reference is unavailable')),
      ]);
    } catch (error: any) {
      return res.status(500).send({
        success: false,
        messages: `Error retrieving Firestore documents: ${error.toString()}`,
      });
    }

    // --- Step 3: Prepare message content and context ---
    const textSaveToFirestore = sanitized;
    const textSendToProvider = sanitized
      .replace(/(<br>|<\/br>|<br \/>)/gim, '\n')
      .replace(/&amp;/g, '&');
    let context: string = 'text';
    let buttonContext: IPayloadQiscusPostMessage['interactive'] | undefined = undefined;
    try {
      if (templateData.get('button')) {
        const button1: string | null = templateData.get('button.button1');
        const button2: string | null = templateData.get('button.button2');
        const button3: string | null = templateData.get('button.button3');
        const buttons = [button1, button2, button3];
        if (button1 || button2 || button3) {
          context = 'interactive';
          buttonContext = {
            type: 'button',
            body: {
              text: textSendToProvider,
            },
            action: {
              buttons: [],
            },
          };
          buttons.forEach(value => {
            if (value) {
              buttonContext!.action.buttons?.push({
                type: 'reply',
                reply: {
                  id: value.toUpperCase(),
                  title: value,
                },
              });
            }
          });
        }
      }
    } catch (error: any) {
      return res.status(500).send({
        success: false,
        messages: `Error preparing message context: ${error.toString()}`,
      });
    }

    // --- Step 4: Determine sessionId dari last inbound message ---
    let sessionId: string = v4();
    try {
      if (lastInboundMessage.size === 1) {
        lastInboundMessage.forEach(result => {
          const data = result.data() as IFirestoreMessageEntity;
          if (
            moment((data.message.timestamp as firestore.Timestamp).toDate())
              .add(24, 'hours')
              .isAfter(moment())
          ) {
            if (data.session_id) sessionId = data.session_id;
          }
        });
      }
    } catch (error: any) {
      return res.status(500).send({
        success: false,
        messages: `Error processing room data: ${error.toString()}`,
      });
    }

    if (!roomGet) {
      return res.status(404).send({
        success: false,
        messages: 'Room not found',
      });
    }

    // --- Step 5: Send message to external provider dan mendapatkan messageId ---
    let messageId: string = '';
    try {
      const project = projectGet.data()!;
      const roomData = roomGet;
      if (project.provider === 'qiscus') {
        const send = await qiscusServiceNew.sendMessageNative({
          to: roomData.contacts[0],
          recipient_type: 'individual',
          preview_url: false,
          type: context as any,
          text:
            context === 'text'
              ? {
                  body: textSendToProvider,
                }
              : undefined,
          interactive: context === 'interactive' ? buttonContext : undefined,
        });
        messageId = send.data.messages[0].id;
      } else if (project.provider === 'kata.ai') {
        await kataAiServices
          .sendMessage({
            text: textSendToProvider,
            target: roomData.contacts[0],
          })
          .then(value => {
            messageId = value.data.messages[0].id;
          });
      } else if (project.provider === 'meta') {
        const payload: IPayloadQiscusPostMessage = {
          to: roomData.contacts[0],
          recipient_type: 'individual',
          preview_url: false,
          type: context as TAvailablePostMessageContext,
        };

        if (context === 'text') {
          payload.type = 'text';
          payload.text = {
            body: textSendToProvider,
          };
        } else if (context === 'interactive') {
          payload.type = 'interactive';
          payload.interactive = buttonContext;
        }

        await metaServices.sendMessageNative(payload, project.meta).then(value => {
          messageId = value.data.messages[0].id;
        });
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        console.log(error.response?.data);
      }
      return res.status(500).send({
        success: false,
        messages: `Error sending message: ${error.toString()}`,
      });
    }

    // --- Step 6: Siapkan parameter pesan, update batch Firestore, dan commit ---
    try {
      const messageParams: IFirestoreMessageEntity = {
        message: {
          id: messageId,
          direction: 'OUT',
          unixtime: new Date().getTime() / 1000,
          timestamp: firestore.Timestamp.now(),
          type: 'text',
          text: {
            body: textSaveToFirestore,
          },
        },
        statuses: {
          delivered: null,
          read: null,
          sent: firestore.Timestamp.now(),
          failed: null,
        },
        origin: {
          display_name: res.locals.account.name,
          id: res.locals.account.ref!.id,
          reference: res.locals.account.ref as firestore.DocumentReference,
        },
        session_id: sessionId,
      };

      const recentChat: IChatRoomModel['recent_chat'] = {
        contact: res.locals.account.email ?? 'Admin',
        direction: 'OUT',
        display_name: 'Admin',
        read: false,
        statuses: {
          failed: null,
          sent: firestore.Timestamp.now(),
          read: null,
          delivered: null,
        },
        text: textSaveToFirestore,
        timestamp: firestore.Timestamp.now(),
        type: 'text',
        unixtime: moment().unix(),
      };

      if (lastInboundMessage.size === 1) {
        lastInboundMessage.forEach(result => {
          const data = result.data() as IFirestoreMessageEntity;
          if (
            !moment((messageParams.message.timestamp as firestore.Timestamp).toDate()).isBefore(
              moment((data.message.timestamp as firestore.Timestamp).toDate()).add(24, 'hours')
            )
          ) {
            messageParams.error = {
              href: '',
              title:
                'Message failed to send because more than 24 hours have passed since the customer last replied to this number',
              code: 402,
            };
          }
        });
      }

      const batch = myFirestore.batch();
      const messageRef = await firestoreService.addNewMessage(
        messageParams,
        roomRef,
        messageId,
        batch
      );
      await firestoreService.updateRoomRecentChat(recentChat, roomRef, batch);

      if (!messageParams.error) {
        if (getAdminSession.exists) {
          const dataAdminSession = getAdminSession.data() as IAdminSessionLog;
          batch.update(getAdminSession.ref, {
            messages: [
              ...dataAdminSession.messages,
              {
                origin: messageParams.origin,
                message: messageParams.message,
                ref: messageRef,
              },
            ],
          });
        }
      }

      const clientSessionDoc = myFirestore.collection('client_session_logs').doc(sessionId);
      const getClientSessionDoc = await clientSessionDoc.get();
      if (getClientSessionDoc.exists) {
        const sessionLog = getClientSessionDoc.data() as ISessionLog<ISessionLogDataClient>;
        sessionLog.messages.push({
          message: messageParams.message,
          origin: messageParams.origin,
        });
        batch.update(clientSessionDoc, {
          messages: sessionLog.messages,
        });
      }

      await batch.commit();
    } catch (error: any) {
      console.error(error);
      return res.status(500).send({
        success: false,
        messages: `Error updating Firestore: ${error.toString()}`,
      });
    }

    // --- Jika semua langkah sukses, kirim respons ---
    res.send({
      success: true,
    });
  }
}
