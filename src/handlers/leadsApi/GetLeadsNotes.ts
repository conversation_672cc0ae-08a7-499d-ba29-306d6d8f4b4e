import { Request, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { param } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import successResponse from '../../schema/successResponse';

const getLeadsNotes = {
  middlewares: [
    leadsAuthorizer,
    param('organization').notEmpty(),
    param('phoneNumber').notEmpty(),
    requestValidator,
  ],
  handler: async function (
    req: Request<{ organization: string; phoneNumber: string }>,
    res: Response
  ) {
    const collection = myFirestore.collection('leads_notes');
    const get = await collection
      .where('phoneNumber', '==', req.params.phoneNumber)
      .where('organization', '==', req.params.organization)
      .orderBy('updatedAt', 'desc')
      .get();

    const results: ILeadsNotesDocument<Date>[] = [];

    get.forEach(result => {
      const data = result.data() as ILeadsNotesDocument;

      if (data) {
        results.push({
          notes: data.notes,
          updatedByUser: data.updatedByUser,
          statusLevel: data.statusLevel,
          event: data.event,

          phoneNumber: data.phoneNumber,
          organization: data.organization,

          firstName: data.firstName,
          lastName: data.lastName,

          agentCode: data.agentCode,
          agentName: data.agentName ?? 'NO_NAME',
          updatedAt: data.updatedAt.toDate(),
          moveToCold: false,
          reactivate: {
            currentTotal: data.reactivate?.currentTotal ?? 0,
          },
          totalUpdateNotes: data.totalUpdateNotes ?? 0,
        });
      }
    });

    res.send(
      successResponse({
        type: 'FETCHED',
        data: results,
      })
    );
  },
};

export default getLeadsNotes;
