import { HandlerTypes } from '../../../types/handler.types';
import dayjs from 'dayjs';
import { myFirestore } from '../../../services/firebaseAdmin';
import { IProject } from '../../../types/firestore/project.types';
import { firestore } from 'firebase-admin';
import { IClientDataCompletionTable } from '../../../types/services/bigQuery/clientDataCompletion.table.bigquery.types';
import ChatRoom from '../../../model/ChatRoom';
import { bigQuery, clientDataCompletions } from '../../../services/bigQueryService';
import { v4 as uuidV4 } from 'uuid';

const clientDataCompletionCronHandler: HandlerTypes = {
  middlewares: [],
  handler: async (req, res) => {
    const today = dayjs();
    const startDate = today.subtract(1, 'day').startOf('day');
    const endDate = today.subtract(1, 'day').endOf('day');

    const getProjects = await myFirestore.collection('projects').where('active', '==', true).get();

    const projects: (IProject & { ref: firestore.DocumentReference })[] = [];

    getProjects.forEach(result => {
      const data = result.data() as IProject;
      projects.push({
        ...data,
        ref: result.ref,
      });
    });

    for (const project of projects) {
      const bigQueryRows: IClientDataCompletionTable[] = [];

      const chatRoomCollectionRef = project.ref.collection('chat_rooms');

      const getChatRooms = await chatRoomCollectionRef
        .withConverter(ChatRoom.converter)
        .where('created_at', '>=', startDate.toDate())
        .where('created_at', '<=', endDate.toDate())
        .get();

      getChatRooms.forEach(result => {
        const data = result.data()! as ChatRoom;
        const dataRow: IClientDataCompletionTable = {
          uuid: uuidV4(),
          projectRef: project.ref.path || '',
          projectName: project.legal_name,
          projectGroup: project.group,
          name: data.headers.title,
          phoneNumber: data.contacts[0],
          organization: data.organization,
          organizationGroup: data.organizationGroup,
          organizationUpdatedBy: data.organizationUpdatedBy,
          cityGroup: data.cityGroup,
          cityGroupUpdatedAt: data.cityGroupUpdatedAt
            ? bigQuery.timestamp(data.cityGroupUpdatedAt.toDate())
            : null,
          cityGroupUpdatedBy: data.cityGroupUpdatedBy,
          organizationUpdatedAt: data.organizationUpdatedAt
            ? bigQuery.timestamp(data.organizationUpdatedAt.toDate())
            : null,
          chatRoomCreatedAt: bigQuery.timestamp(data.created_at.toDate()),
          createdAt: bigQuery.timestamp(today.toDate()),
        };
        bigQueryRows.push(dataRow);
      });

      // console.log(project.legal_name);
      // console.log('total', getChatRooms.size);

      try {
        await clientDataCompletions.insert(bigQueryRows);
      } catch (e) {
        console.log('FAILED_INSERT_CLIENT_DATA_COMPLETION', e);
        return res.status(500).send({
          success: false,
          messages: e?.toString(),
        });
      }
    }

    return res.send({
      success: true,
    });
  },
};

export default clientDataCompletionCronHandler;
