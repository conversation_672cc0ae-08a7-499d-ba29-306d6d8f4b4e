import { j<PERSON>, Request, RequestHand<PERSON>, Response } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../middlewares/requestValidator';
import FreeLeadsModel from '../../model/FreeLeadsModel';
import moment from 'moment';
import { myFirestore } from '../../services/firebaseAdmin';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import { firestore } from 'firebase-admin';
import { bigQuery, rawLeadsTable } from '../../services/bigQueryService';
import { RawLeadsTable } from '../../types/services/bigQuery/rawLeadsTable.types';
import { v4 as uuidV4 } from 'uuid';
import ClientModel from '../../model/ClientModel';
import { autotrimitraServices } from '../../services/autotrimitraServices';
import catalogueService from '../../services/catalogueService';
import { VariantCatalogue } from '../../types/services/catalogueServices.types';
import leadsAuthorizerExternal from '../../middlewares/leadsAuthorizerExternal';

interface ReqBody {
  title: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  zipCode: string;
  vehicleUsage: 'individual' | 'shared' | 'corporate';
  paymentPlan: 'cash' | 'credit';
  hasVehicleLoan: boolean;
  vehicleModel: string;
  vehicleVariantCode: string;
  organization: 'amartachery' | 'amartahonda' | 'amartaneta' | 'amartavinfast';
  source: string;
  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition';
  nextTotalVehicleOwnerShip: string;

  price: number;
  notes: string;

  idCard_number: string | null;
  driverLicense_number: string | null;
}

const addFreeLeadsZipCodeAsCity: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [
    json(),
    leadsAuthorizerExternal,
    body('title').optional({ values: 'falsy' }),
    body('firstName').notEmpty().trim().toUpperCase(),
    body('lastName')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? '' : input)),
    body('zipCode').notEmpty(),
    body('phoneNumber')
      .notEmpty()
      .trim()
      .isMobilePhone('id-ID')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('email').optional({ values: 'falsy' }).isEmail(),
    body('vehicleUsage').optional({ checkFalsy: true }).isIn(['individual', 'shared', 'corporate']),
    body('paymentPlan').optional({ values: 'falsy' }).isIn(['cash', 'credit']),
    body('hasVehicleLoan')
      .customSanitizer(input => (!input ? false : input))
      .isBoolean()
      .toBoolean(),
    body('vehicleModel').notEmpty().trim().toLowerCase(),
    body('vehicleVariantCode').optional({ values: 'falsy' }),
    body('organization')
      .notEmpty()
      .isIn(['amartachery', 'amartahonda'])
      .customSanitizer(input => input.replace(/ /g, '')),
    body('purchasePlan')
      .optional({ values: 'falsy' })
      .isIn(['firstVehicle', 'vehicleReplacement', 'vehicleAddition']),
    body('nextTotalVehicleOwnerShip').optional({ values: 'falsy' }),
    body('idCard_number')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? null : input)),
    body('driverLicense_number')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? null : input)),

    body('price').optional({ values: 'falsy' }).isNumeric().toInt(),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response<ResponseSchema>) => {
    console.log(req.body);
    const now = moment();
    const freeLeadsCollection = myFirestore.collection('free_leads');
    const leadsCollection = myFirestore.collection('leads');
    const rawLeadsCollection = myFirestore.collection('raw_leads');

    const bigQueryRows: RawLeadsTable[] = [];
    const rawLeadsDocs: firestore.DocumentReference[] = [];

    const body = req.body;

    const freeLeadsDocName = `${body.organization}-${body.phoneNumber}`;

    const acquiredLeads = await leadsCollection.doc(freeLeadsDocName).get();
    const findInRawLeads = await rawLeadsCollection
      .where('phoneNumber', '==', body.phoneNumber)
      .get();
    const findFreeLeads = await freeLeadsCollection
      .doc(freeLeadsDocName)
      .withConverter(FreeLeadsModel.converter)
      .get();

    if (acquiredLeads.exists) {
      return res.status(409).send(
        errorResponse({
          type: 'UNPROCESSABLE',
          messages: 'Sudah ada My Leads dan sudah diakusisi',
        })
      );
    } else if (findFreeLeads.exists) {
      return res.status(409).send(
        errorResponse({
          type: 'UNPROCESSABLE',
          messages: 'Sudah ada di Free Leads',
        })
      );
    }

    const getGeo = await autotrimitraServices.postalCode(req.body.zipCode);

    if (getGeo.data.data.length === 0) {
      return res.status(404).send(
        errorResponse({
          type: 'UNPROCESSABLE',
          messages: 'ZIP Code tidak ada di dalam master',
        })
      );
    }

    const geo = getGeo.data.data[0];

    const getCityGroup = await catalogueService.getAvailableArea({
      cityCode: geo.city_code,
    });

    const cityGroup = getCityGroup.data.data[0].city_group;

    const getModel = await catalogueService.getAvailableModelByCityGroup({
      cityGroup: cityGroup,
      modelName: req.body.vehicleModel,
    });

    const model = getModel.data.data[0];
    let variant: VariantCatalogue | null = null;
    if (req.body.vehicleVariantCode) {
      const getVariant = await catalogueService.getVariantByCityGroup({
        cityGroup: cityGroup,
        variantCode: req.body.vehicleVariantCode,
      });
      variant = getVariant.data.data[0];
    }

    const batchAddFreLeads = myFirestore.batch();

    const rawLeadsDoc = rawLeadsCollection.doc();
    const rawLeads: any = {
      externalId: null,
      phoneNumber: body.phoneNumber,
      fullName: `${body.firstName} ${body.lastName}`,
      // TODO: CITY NAME
      city: geo.city_name,
      vehicleModel: model.model_name ?? null,
      vehicleVariant: variant?.variant_name ?? null,
      organization: body.organization,

      externalData: {
        ...req.body,
      },
      source: body.source || 'form-free-leads',

      inFreeLeads: findFreeLeads.exists,
      shouldPushToFreeLeadsAt: now.clone().add(15, 'minutes').toDate(),
      freeLeadsPath: findFreeLeads.exists ? findFreeLeads.ref : null,
      freeLeadsCreatedAt: findFreeLeads.exists ? findFreeLeads.get('createdAt') : null,

      shouldPushToBigQueryAt: now.clone().add(15, 'minutes').toDate(),
      donePushToBigQuery: false,
      donePushToBigQueryAt: null,

      createdAt: now.toDate(),

      duplicated: !findInRawLeads.empty,
      mappedData: {
        // TODO Domicile
        domicile: {
          provinceName: geo.province_name,
          provinceCode: geo.province_code,
          cityName: geo.city_name,
          cityCode: geo.city_code,
        },
        vehicle: [
          {
            brand: {
              name: model.brand_name,
            },
            model: {
              name: model.model_name,
            },
            variant: {
              name: variant?.variant_name || null,
              code: variant?.variant_code || null,
            },
            color: {
              name: null,
              code: null,
            },
          },
        ],
        area: cityGroup,
        paymentPlan: body.paymentPlan,
      },
      whatsapp: null,
      alreadyInMyLeads: acquiredLeads.exists,
    };
    batchAddFreLeads.create(rawLeadsDoc, rawLeads);
    rawLeadsDocs.push(rawLeadsDoc);

    const freeLeadsDocRef: firestore.DocumentReference = freeLeadsCollection.doc(freeLeadsDocName);

    let price = body.organization === 'amartahonda' ? 20000 : 50000;
    let isCustomPrice = false;

    if (body.price) {
      if (body.organization === 'amartachery' && body.price > 50000) {
        price = body.price;
        isCustomPrice = true;
      } else if (body.organization === 'amartahonda' && body.price > 20000) {
        price = body.price;
        isCustomPrice = true;
      }
    }

    const freeLeads = new FreeLeadsModel({
      rawLeadsRef: rawLeadsDoc,
      area: cityGroup,
      externalId: null,
      title: body.title,
      firstName: body.firstName,
      lastName: body.lastName,
      phoneNumber: body.phoneNumber,
      paymentPlan: body.paymentPlan,
      hasVehicleLoan: body.hasVehicleLoan,
      createdAt: now.toDate(),

      // TODO Domicile
      domicile: {
        cityCode: geo.city_code,
        cityName: geo.city_name,
        provinceCode: geo.province_code,
        provinceName: geo.province_name,
      },

      driverLicense_number: body.driverLicense_number,
      idCard_number: body.idCard_number,
      email: body.email,
      nextTotalVehicleOwnerShip: body.nextTotalVehicleOwnerShip,
      organization: body.organization,
      purchasePlan: body.purchasePlan,
      source: body.source || 'form-free-leads',
      vehicleOptions: rawLeads.vehicle,
      vehicleUsage: body.vehicleUsage,
      ref: freeLeadsDocRef,

      isAcquired: false,
      acquiredAt: null,
      acquiredAgentCode: null,
      acquiredAgentName: null,
      price,
      isCustomPrice: isCustomPrice,
      notes: body.notes,
      ideal: null,
    });

    batchAddFreLeads.set(freeLeads.ref.withConverter(FreeLeadsModel.converter), freeLeads);

    const uuid = uuidV4();
    const bigQueryRow: RawLeadsTable = {
      city: geo.city_name,
      city_group: cityGroup,
      duplicated: !findInRawLeads.empty,
      external_id: freeLeads.externalId,
      full_name: `${freeLeads.firstName} ${freeLeads.lastName}`,
      organization: freeLeads.organization,
      phone_number: freeLeads.phoneNumber,
      source: freeLeads.source,

      trimitra_city: freeLeads.domicile?.cityCode
        ? {
            code: freeLeads.domicile?.cityCode,
            name: freeLeads.domicile?.cityName,
          }
        : null,
      trimitra_province: freeLeads.domicile?.provinceCode
        ? {
            code: freeLeads.domicile?.provinceCode,
            name: freeLeads.domicile?.provinceName,
          }
        : null,

      trimitra_vehicle: {
        variant: {
          code: freeLeads.vehicleOptions?.[0]?.variant.code,
          name: freeLeads.vehicleOptions?.[0]?.variant.name,
        },
        model: {
          name: freeLeads.vehicleOptions?.[0]?.model.name,
        },
        color: {
          code: freeLeads.vehicleOptions?.[0]?.color.code,
          name: freeLeads.vehicleOptions?.[0]?.color.name,
        },
      },

      uid: uuid,
      vehicle_model: freeLeads.vehicleOptions?.[0]?.model.name,
      vehicle_variant: freeLeads.vehicleOptions?.[0]?.variant.name,
      whatsapp_delivered: null,
      whatsapp_delivered_at_timestamp: null,
      data_leads_created_at_timestamp: bigQuery.timestamp(now.toDate()),
      bq_created_at_datetime: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
      bq_created_at_timestamp: bigQuery.timestamp(now.toDate()),
      is_in_my_leads: acquiredLeads.exists,
    };

    bigQueryRows.push(bigQueryRow);

    const getClientIdeal = await myFirestore
      .collection('clients')
      .where('contacts.whatsapp', '==', req.body.phoneNumber)
      .withConverter(ClientModel.converter)
      .get();

    if (!getClientIdeal.empty) {
      let client!: ClientModel;
      getClientIdeal.forEach(result => {
        client = result.data();
      });

      client.freeLeadsStatus = {
        createdAt: firestore.Timestamp.fromDate(now.toDate()),
        submitted: true,
        organization: freeLeads.organization,
      };

      batchAddFreLeads.set(client.ref.withConverter(ClientModel.converter), client);
    }

    try {
      await batchAddFreLeads.commit();
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi kesalahan pada server',
          data: {
            messages: 'Terjadi kesalahan pada server',
            error: e?.toString(),
          },
        })
      );
    }

    try {
      const batchUpdateStatusBigQuery = myFirestore.batch();
      await rawLeadsTable.insert(bigQueryRows);
      for (const doc of rawLeadsDocs) {
        batchUpdateStatusBigQuery.update(doc, {
          donePushToBigQuery: true,
          donePushToBigQueryAt: now.toDate(),
        });
      }
      await batchUpdateStatusBigQuery.commit();
    } catch (e) {
      console.log('FAILED_TO_INSERT_RAW_LEADS_FORM', JSON.stringify(e), e);
    }

    res.send(
      successResponse({
        data: null,
        type: 'CREATED',
      })
    );
  },
};

export default addFreeLeadsZipCodeAsCity;
