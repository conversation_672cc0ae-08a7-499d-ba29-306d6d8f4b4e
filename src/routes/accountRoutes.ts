import { Router } from 'express';
import AccountHandler from '../handlers/admin/AccountHandler';
import UpdateCreditSchemeHandler from '../handlers/client/UpdateCreditSchemeHandler';
import DreamVehicleHandler from '../handlers/client/DreamVehicleHandler';
import UpdateLoanSchemeHandler from '../handlers/client/UpdateLoanSchemeHandler';

const router = Router();

export default function (parent: Router) {
  parent.use('/account', router);
  router.get('/me', AccountHandler.middlewares, AccountHandler.handler);

  router.post(
    '/survey/update-credit-scheme',
    UpdateCreditSchemeHandler.middlewares,
    UpdateCreditSchemeHandler.handler
  );
  router.post(
    '/survey/update-loan-scheme',
    UpdateLoanSchemeHandler.middlewares,
    UpdateLoanSchemeHandler.handler
  );
  router.post(
    '/survey/update-dream-vehicle',
    DreamVehicleHandler.middlewares,
    DreamVehicleHandler.handler
  );
}
