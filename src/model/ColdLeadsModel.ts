import { firestore } from 'firebase-admin';
import { IColdLeadsDocument } from '../types/firestore/cold_leads_model.types';
import { ILeadsDocument } from '../types/firestore/leads_model.types';
import FirestoreDataConverter = firestore.FirestoreDataConverter;

class ColdLeadsModel implements IColdLeadsDocument<Date> {
  public static converter: FirestoreDataConverter<ColdLeadsModel> = {
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot): ColdLeadsModel {
      const data = snapshot.data() as IColdLeadsDocument;
      return new ColdLeadsModel({
        agentCode: data.agentCode,
        area: data.area,
        title: data.title,
        firstName: data.firstName,
        lastName: data.lastName,
        phoneNumber: data.phoneNumber,
        email: data.email,
        domicile: data.domicile,
        vehicleUsage: data.vehicleUsage,
        paymentPlan: data.paymentPlan,
        downPaymentPlan: data.downPaymentPlan || null,
        hasVehicleLoan: data.hasVehicleLoan,
        vehicleOptions: data.vehicleOptions,
        source: data.source,
        organization: data.organization,
        createdAt: data.createdAt.toDate(),
        statusLevel: data.statusLevel,
        updatedAt: data.updatedAt?.toDate(),
        updateHistories:
          data.updateHistories?.map(value => ({
            ...value,
            updatedAt: value.updatedAt.toDate(),
            followUpScheduledAt: value.followUpScheduledAt?.toDate() ?? null,
          })) ?? [],
        purchasePlan: data.purchasePlan,
        nextTotalVehicleOwnerShip: data.nextTotalVehicleOwnerShip,
        isTracking: data.isTracking,
        notes: data.notes ?? '',
        idCard_number: data.idCard_number,
        driverLicense_number: data.driverLicense_number,
        agentName: data.agentName,
        phoneNumberAgent: data.phoneNumberAgent,
        whatsapp: data.whatsapp
          ? {
              ...data.whatsapp,
              response: data.whatsapp.response
                ? {
                    ...data.whatsapp.response,
                    createdAt: data.whatsapp.response?.createdAt?.toDate() ?? null,
                    repliedAt: data.whatsapp.response?.repliedAt?.toDate() ?? null,
                  }
                : null,
            }
          : null,
        followUp: data.followUp,
        followUpRequestedAt: data.followUpRequestedAt?.toDate(),
        followUpStartedAt: data.followUpStartedAt?.toDate(),
        hotLeads: data.hotLeads ?? false,
        hotLeadsRequestedAt: data.hotLeadsRequestedAt?.toDate() ?? null,
        pendingRequestReactivateTracking: data.pendingRequestReactivateTracking
          ? {
              ...data.pendingRequestReactivateTracking,
              requestedAt: data.pendingRequestReactivateTracking.requestedAt.toDate(),
              response: data.pendingRequestReactivateTracking.response
                ? {
                    ...data.pendingRequestReactivateTracking.response,
                    updatedAt: data.pendingRequestReactivateTracking.response.updatedAt.toDate(),
                  }
                : null,
            }
          : null,
        webWhatsapp: data.webWhatsapp
          ? {
              inbound: {
                ...data.webWhatsapp.inbound,
                updatedAt: data.webWhatsapp.inbound.updatedAt.toDate(),
              },
              outbound: {
                ...data.webWhatsapp.outbound,
                updatedAt: data.webWhatsapp.outbound.updatedAt.toDate(),
              },
            }
          : null,
        testDrive: data.testDrive
          ? {
              ...data.testDrive,
              date: data.testDrive.date.toDate(),
            }
          : null,
        moveToColdBy: data.moveToColdBy,
        moveToColdAt: data.moveToColdAt.toDate(),
        coldNotes: data.coldNotes ?? '',
        moveToColdByUserType: data.moveToColdByUserType ?? 'admin',

        spk: data.spk
          ? {
              ...data.spk,
              createdAt: data.createdAt.toDate(),
            }
          : null,
        requestPromo: data.requestPromo
          ? {
              ...data.requestPromo,
              createdAt: data.createdAt.toDate(),
            }
          : null,
        fromFreeLeads: data.fromFreeLeads ?? false,
        freeLeadsCreatedAt: data.freeLeadsCreatedAt?.toDate() ?? null,
        feedBackText: data.feedBackText,
        feedBackVoice: data.feedBackVoice,
        feedbackUpdatedAt: data.feedbackUpdatedAt?.toDate() ?? null,
        followUpScheduledAt: data.followUpScheduledAt?.toDate() ?? null,

        firstMessageDirection: data.firstMessageDirection || null,

        b2bCreatedSurveyOrders: data.b2bCreatedSurveyOrders
          ? data.b2bCreatedSurveyOrders.map(value => {
              return {
                ...value,
                createdAt: value.createdAt.toDate(),
              };
            })
          : [],

        ref: snapshot.ref,
        agentWhatsappConversations:
          data.agentWhatsappConversations?.map(a => {
            return {
              ...a,
              createdAt: a.createdAt.toDate(),
              startChatAt: a.startChatAt.toDate(),
              endChatAt: a.endChatAt.toDate(),
              startInboundChatAt: a.startInboundChatAt?.toDate() || null,
              endInboundChatAt: a.endInboundChatAt?.toDate() || null,
              startOutboundChatAt: a.startOutboundChatAt.toDate(),
              endOutboundChatAt: a.endOutboundChatAt.toDate(),
            };
          }) || [],
        tradeIn: data.tradeIn || null,
      });
    },
    toFirestore(modelObject): FirebaseFirestore.DocumentData {
      return {
        agentCode: modelObject.agentCode,
        area: modelObject.area,
        title: modelObject.title,
        firstName: modelObject.firstName,
        lastName: modelObject.lastName,
        phoneNumber: modelObject.phoneNumber,
        email: modelObject.email,
        domicile: modelObject.domicile,
        vehicleUsage: modelObject.vehicleUsage,
        paymentPlan: modelObject.paymentPlan,
        hasVehicleLoan: modelObject.hasVehicleLoan,
        vehicleOptions: modelObject.vehicleOptions,
        source: modelObject.source,
        organization: modelObject.organization,
        createdAt: modelObject.createdAt,
        statusLevel: modelObject.statusLevel,
        updatedAt: modelObject.updatedAt,
        updateHistories: modelObject.updateHistories,
        purchasePlan: modelObject.purchasePlan,
        downPaymentPlan: modelObject.downPaymentPlan,
        nextTotalVehicleOwnerShip: modelObject.nextTotalVehicleOwnerShip,
        isTracking: modelObject.isTracking,
        notes: modelObject.notes,
        idCard_number: modelObject.idCard_number,
        driverLicense_number: modelObject.driverLicense_number,
        agentName: modelObject.agentName,
        phoneNumberAgent: modelObject.phoneNumberAgent,
        whatsapp: modelObject.whatsapp ?? null,
        followUp: modelObject.followUp ?? false,
        followUpRequestedAt: modelObject.followUpRequestedAt,
        followUpStartedAt: modelObject.followUpStartedAt,
        hotLeads: modelObject.hotLeads ?? false,
        hotLeadsRequestedAt: modelObject.hotLeadsRequestedAt,
        pendingRequestReactivateTracking: modelObject.pendingRequestReactivateTracking,
        webWhatsapp: modelObject.webWhatsapp,
        testDrive: modelObject.testDrive,
        moveToColdAt: modelObject.moveToColdAt,
        moveToColdBy: modelObject.moveToColdBy,
        moveToColdByUserType: modelObject.moveToColdByUserType,
        coldNotes: modelObject.coldNotes,
        spk: modelObject.spk,
        requestPromo: modelObject.requestPromo,
        fromFreeLeads: modelObject.fromFreeLeads ?? false,
        freeLeadsCreatedAt: modelObject.freeLeadsCreatedAt,
        feedBackText: modelObject.feedBackText,
        feedBackVoice: modelObject.feedBackVoice,
        feedbackUpdatedAt: modelObject.feedbackUpdatedAt ?? null,
        firstMessageDirection: modelObject.firstMessageDirection || null,
        agentWhatsappConversations: modelObject.agentWhatsappConversations || [],
        b2bCreatedSurveyOrders: modelObject.b2bCreatedSurveyOrders || [],
        tradeIn: modelObject.tradeIn || null,
      };
    },
  };
  public agentCode!: IColdLeadsDocument['agentCode'];
  public area!: IColdLeadsDocument['area'];
  public title!: IColdLeadsDocument['title'];
  public firstName!: IColdLeadsDocument['firstName'];
  public lastName!: IColdLeadsDocument['lastName'];
  public phoneNumber!: IColdLeadsDocument['phoneNumber'];
  public email!: IColdLeadsDocument['email'];
  public domicile!: IColdLeadsDocument['domicile'];
  public vehicleUsage!: IColdLeadsDocument['vehicleUsage'];
  public paymentPlan!: IColdLeadsDocument['paymentPlan'];
  public downPaymentPlan!: IColdLeadsDocument['downPaymentPlan'];
  public vehicleOptions!: IColdLeadsDocument['vehicleOptions'];
  public source!: IColdLeadsDocument['source'];
  public createdAt!: IColdLeadsDocument<Date>['createdAt'];
  public hasVehicleLoan!: IColdLeadsDocument['hasVehicleLoan'];
  public statusLevel!: IColdLeadsDocument['statusLevel'];
  public updatedAt!: IColdLeadsDocument<Date>['updatedAt'];
  public updateHistories!: IColdLeadsDocument<Date>['updateHistories'];
  public purchasePlan!: IColdLeadsDocument['purchasePlan'];
  public nextTotalVehicleOwnerShip!: IColdLeadsDocument['nextTotalVehicleOwnerShip'];
  public organization!: IColdLeadsDocument['organization'];
  public isTracking!: IColdLeadsDocument['isTracking'];
  public notes!: IColdLeadsDocument['notes'];
  public idCard_number!: IColdLeadsDocument['idCard_number'];
  public driverLicense_number!: IColdLeadsDocument['driverLicense_number'];
  public agentName!: IColdLeadsDocument['agentName'];
  public phoneNumberAgent!: IColdLeadsDocument['phoneNumberAgent'];
  public followUp!: IColdLeadsDocument['followUp'];
  public followUpRequestedAt!: IColdLeadsDocument<Date>['followUpRequestedAt'];
  public followUpStartedAt!: IColdLeadsDocument<Date>['followUpStartedAt'];
  public followUpScheduledAt!: IColdLeadsDocument<Date>['followUpScheduledAt'];
  public hotLeads!: IColdLeadsDocument['hotLeads'];
  public hotLeadsRequestedAt!: IColdLeadsDocument<Date>['hotLeadsRequestedAt'];
  public whatsapp!: IColdLeadsDocument<Date>['whatsapp'];
  public pendingRequestReactivateTracking!: IColdLeadsDocument<Date>['pendingRequestReactivateTracking'];
  public webWhatsapp!: IColdLeadsDocument<Date>['webWhatsapp'];
  public testDrive!: IColdLeadsDocument<Date>['testDrive'];
  public moveToColdAt!: IColdLeadsDocument<Date>['moveToColdAt'];
  public moveToColdBy!: IColdLeadsDocument<Date>['moveToColdBy'];
  public coldNotes!: IColdLeadsDocument<Date>['coldNotes'];
  public moveToColdByUserType!: IColdLeadsDocument<Date>['moveToColdByUserType'];
  public fromFreeLeads!: IColdLeadsDocument<Date>['fromFreeLeads'];

  public feedBackVoice!: IColdLeadsDocument<Date>['feedBackVoice'];
  public feedBackText!: IColdLeadsDocument<Date>['feedBackText'];
  public feedbackUpdatedAt!: IColdLeadsDocument<Date>['feedbackUpdatedAt'];
  public freeLeadsCreatedAt!: IColdLeadsDocument<Date>['freeLeadsCreatedAt'];

  public firstMessageDirection!: IColdLeadsDocument<Date>['firstMessageDirection'];

  public spk!: IColdLeadsDocument<Date>['spk'];
  public requestPromo!: IColdLeadsDocument<Date>['requestPromo'];
  public agentWhatsappConversations!: IColdLeadsDocument<Date>['agentWhatsappConversations'];

  public b2bCreatedSurveyOrders!: IColdLeadsDocument<Date>['b2bCreatedSurveyOrders'];

  public tradeIn!: IColdLeadsDocument<Date>['tradeIn'];

  public ref!: firestore.DocumentReference;

  constructor(params?: {
    agentCode: IColdLeadsDocument['agentCode'];
    area: IColdLeadsDocument['area'];
    title: IColdLeadsDocument['title'];
    firstName: IColdLeadsDocument['firstName'];
    lastName: IColdLeadsDocument['lastName'];
    phoneNumber: IColdLeadsDocument['phoneNumber'];
    email: IColdLeadsDocument['email'];
    domicile: IColdLeadsDocument['domicile'];
    vehicleUsage: IColdLeadsDocument['vehicleUsage'];
    paymentPlan: IColdLeadsDocument['paymentPlan'];
    downPaymentPlan: IColdLeadsDocument['downPaymentPlan'];
    vehicleOptions: IColdLeadsDocument['vehicleOptions'];
    source: IColdLeadsDocument['source'];
    organization: IColdLeadsDocument['organization'];
    createdAt: IColdLeadsDocument<Date>['createdAt'];
    hasVehicleLoan: IColdLeadsDocument['hasVehicleLoan'];
    statusLevel?: IColdLeadsDocument['statusLevel'];
    updatedAt?: IColdLeadsDocument<Date>['updatedAt'];
    updateHistories?: IColdLeadsDocument<Date>['updateHistories'];
    purchasePlan: IColdLeadsDocument['purchasePlan'];
    nextTotalVehicleOwnerShip: IColdLeadsDocument['nextTotalVehicleOwnerShip'];
    isTracking: IColdLeadsDocument['isTracking'];
    notes: IColdLeadsDocument['notes'];
    idCard_number: IColdLeadsDocument['idCard_number'];
    driverLicense_number: IColdLeadsDocument['driverLicense_number'];
    agentName?: IColdLeadsDocument['agentName'];
    phoneNumberAgent?: IColdLeadsDocument['phoneNumberAgent'];
    whatsapp?: IColdLeadsDocument<Date>['whatsapp'];
    followUp?: IColdLeadsDocument['followUp'];
    followUpRequestedAt?: IColdLeadsDocument<Date>['followUpRequestedAt'];
    followUpStartedAt?: IColdLeadsDocument<Date>['followUpStartedAt'];
    followUpScheduledAt?: IColdLeadsDocument<Date>['followUpScheduledAt'];
    hotLeads?: IColdLeadsDocument['hotLeads'];
    hotLeadsRequestedAt?: IColdLeadsDocument<Date>['hotLeadsRequestedAt'];
    pendingRequestReactivateTracking?: IColdLeadsDocument<Date>['pendingRequestReactivateTracking'];
    webWhatsapp?: IColdLeadsDocument<Date>['webWhatsapp'];
    testDrive?: IColdLeadsDocument<Date>['testDrive'];
    moveToColdAt: IColdLeadsDocument<Date>['moveToColdAt'];
    moveToColdBy: IColdLeadsDocument<Date>['moveToColdBy'];
    spk: IColdLeadsDocument<Date>['spk'];
    requestPromo: IColdLeadsDocument<Date>['requestPromo'];
    coldNotes: IColdLeadsDocument<Date>['coldNotes'];
    moveToColdByUserType: IColdLeadsDocument<Date>['moveToColdByUserType'];
    fromFreeLeads: IColdLeadsDocument<Date>['fromFreeLeads'];
    freeLeadsCreatedAt: IColdLeadsDocument<Date>['freeLeadsCreatedAt'];

    firstMessageDirection: IColdLeadsDocument<Date>['firstMessageDirection'];

    feedBackText: IColdLeadsDocument<Date>['feedBackText'];
    feedBackVoice: IColdLeadsDocument<Date>['feedBackVoice'];
    feedbackUpdatedAt: IColdLeadsDocument<Date>['feedbackUpdatedAt'];
    agentWhatsappConversations?: IColdLeadsDocument<Date>['agentWhatsappConversations'];

    b2bCreatedSurveyOrders?: ILeadsDocument<Date>['b2bCreatedSurveyOrders'];
    tradeIn?: IColdLeadsDocument<Date>['tradeIn'];

    ref: firestore.DocumentReference;
  }) {
    if (params) {
      this.agentCode = params.agentCode;
      this.area = params.area ?? null;
      this.title = params.title;
      this.firstName = params.firstName;
      this.lastName = params.lastName;
      this.phoneNumber = params.phoneNumber;
      this.email = params.email;
      this.domicile = params.domicile;
      this.vehicleUsage = params.vehicleUsage;
      this.paymentPlan = params.paymentPlan;
      this.downPaymentPlan = params.downPaymentPlan;
      this.vehicleOptions = params.vehicleOptions;
      this.source = params.source;
      this.organization = params.organization;
      this.createdAt = params.createdAt;
      this.hasVehicleLoan = params.hasVehicleLoan;
      this.statusLevel = params.statusLevel ?? 0;
      this.updatedAt = params.updatedAt ?? null;
      this.updateHistories = params.updateHistories ?? [];
      this.purchasePlan = params.purchasePlan;
      this.nextTotalVehicleOwnerShip = params.nextTotalVehicleOwnerShip;
      this.isTracking = params.isTracking;
      this.notes = params.notes ?? '';
      this.idCard_number = params.idCard_number ?? null;
      this.driverLicense_number = params.driverLicense_number ?? null;
      this.agentName = params.agentName ?? '';
      this.phoneNumberAgent = params.phoneNumberAgent ?? [];
      this.followUp = params.followUp ?? false;
      this.followUpRequestedAt = params.followUpRequestedAt ?? null;
      this.followUpStartedAt = params.followUpStartedAt ?? null;
      this.followUpScheduledAt = params.followUpScheduledAt ?? null;
      this.hotLeads = params.hotLeads ?? false;
      this.hotLeadsRequestedAt = params.hotLeadsRequestedAt ?? null;
      this.whatsapp = params.whatsapp ?? null;
      this.pendingRequestReactivateTracking = params.pendingRequestReactivateTracking ?? null;
      this.webWhatsapp = params.webWhatsapp ?? null;
      this.testDrive = params.testDrive ?? null;
      this.moveToColdAt = params.moveToColdAt;
      this.moveToColdBy = params.moveToColdBy;
      this.moveToColdByUserType = params.moveToColdByUserType;
      this.coldNotes = params.coldNotes;
      this.spk = params.spk;
      this.requestPromo = params.requestPromo;
      this.fromFreeLeads = params.fromFreeLeads ?? false;
      this.freeLeadsCreatedAt = params.freeLeadsCreatedAt ?? null;
      this.feedBackVoice = params.feedBackVoice ?? null;
      this.feedBackText = params.feedBackText ?? null;
      this.feedbackUpdatedAt = params.feedbackUpdatedAt ?? null;
      this.firstMessageDirection = params.firstMessageDirection || null;
      this.agentWhatsappConversations = params.agentWhatsappConversations || [];
      this.b2bCreatedSurveyOrders = params.b2bCreatedSurveyOrders || [];
      this.tradeIn = params.tradeIn || null;

      this.ref = params.ref;
    }
  }

  toJsonResponse() {
    return {
      ...this,
      ref: this.ref.path,
      documentId: this.ref.id,
    };
  }
}

export default ColdLeadsModel;
