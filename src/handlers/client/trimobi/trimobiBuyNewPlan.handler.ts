import { json } from 'express';
import { HandlerTypes } from '../../../types/handler.types';
import requestValidator from '../../../middlewares/requestValidator';
import { body } from 'express-validator';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import errorResponse from '../../../schema/errorResponse';
import amartavipService from '../../../services/amartavip/amartavip.services';
import successResponse from '../../../schema/successResponse';
import { PlanCheckoutResponse } from '../../../services/amartavip/amartavip.services.types';
import { isAxiosError } from 'axios';
import authChecker from '../../../middlewares/authChecker';
import { generateQrCodeImage } from '../../../helpers/generateQrCodeImage';
import uploadImage from '../../../services/uploadImage';

interface ReqBody {
  projectId: string;
  planCode: string;
  adsQuantity: number;
  customerPhoneNumber: string;
  vehicleType: string;
}

const trimobiBuyNewPlanHandler: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    json(),
    authChecker,
    function (req, res, next) {
      amartavipService.setEmail(res.locals.account.email);
      next();
    },
    body('projectId').isString().notEmpty().withMessage('Project ID is required'),
    body('planCode').isString().notEmpty().withMessage('Plan code is required'),
    body('adsQuantity').isInt({ min: 1 }).withMessage('Ads quantity must be a positive integer'),
    body('customerPhoneNumber')
      .isString()
      .notEmpty()
      .withMessage('Customer phone number is required'),
    body('vehicleType').isString().notEmpty().withMessage('Vehicle type is required'),
    requestValidator,
  ],
  handler: async (req, res) => {
    let checkOutResponse: PlanCheckoutResponse | null = null;
    let qrImageUrl = '';

    try {
      checkOutResponse = await amartavipService.adsPlanCheckout({
        source: 'trimobi',
        code: req.body.planCode,
        ads_option_qty: req.body.adsQuantity,
        payment_issuer: 'xendit',
        payment_method: 'qris',
        notes: 'Pembelian paket baru oleh nomor telepon ' + req.body.customerPhoneNumber,
      });
    } catch (error) {
      let errorMessage = 'Terjadi kesalahan ketika melakukan checkout plan';
      if (isAxiosError(error)) {
        errorMessage = 'Error API checkout plan: ' + error.response?.data.message;
      }
      return res.status(500).json(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: errorMessage,
        })
      );
    }

    try {
      const generateImage = await generateQrCodeImage(
        checkOutResponse!.data.bill_data,
        'qris-qrcode.png',
        {
          width: 400,
          margin: 2,
        }
      );
      qrImageUrl = await uploadImage(generateImage);
    } catch (error) {
      console.error('Error generating/uploading QR code:', error);
    }

    return res.status(200).json(
      successResponse({
        data: {
          qrisUrl: qrImageUrl,
          checkout: checkOutResponse.data,
        },
      })
    );
  },
};

export default trimobiBuyNewPlanHandler;
