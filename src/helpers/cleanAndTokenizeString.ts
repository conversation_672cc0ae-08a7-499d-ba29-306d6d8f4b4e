import { ind, removeStopwords } from 'stopword';

const cleanAndTokenizeString = (text: string) => {
  function removeNonAlphaNumeric(text: string): string {
    let _text = text;
    _text = _text.replace(/\n/g, ' ');
    _text = _text.replace(/[^a-zA-Z0-9 ]/g, '');
    _text = _text.toLowerCase();
    return _text;
  }

  const _text = removeNonAlphaNumeric(text);
  const _splitBySpace = _text.split(' ').filter(r => !!r);
  return removeStopwords(_splitBySpace, ind);
};

export default cleanAndTokenizeString;
