import { NextFunction, Request, Response } from 'express';
import moment from 'moment/moment';

const formatDateV1 = (req: Request, res: Response, next: NextFunction) => {
  const originalJson = res.json;

  // @ts-ignore
  res.json = function (data: any) {
    // Fungsi rekursif untuk mengganti format semua Date dalam objek menjadi YYYY-MM-DD HH:mm
    const formatDate = (obj: any): any => {
      if (obj instanceof Date) {
        // Gunakan Moment.js untuk memformat tanggal
        return moment(obj).format('YYYY-MM-DD HH:mm');
      }
      if (Array.isArray(obj)) {
        return obj.map(formatDate);
      }
      if (obj && typeof obj === 'object') {
        return Object.keys(obj).reduce((acc: any, key: string) => {
          acc[key] = formatDate(obj[key]);
          return acc;
        }, {});
      }
      return obj;
    };

    // Modifikasi data sebelum dikirimkan
    const modifiedData = formatDate(data);
    originalJson.call(this, modifiedData); // Panggil original `json` dengan data yang sudah dimodifikasi
  };

  next();
};

export default formatDateV1;
