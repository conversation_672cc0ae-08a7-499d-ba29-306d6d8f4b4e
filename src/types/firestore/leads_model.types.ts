import { firestore } from 'firebase-admin';
import { ILeadsNotesDocument } from './leads_notes.types';

export type LeadsOrganization = string;
export type LeadsUpdateEvent =
  | 'untrack'
  | 'reactivateTrack'
  | 'track'
  | 'increaseLevelStatus'
  | 'decreaseLevelStatus'
  | 'init'
  | 'triforceNotes'
  | 'updateTestDrive'
  | 'updateSpk'
  | 'updateFileConversation'
  | 'moveToCold'
  | 'insertExistLeads'
  | 'whatsappAnswer';

export interface ILeadsDocument<TIMESTAMP = firestore.Timestamp> {
  agentCode: string;
  area: string | null;
  title: string | null;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string | null;
  domicile: {
    provinceName: string;
    provinceCode: string;
    cityName: string;
    cityCode: string;
  } | null;
  vehicleUsage: 'individual' | 'shared' | 'corporate' | null;
  paymentPlan: 'cash' | 'credit' | null;
  hasVehicleLoan: boolean;
  vehicleOptions: LeadsVehicleOptions[];
  source: string;
  organization: LeadsOrganization;
  createdAt: TIMESTAMP;
  statusLevel: number;

  updatedAt: TIMESTAMP | null;
  notes: string;

  updateHistories: ILeadsNotesDocument<TIMESTAMP>[];

  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition' | null;
  downPaymentPlan: number | null;
  nextTotalVehicleOwnerShip: string | null;

  isTracking: boolean;

  idCard_number: string | null;
  driverLicense_number: string | null;

  agentName: string;
  phoneNumberAgent: string[];

  followUp: boolean;
  followUpRequestedAt: TIMESTAMP | null;
  followUpStartedAt: TIMESTAMP | null;
  followUpScheduledAt: TIMESTAMP | null;

  hotLeads: boolean;
  hotLeadsRequestedAt: TIMESTAMP | null;

  pendingRequestReactivateTracking: null | {
    isPending: boolean;
    requestedAt: TIMESTAMP;
    notes: string;
    response: null | {
      positive: boolean;
      updatedAt: TIMESTAMP;
    };
  };

  whatsapp: {
    idealPath: firestore.DocumentReference | null;
    messageId: string;
    response: {
      text: string;
      createdAt?: TIMESTAMP | null;
      repliedAt?: TIMESTAMP | null;
    } | null;
    failed?: boolean;
  } | null;
  webWhatsapp: null | {
    inbound: WebWhatsappInOutCounter<TIMESTAMP>;
    outbound: WebWhatsappInOutCounter<TIMESTAMP>;
  };

  testDrive: null | {
    model: string;
    date: TIMESTAMP;
    idTestDrive: string;
    notes: string;
  };

  spk: {
    spkNumber: string;
    createdAt: TIMESTAMP;
  } | null;

  requestPromo: {
    offerCode: string;
    createdAt: TIMESTAMP;
  } | null;

  needToMoveToColdAt: TIMESTAMP | null;

  fromFreeLeads: boolean;
  freeLeadsCreatedAt: TIMESTAMP | null;

  feedBackText: FeedBackTypes | null;
  feedBackVoice: FeedBackTypes | null;
  feedbackUpdatedAt: TIMESTAMP | null;
  firstMessageDirection: 'outbound' | 'inbound' | null;

  agentWhatsappConversations: {
    agentCode: string;
    agentName: string;
    bucketStorage: string;
    createdAt: TIMESTAMP;
    docRef: firestore.DocumentReference;

    startInboundChatAt: TIMESTAMP | null;
    endInboundChatAt: TIMESTAMP | null;
    startOutboundChatAt: TIMESTAMP;
    endOutboundChatAt: TIMESTAMP;
    startChatAt: TIMESTAMP;
    endChatAt: TIMESTAMP;
  }[];

  b2bCreatedSurveyOrders: LeadsB2BCreatedSurveyOrder<TIMESTAMP>[];
  tradeIn: {
    count: number;
  } | null;
}

interface WebWhatsappInOutCounter<TIMESTAMP> {
  total: number;
  updatedAt: TIMESTAMP;
}

export interface LeadsVehicleOptions {
  brand: {
    name: string;
  };
  model: {
    name: string;
  };
  variant: {
    code: string;
    name: string;
  };
  color: {
    code: string;
    name: string;
  };
}

interface LeadsB2BCreatedSurveyOrder<TIMESTAMP> {
  offerCode: string;
  createdAt: TIMESTAMP;
}

export type FeedBackTypes = 'INVALID' | 'VALID_NO_RESPONSE' | 'VALID_WITH_RESPONSE';
