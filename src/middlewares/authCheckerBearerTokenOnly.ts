import { RequestHand<PERSON> } from 'express';
import errorResponse from '../schema/errorResponse';
import { myAuth } from '../services/firebaseAdmin';

const authCheckerBearerTokenOnly: RequestHandler = async (req, res, next) => {
  const token = req.headers.authorization?.split('Bearer ')[1];

  if (!token) {
    return res.status(422).send(
      errorResponse({
        type: 'NOT_AUTHENTICATED',
        messages: 'Tidak terautentikasi',
      })
    );
  }

  try {
    res.locals.decodedToken = await myAuth().verifyIdToken(token);
    next();
  } catch (error) {
    return res.status(422).send(
      errorResponse({
        type: 'NOT_AUTHENTICATED',
        messages: 'Tidak terautentikasi',
      })
    );
  }
};

export default authCheckerBearerTokenOnly;
