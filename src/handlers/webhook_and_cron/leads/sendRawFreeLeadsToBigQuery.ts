import { Request<PERSON>and<PERSON> } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import moment from 'moment';
import { RawLeads } from '../../../types/firestore/raw_leads_types';
import { RawLeadsTable } from '../../../types/services/bigQuery/rawLeadsTable.types';
import { bigQuery, rawLeadsTable } from '../../../services/bigQueryService';
import { v4 } from 'uuid';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import collect from 'collect.js';

const sendRawFreeLeadsToBigQuery: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [],
  handler: async (req, res) => {
    const rawLeads = myFirestore.collection('raw_leads');

    const now = moment();

    const get = await rawLeads
      .where('donePushToBigQuery', '==', false)
      .where('shouldPushToBigQueryAt', '<=', now.toDate())
      .get();

    const rawLeadsResults: FirebaseFirestore.QueryDocumentSnapshot[] = [];

    get.forEach(result => {
      rawLeadsResults.push(result);
    });

    const rawLeadsResultCollections = collect(rawLeadsResults);
    const rawLeadsResultChunk = rawLeadsResultCollections.chunk(200);

    for (let i = 0; i < rawLeadsResultChunk.all().length; i++) {
      const part = rawLeadsResultChunk.all()[i];
      const batchRawFreeLeads = myFirestore.batch();

      const bigQueryRawLeads: RawLeadsTable[] = [];
      for (const rawLeads of part) {
        const uid = v4();
        const data = rawLeads.data() as RawLeads;

        batchRawFreeLeads.update(rawLeads.ref, {
          donePushToBigQuery: true,
          donePushToBigQueryAt: now.toDate(),
        });

        bigQueryRawLeads.push({
          city: data.city,
          city_group: data.mappedData.area,
          duplicated: data.duplicated,
          external_id: data.externalId,
          full_name: data.fullName,
          organization: data.organization,
          phone_number: data.phoneNumber,
          source: data.source,

          trimitra_city: data.mappedData.domicile
            ? {
                code: data.mappedData.domicile.cityCode,
                name: data.mappedData.domicile.cityName,
              }
            : null,
          trimitra_province: data.mappedData.domicile
            ? {
                code: data.mappedData.domicile.provinceCode,
                name: data.mappedData.domicile.provinceName,
              }
            : null,

          trimitra_vehicle: data.mappedData.vehicle
            ? {
                model: {
                  name: data.mappedData.vehicle.model.name,
                },
                variant: {
                  code: data.mappedData.vehicle.variant.code,
                  name: data.mappedData.vehicle.variant.name,
                },
                color: null,
              }
            : null,

          uid: uid,
          vehicle_model: data.vehicleModel,
          vehicle_variant: data.vehicleVariant,
          whatsapp_delivered: !!data.whatsapp?.statuses.delivered,
          whatsapp_delivered_at_timestamp: data.whatsapp?.statuses.delivered
            ? bigQuery.timestamp(data.whatsapp.statuses.delivered.toDate())
            : null,
          data_leads_created_at_timestamp: bigQuery.timestamp(data.createdAt.toDate()),
          bq_created_at_datetime: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
          bq_created_at_timestamp: bigQuery.timestamp(now.toDate()),
          is_in_my_leads: data.alreadyInMyLeads ?? false,
        });
      }

      try {
        await rawLeadsTable.insert(bigQueryRawLeads);
        await batchRawFreeLeads.commit();
      } catch (e) {
        console.log(JSON.stringify(e));
        // console.log("ERROR_BIGQUERY_INSERT_RAW_LEADS", e)
      }
    }

    res.send(
      successResponse({
        data: null,
      })
    );
  },
};

export default sendRawFreeLeadsToBigQuery;
