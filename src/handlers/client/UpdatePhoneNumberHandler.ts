import { HandlerTypes } from '../../types/handler.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { json } from 'express';
import authChecker from '../../middlewares/authChecker';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import ClientModel from '../../model/ClientModel';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';

interface ReqBody {
  clientRefPath: string;
  phoneNumberOwner: string;
  phoneNumberGuarantor: string;
  phoneNumberOrderMaker: string;
}

const updatePhoneNumberHandler: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    auth<PERSON><PERSON><PERSON>,
    json(),
    body('phoneNumberOwner')
      .notEmpty()
      .isNumeric()
      .withMessage('Nomor Telepon Pemilik dibutuhkan')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('phoneNumberGuarantor')
      .notEmpty()
      .isNumeric()
      .withMessage('Nomor Telepon Penjamin dibutuhkan')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('phoneNumberOrderMaker')
      .notEmpty()
      .isNumeric()
      .withMessage('Nomor Telepon Pemesan dibutuhkan')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    requestValidator,
  ],
  handler: async (req, res) => {
    const clientRef = myFirestore.doc(req.body.clientRefPath);

    try {
      await clientRef.update({
        'details.owner_phone_number': req.body.phoneNumberOwner,
        'details.order_maker_phone_number': req.body.phoneNumberOrderMaker,
        'details.guarantor_phone_number': req.body.phoneNumberGuarantor,
      });
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
        })
      );
    }

    return res.status(200).send(
      successResponse({
        data: null,
        type: 'UPDATED',
      })
    );
  },
};

export default updatePhoneNumberHandler;
