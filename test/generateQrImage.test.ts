import { describe, it, expect } from '@jest/globals';
import { generateQrCodeImage } from '../src/helpers/generateQrCodeImage';
import uploadImage from '../src/services/uploadImage';

describe('Generate QR Image', () => {
  it('should generate a QR image as File object', async () => {
    const qrCodeImageFile = await generateQrCodeImage('https://www.google.com', 'qrCode.png');

    // Verifikasi bahwa File object berhasil dibuat
    expect(qrCodeImageFile).toBeInstanceOf(File);
    expect(qrCodeImageFile.name).toBe('qrCode.png');
    expect(qrCodeImageFile.type).toBe('image/png');
    expect(qrCodeImageFile.size).toBeGreaterThan(0);
  });

  it('should upload a QR image to AWS S3', async () => {
    const qrCodeImageFile = await generateQrCodeImage('https://www.google.com', 'qris-qrcode.png', {
      width: 400,
      margin: 2,
    });

    const uploadedImageUrl = await uploadImage(qrCodeImageFile);
    console.log(uploadedImageUrl);
    expect(uploadedImageUrl).toMatch(/^https?:\/\//);
    expect(uploadedImageUrl).toMatch(/\.png$/);
  });
});
