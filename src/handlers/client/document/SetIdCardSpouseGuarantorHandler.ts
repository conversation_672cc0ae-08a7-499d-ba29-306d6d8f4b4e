import { EMaritalStatus } from '../../../types/client/document/marital-status-types';
import { CommonRegionFields } from '../../../types/client/document/region-types';
import { Request, Response } from 'express';
import { body } from 'express-validator';
import multer from 'multer';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import requestChecker from '../../../middlewares/requestValidator';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import { File } from '@google-cloud/storage';
import { IdCardSpouseGuarantorFields } from '../../../types/services/profile_service_types';
import { profileService } from '../../../services/profileService';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';

export interface IdCardSpouseGuarantorCaptureFields {
  idCardNumber: string;
  fullName: string;
  birthMother: string;
  placeOfBirth: string;
  dateOfBirth: Date;
  occupation: string;
  maritalStatus: EMaritalStatus;
  lastEducation: string;

  fullAddress: string;
  zipCode: string;
  province: CommonRegionFields;
  city: CommonRegionFields;
  district: CommonRegionFields;
  subDistrict: CommonRegionFields;
  neighbourhood: string;
  hamlet: string;

  phoneNumber: string;

  clientRef: string;

  imageUrl?: string;
}

export interface UpdateIdCardSpouseGuarantorHandlerReqBody
  extends IdCardSpouseGuarantorCaptureFields {}

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export default class SetIdCardSpouseGuarantorHandler {
  public static middlewares = [
    formDataParse.single('image'),

    body('clientRef').notEmpty(),

    body('phoneNumber')
      .notEmpty()
      .withMessage('Nomor telepon Penjamin diperlukan')
      .customSanitizer(input => {
        return phoneNumberCountryCodeSanitizer(input, '62', '62');
      }),
    body('idCardNumber')
      .trim()
      .notEmpty()
      .isNumeric()
      .isLength({ max: 16, min: 16 })
      .withMessage('KTP harus 16 digit'),
    body('fullName').trim().notEmpty(),
    body('placeOfBirth').trim().notEmpty(),
    body('dateOfBirth').trim().notEmpty().toDate(),
    body('occupation').trim().notEmpty(),
    body('maritalStatus').trim().notEmpty(),

    body('fullAddress').trim().notEmpty(),
    body('zipCode').trim().notEmpty(),
    body('province.code').trim().notEmpty(),
    body('province.name').trim().notEmpty(),
    body('city.code').trim().notEmpty(),
    body('city.name').trim().notEmpty(),
    body('district.code').trim().notEmpty(),
    body('district.name').trim().notEmpty(),
    body('subDistrict.code').trim().notEmpty(),
    body('subDistrict.name').trim().notEmpty(),
    body('neighbourhood')
      .trim()
      .isNumeric()
      .withMessage('RT Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('RT Maksimal memiliki 3 digit')
      .notEmpty(),
    body('hamlet')
      .trim()
      .isNumeric()
      .withMessage('RW Harus berupa angka')
      .isLength({ max: 3 })
      .withMessage('RW Maksimal memiliki 3 digit')
      .notEmpty(),

    body('birthMother').optional(),
    body('lastEducation').optional(),

    body('imageUrl').optional(),

    requestChecker,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, UpdateIdCardSpouseGuarantorHandlerReqBody>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);

    let file: File | null = null;
    if (req.file?.buffer) {
      file = bucketTrimitraIdeal.file(
        `client-documents/${clientDocRef.id}/ID_CARD_SPOUSE_GUARANTOR.jpg`
      );
    }

    try {
      if (file) {
        if (req.file) {
          await file.save(req.file.buffer, {
            public: true,
          });
        }
      }

      const getProfile = await profileService.getProfile(req.body.phoneNumber);
      if (getProfile.data.length === 0) {
        await profileService.updateProfile(
          {
            customerName: req.body.fullName,
            phone: req.body.phoneNumber,
          },
          req.body.phoneNumber
        );
      }

      const idCardPayload: IdCardSpouseGuarantorFields = {
        DateOfBirth: req.body.dateOfBirth,
        FullName: req.body.fullName.toUpperCase(),
        IdCardNumber: req.body.idCardNumber,
        PlaceOfBirth: req.body.placeOfBirth,
        MaritalStatus: req.body.maritalStatus,
        Occupation: req.body.occupation,

        IdCardImage: file?.publicUrl() ?? req.body.imageUrl,
        // IdCardImage: "https://storage.googleapis.com/chat-trimitra-ideal/client-documents/7e9ca9cf-0f1d-4fda-8fe2-79867d5d8a3d/id_card_order_maker.jpg",

        FullAddress: req.body.fullAddress.toUpperCase(),
        Province: {
          Code: req.body.province.code,
          Name: req.body.province.name.toUpperCase(),
        },
        City: {
          Code: req.body.city.code,
          Name: req.body.city.name.toUpperCase(),
        },
        District: {
          Code: req.body.district.code,
          Name: req.body.district.name.toUpperCase(),
        },
        SubDistrict: {
          Code: req.body.subDistrict.code,
          Name: req.body.subDistrict.name.toUpperCase(),
        },
        ZipCode: req.body.zipCode,
        Neighbourhood: req.body.neighbourhood,
        Hamlet: req.body.hamlet,

        BirthMother: req.body.birthMother?.toUpperCase() ?? null,
        LastEducation: req.body.lastEducation ?? null,
      };

      const update = await profileService.updateIdCardSpouseGuarantor(
        {
          ...idCardPayload,
        },
        req.body.phoneNumber
      );

      res.send(
        successResponse({
          type: 'UPDATED',
          data: update?.data,
        })
      );
    } catch (e: any) {
      if (file)
        await file.delete({
          ignoreNotFound: true,
        });
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
