import { Router } from 'express';
import addNewAdminHandler from '../handlers/admin/addNewAdminHandler';
import updateActiveStatusAdmin from '../handlers/admin/activateStatusAdminHandler';
import revokeTokenFirebaseAuthHandler from '../handlers/revokeTokenFirebaseAuthHandler';

const router = Router();

export default function (parent: Router) {
  parent.use('/admin', router);
  router.post('/add-new-admin', addNewAdminHandler.middlewares, addNewAdminHandler.handler);
  router.post(
    '/update-admin-status',
    updateActiveStatusAdmin.middlewares,
    updateActiveStatusAdmin.handler
  );
  router.post(
    '/revoke-token',
    revokeTokenFirebaseAuthHandler.middlewares,
    revokeTokenFirebaseAuthHandler.handler
  );
}
