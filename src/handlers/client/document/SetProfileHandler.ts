import { body } from 'express-validator';
import { json, Request, Response } from 'express';
import requestValidator from '../../../middlewares/requestValidator';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import { myFirestore } from '../../../services/firebaseAdmin';
import authChecker from '../../../middlewares/authChecker';
import { firestore } from 'firebase-admin';

export interface IRequestHandlerProfileHandler {
  phoneNumber: string;
  contactName: string;
  area?: {
    text: string;
    value: number;
  };
  clientRef: string;
  roomRef: string;
  organization: string;
  organization_group: string;
}

export default class SetProfileHandler {
  public static middlewares = [
    authChe<PERSON>,
    json(),

    body('clientRef').notEmpty(),
    body('roomRef').notEmpty(),
    body('phoneNumber').notEmpty(),
    body('contactName').notEmpty(),
    body('area.text').optional(),
    body('organization').notEmpty().withMessage('Organization Diperlukan'),
    requestValidator,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, IRequestHandlerProfileHandler>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const roomDocRef = myFirestore.doc(req.body.roomRef);

    try {
      const myBatch = myFirestore.batch();

      myBatch.update(clientDocRef, {
        profile: {
          area: req.body.area || null,
          name: req.body.contactName,
          phone_number: req.body.phoneNumber,
          organization: req.body.organization || null,
          organization_group: req.body.organization_group || null,
        },
      });

      const chatRoomDataUpdate: any = {
        headers: {
          title: req.body.contactName,
        },
      };

      if (req.body.area?.text && req.body.organization === 'amartahonda') {
        const findDepartment = await roomDocRef.parent.parent
          ?.collection('departments')
          .where('city_group', 'array-contains', req.body.area.text.toUpperCase())
          .get();

        if (!findDepartment?.empty) {
          findDepartment!.forEach(document => {
            Object.assign(chatRoomDataUpdate, {
              doc_department: document.ref,
            });
          });
        }
      }

      const fsTimestamp = firestore.Timestamp.now();

      chatRoomDataUpdate['organization'] = req.body.organization;
      chatRoomDataUpdate['organizationGroup'] = req.body.organization_group;
      chatRoomDataUpdate['organizationUpdatedBy'] = res.locals.account.email;
      chatRoomDataUpdate['organizationUpdatedAt'] = fsTimestamp;

      if (req.body.area) {
        chatRoomDataUpdate['cityGroup'] = req.body.area.text || req.body.area.value || null;
        chatRoomDataUpdate['cityGroupUpdatedAt'] = fsTimestamp;
        chatRoomDataUpdate['cityGroupUpdatedBy'] = res.locals.account.email;
      } else {
        chatRoomDataUpdate['cityGroup'] = null;
        chatRoomDataUpdate['cityGroupUpdatedAt'] = null;
        chatRoomDataUpdate['cityGroupUpdatedBy'] = null;
      }

      myBatch.update(roomDocRef, chatRoomDataUpdate);

      await myBatch.commit();

      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
