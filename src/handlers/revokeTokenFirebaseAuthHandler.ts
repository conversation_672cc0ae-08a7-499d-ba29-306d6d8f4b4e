import { HandlerTypes } from '../types/handler.types';
import authCheckerBearerTokenOnly from '../middlewares/authCheckerBearerTokenOnly';
import { DecodedIdToken } from 'firebase-admin/lib/auth';
import { myAuth } from '../services/firebaseAdmin';
import errorResponse from '../schema/errorResponse';
import successResponse from '../schema/successResponse';

const revokeTokenFirebaseAuthHandler: HandlerTypes = {
  middlewares: [authCheckerBearerTokenOnly],
  handler: async (req, res) => {
    const decodedToken = res.locals.decodedToken as DecodedIdToken;
    try {
      await myAuth().revokeRefreshTokens(decodedToken.uid);
    } catch (e) {
      return res.status(401).send(
        errorResponse({
          type: 'SERVER_ERROR',
        })
      );
    }

    return res.status(200).send(
      successResponse({
        data: null,
      })
    );
  },
};

export default revokeTokenFirebaseAuthHandler;
