import { myFirestore } from '../../../src/services/firebaseAdmin';
import ColdLeadsModel from '../../../src/model/ColdLeadsModel';
import LeadsModel from '../../../src/model/LeadsModel';
import aggregatesAgent from '../../../src/helpers/leads/aggregatesAgent';

async function restoreColdLeads() {
  const doc = myFirestore.collection('cold_leads').doc('amartavinfast-628561118820');

  const get = await doc.withConverter(ColdLeadsModel.converter).get();
  const data = get.data()!;

  const leadsDoc = myFirestore.collection('leads').doc(`${data.organization}-${data.phoneNumber}`);

  // Check exist
  const getLeads = await leadsDoc.withConverter(LeadsModel.converter).get();
  if (getLeads.exists) {
    throw new Error("Leads already exist");
  }

  const leads = new LeadsModel({
    agentCode: data.agentCode || '',
    area: data.area || null,
    title: data.title,
    firstName: data.firstName,
    lastName: data.lastName,
    phoneNumber: data.phoneNumber,
    email: data.email,
    domicile: data.domicile,
    vehicleUsage: data.vehicleUsage,
    paymentPlan: data.paymentPlan,
    downPaymentPlan: data.downPaymentPlan || null,
    vehicleOptions: data.vehicleOptions,
    source: data.source,
    organization: data.organization,
    createdAt: data.createdAt,
    hasVehicleLoan: data.hasVehicleLoan,
    statusLevel: data.statusLevel || 0,
    updatedAt: data.updatedAt || null,
    updateHistories: data.updateHistories || [],
    purchasePlan: data.purchasePlan,
    nextTotalVehicleOwnerShip: data.nextTotalVehicleOwnerShip,
    isTracking: true,
    notes: 'Restore from cold leads' || '',
    idCard_number: data.idCard_number || null,
    driverLicense_number: data.driverLicense_number || null,
    agentName: data.agentName || '',
    phoneNumberAgent: data.phoneNumberAgent || [],
    followUp: data.followUp || false,
    followUpRequestedAt: data.followUpRequestedAt || null,
    followUpStartedAt: data.followUpStartedAt || null,
    followUpScheduledAt: data.followUpScheduledAt || null,
    hotLeads: data.hotLeads || false,
    hotLeadsRequestedAt: data.hotLeadsRequestedAt || null,
    whatsapp: data.whatsapp || null,
    pendingRequestReactivateTracking: data.pendingRequestReactivateTracking || null,
    webWhatsapp: data.webWhatsapp || null,
    testDrive: data.testDrive || null,
    spk: data.spk || null,
    requestPromo: data.requestPromo || null,
    fromFreeLeads: data.fromFreeLeads || false,
    freeLeadsCreatedAt: data.freeLeadsCreatedAt || null,
    feedBackVoice: data.feedBackVoice || null,
    feedBackText: data.feedBackText || null,
    feedbackUpdatedAt: data.feedbackUpdatedAt || null,
    agentWhatsappConversations: data.agentWhatsappConversations || [],
    ref: leadsDoc,
  });

  await leadsDoc.withConverter(LeadsModel.converter).set(leads);
  await doc.delete();
  await aggregatesAgent(data.agentCode || "", data.organization);
}

restoreColdLeads();
