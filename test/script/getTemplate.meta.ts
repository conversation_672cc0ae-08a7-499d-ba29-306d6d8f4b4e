import { myFirestore } from '../../src/services/firebaseAdmin';
import metaServices from '../../src/services/MetaServices';
import { AxiosError } from 'axios';

const getTemplate = async () => {
  const projectCollection = myFirestore.collection('projects');
  const projectDoc = await projectCollection.doc('B6hfkRsnmcXsKUpFYjN8').get();

  const dataProject = projectDoc.data() as any;

  try {
    const getTemplate = await metaServices.getTemplate({
      meta: {
        whatsappBusinessAccountId: dataProject.meta.whatsappBusinessAccountId,
        bearerToken: dataProject.meta.bearer,
      },
      query: {
        name: 'service_thanks',
      },
    });

    const dataTemplates = getTemplate.data.data;

    if (dataTemplates.length > 0) {
      console.log(JSON.stringify(dataTemplates[0]));
    }
  } catch (e) {
    const error = e as AxiosError;
    console.log(error.response!.data);
  }
};

getTemplate();
