import { firestore } from 'firebase-admin';

export interface IFirestoreMessageEntity<
  DATE = firestore.Timestamp,
  REF = firestore.DocumentReference,
> {
  origin: {
    id: string;
    display_name: string;
    reference: REF;
  };
  message: {
    id: string;
    audio?: IFileContextWebhook;
    document?: IFileContextWebhook;
    image?: IFileContextWebhook;
    voice?: IFileContextWebhook;
    text?: ITextContextWebhook;
    sticker?: IStickerContext;
    location?: ILocationContextWebhook;
    system?: ITextContextWebhook;
    video?: IFileContextWebhook;
    context?: IReplyContext;
    unixtime: number;
    timestamp: firestore.Timestamp;
    type: TAvailablePostMessageContext;
    direction: EMessageDirection;
    interactive?: IInteractiveContextOutbound | IInteractiveInboundContext;
    referral?: IReferralMessageContext;
  };
  statuses?: TStatuses<DATE>;
  error?: {
    code: number;
    href: string;
    title: string;
  } | null;
  session_id?: string | null;
  bindContextAndDocuments?: {
    path: REF;
    context: string;
  }[];
  bindDocuments?: REF[];
}

type EMessageDirection = 'IN' | 'OUT';
export type TStatuses<DATE = firestore.Timestamp> = Record<EStatuses, DATE | null>;
export type EStatuses = 'sent' | 'delivered' | 'read' | 'failed';

export type TAvailablePostMessageContext =
  | 'audio'
  | 'document'
  | 'image'
  | 'location'
  | 'system'
  | 'text'
  | 'video'
  | 'voice'
  | 'context'
  | 'contacts'
  | 'sticker'
  | 'button'
  | 'referral'
  | 'interactive';

export interface IFileContextWebhook {
  id?: string;
  link?: string;
  caption: string;
}

export interface ILocationContextWebhook {
  address: string;
  latitude: number;
  longitude: number;
  name: string;
}

export interface ITextContextWebhook {
  body: string;
}

export interface IStickerContext {
  id: string;
  metadata: {
    emojis: string[];
    'is-first-party-sticker': number;
    'sticker-pack-id': string;
  };
  mime_type: string;
  sha256: string;
}

export interface IReplyContext {
  from: string;
  id: string;
  mentions: string[];
}

export interface IInteractiveContextOutbound {
  type: 'list' | 'button';
  header?: {
    type: 'text' | 'video' | 'image' | 'document';
    text?: string;
    video?: IFileContextWebhook;
    image?: IFileContextWebhook;
    document?: IFileContextWebhook;
  };
  body: {
    text: string;
  };
  footer?: {
    text: string;
  };
  action: {
    button?: string;
    buttons?: {
      type: 'reply';
      reply?: {
        title: string;
        id: string;
      };
    }[];

    sections?: {
      title: string;
      rows: {
        id: string;
        title: string;
        description?: string;
      }[];
    }[];
  };
}

export interface IInteractiveInboundContext {
  type: 'list_reply' | 'button_reply';
  list_reply?: {
    id: string;
    title: string;
    description: string;
  };
  button_reply?: {
    id: string;
    title: string;
  };
}
export interface IReferralMessageContext {
  source_id: string;
  media_type: string;
  body: string;
  source_url: string;
  video_url: string;
  thumbnail_url: string;
  headline: string;
  source_type: string;
}
