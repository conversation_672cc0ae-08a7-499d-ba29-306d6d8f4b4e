import { Router } from 'express';
import AddLeadHandler from '../handlers/leadsApi/AddLeadHandler';
import GetLeadHandler from '../handlers/leadsApi/GetLeadHandler';
import GetLeadCounter from '../handlers/leadsApi/GetLeadCounter';
import UpdateLeadsStatusHandler from '../handlers/leadsApi/UpdateLeadsStatusHandler';
import IncreaseOrDecreaseStatusLevelHandler from '../handlers/leadsApi/IncreaseOrDecreaseStatusLevelHandler';
import TrackOrUntrackedHandler from '../handlers/leadsApi/TrackOrUntrackedHandler';
import GetLeadsNotes from '../handlers/leadsApi/GetLeadsNotes';
import GetNotesHandler from '../handlers/leadsApi/GetNotesHandler';
import leadsAnswerNotificationHandler from '../handlers/webhook_and_cron/leads/leadsAnswerNotificationHandler';
import updateFollowUpStatus from '../handlers/leadsApi/UpdateFollowUpStatus';
import untrackInactiveLeadsInteraction from '../handlers/webhook_and_cron/leads/untrackInactiveLeadsInteraction';
import untrackLeadsNoReply from '../handlers/webhook_and_cron/leads/untrackLeadsNoReply';
import untrackInactiveLeadsNotes from '../handlers/webhook_and_cron/leads/untrackInactiveLeadsNotes';
import approveReactivateTracking from '../handlers/leadsApi/approveReactivateTracking';
import getLeadsRequestActivateTracking from '../handlers/leadsApi/getLeadsRequestActivateTracking';
import changeLeadsAgent from '../handlers/leadsApi/changeLeadsAgent';
import inboundOutboundWWJSCounterHandler from '../handlers/webhook_and_cron/leads/inboundOutboundWWJSCounterHandler';
import addLeadsTestDrive from '../handlers/leadsApi/AddLeadsTestDrive';
import moveLeadsToCold from '../handlers/leadsApi/MoveLeadsToCold';
import addLeadsFromOffer from '../handlers/leadsApi/AddLeadsFromOffer';
import AddRawLeadsOtocom from '../handlers/leadsApiExternal/AddRawLeadsOtocom';
import moveToCloudLeadsScheduled from '../handlers/webhook_and_cron/leads/moveToCloudLeadsScheduled';
import GetRawLeads from '../handlers/leadsApi/GetRawLeads';
import RawLeadsAnswerNotificationHandler from '../handlers/webhook_and_cron/leads/rawLeadsAnswerNotificationHandler';
import GetUnTrackingLeadHandler from '../handlers/leadsApi/GetUnTrackingLeadHandler';
import checkPhoneNumberStates from '../handlers/leadsApiExternal/checkPhoneNumberStates';
import multiAddFreeLeads from '../handlers/leadsApi/AddMultiFreeLeads';
import leadsFailedSendWANotification from '../handlers/webhook_and_cron/leads/leadsFailedSendWANotification';
import checkPhoneNumberStoredLocation from '../handlers/leadsApiExternal/checkPhoneNumberStoredLocation';
import addFreeLeadsZipCodeAsCity from '../handlers/leadsApiExternal/AddFreeLeadsZipCodeAsCity';
import AddFreeLeads from '../handlers/leadsApiExternal/AddFreeLeads/AddFreeLeads';
import autoAddFreeLeadsCron from '../handlers/webhook_and_cron/leads/autoAddFreeLeadsCron';
import getAgentConversationWithLeads from '../handlers/leadsApi/GetAgentConversationWithLeads';
import contactListBecomeLeadsPercentageByAgentCode from '../handlers/leadsApi/ContactListBecomeLeadsPercentageByAgentCode';
import getTotalLeadsByAgentCodes from '../handlers/leadsApi/GetTotalLeadsByAgentCodes';
import MoveLeadsToFreeLeads from '../handlers/leadsApi/MoveLeadsToFreeLeads';
import AddFreeLeadsOtocomFromFileXls from '../handlers/leadsApiExternal/AddFreeLeadsOtocomFromFileXls';
import formatDateV1 from '../middlewares/formatDate.v1';
import dailyReportAgentGetLeads from '../handlers/webhook_and_cron/leads/dailyReportAgentGetLeads';
import getFreeLeadsEncrypted from '../handlers/leadsApi/GetFreeLeads.encrypted';
import getFreeLeadChatEncrypted from '../handlers/leadsApi/GetFreeLeadsChat.encrypted';
import updateHotLeadsStatus from '../handlers/leadsApi/UpdateHotLeadsStatus';
import CheckFreeLeadsAcquisition from '../handlers/leadsApi/CheckFreeLeadsAcquisition';
import AddRatingFreeLeads from '../handlers/leadsApi/AddRatingFreeLeads';
import GetRatingFreeLeads from '../handlers/leadsApi/GetRatingFreeLeads';
import clientDataCompletionCronHandler from '../handlers/webhook_and_cron/clients/clientDataCompletion.cron.handler';
import GetColdLeads from '../handlers/leadsApi/GetColdLeads';

const router = Router();

export default function (parent: Router) {
  parent.use('/leads', router);

  router.post('/add-lead', AddLeadHandler.middlewares as any, AddLeadHandler.handler);
  router.get(
    '/get-lead/:organization/:agentCode',
    GetLeadHandler.middlewares as any,
    GetLeadHandler.handler
  );
  router.get(
    '/get-untracking-lead/:organization/:agentCode',
    GetUnTrackingLeadHandler.middlewares as any,
    GetUnTrackingLeadHandler.handler
  );
  router.get('/get-lead/:organization', GetLeadCounter.middlewares as any, GetLeadCounter.handler);
  router.post(
    '/get-total-leads-by-agent-codes',
    getTotalLeadsByAgentCodes.middlewares,
    getTotalLeadsByAgentCodes.handler
  );
  router.get(
    '/get-notes/:organization/:phoneNumber',
    GetLeadsNotes.middlewares,
    GetLeadsNotes.handler
  );

  router.get('/get-notes/:organization', GetNotesHandler.middlewares, GetNotesHandler.handler);

  router.post(
    '/update-level',
    UpdateLeadsStatusHandler.middlewares,
    UpdateLeadsStatusHandler.handler
  );
  router.post(
    '/update-level/:type',
    IncreaseOrDecreaseStatusLevelHandler.middlewares,
    IncreaseOrDecreaseStatusLevelHandler.handler
  );

  router.post(
    '/update-track/:type',
    TrackOrUntrackedHandler.middlewares,
    TrackOrUntrackedHandler.handler
  );
  router.post(
    '/approve-reactivate-leads',
    approveReactivateTracking.middlewares,
    approveReactivateTracking.handler
  );
  router.get(
    '/get-reactivate-leads/:organization',
    getLeadsRequestActivateTracking.middlewares,
    getLeadsRequestActivateTracking.handler
  );

  router.post(
    '/move-to-free-leads',
    MoveLeadsToFreeLeads.middlewares as any,
    MoveLeadsToFreeLeads.handler
  );

  router.post('/move-to-cold', moveLeadsToCold.middlewares, moveLeadsToCold.handler);

  router.post('/change-leads-agent', changeLeadsAgent.middlewares, changeLeadsAgent.handler);

  router.post(
    '/update-follow-up-status',
    updateFollowUpStatus.middlewares,
    updateFollowUpStatus.handler
  );

  router.post(
    '/check-free-leads',
    CheckFreeLeadsAcquisition.middlewares,
    CheckFreeLeadsAcquisition.handler
  );

  router.post('/add-rating-free-leads', AddRatingFreeLeads.middlewares, AddRatingFreeLeads.handler);

  router.post(
    '/update-hot-leads-status',
    updateHotLeadsStatus.middlewares,
    updateHotLeadsStatus.handler
  );

  router.post(
    '/untrack-inactive-interaction',
    untrackInactiveLeadsInteraction.middlewares,
    untrackInactiveLeadsInteraction.handler
  );

  router.post(
    '/untrack-inactive-notes',
    untrackInactiveLeadsNotes.middlewares,
    untrackInactiveLeadsNotes.handler
  );

  router.post(
    '/move-leads-to-cold',
    moveToCloudLeadsScheduled.middlewares,
    moveToCloudLeadsScheduled.handler
  );

  router.post(
    '/untrack-leads-no-answer',
    untrackLeadsNoReply.middlewares,
    untrackLeadsNoReply.handler
  );

  router.post(
    '/count-chats-in-out',
    inboundOutboundWWJSCounterHandler.handler,
    inboundOutboundWWJSCounterHandler.middlewares
  );

  router.post('/leads-test-drive', addLeadsTestDrive.middlewares, addLeadsTestDrive.handler);

  router.post('/add-free-leads', AddFreeLeads.middlewares, AddFreeLeads.handler);

  router.post('/add-free-leads-raw', AddRawLeadsOtocom.middlewares, AddRawLeadsOtocom.handler);

  router.post(
    '/otocom-spreadsheet-leads',
    AddFreeLeadsOtocomFromFileXls.middlewares,
    AddFreeLeadsOtocomFromFileXls.handler
  );

  router.get(
    '/get-raw-leads/:organization',
    GetRawLeads.middlewares as any,
    GetRawLeads.handler as any
  );

  router.get(
    '/check-phone-number',
    checkPhoneNumberStates.middlewares,
    checkPhoneNumberStates.handler
  );
  router.post('/add-free-lead', multiAddFreeLeads.middlewares, multiAddFreeLeads.handler); // Akan Dihapus
  router.post('/multi-add-free-leads', multiAddFreeLeads.middlewares, multiAddFreeLeads.handler);
  router.get(
    '/check-phone-number-stored',
    checkPhoneNumberStoredLocation.middlewares,
    checkPhoneNumberStoredLocation.handler
  );

  router.post(
    '/add-free-lead/:source',
    addFreeLeadsZipCodeAsCity.middlewares,
    addFreeLeadsZipCodeAsCity.handler
  );

  router.get(
    '/get-agent-conversation-with-leads',
    formatDateV1,
    getAgentConversationWithLeads.middlewares,
    getAgentConversationWithLeads.handler
  );

  router.get(
    '/get-percentage-contact-become-leads/:agentCode',
    contactListBecomeLeadsPercentageByAgentCode.middlewares,
    contactListBecomeLeadsPercentageByAgentCode.handler
  );

  router.get(
    '/get-free-leads/:organization',
    getFreeLeadsEncrypted.middlewares as any,
    getFreeLeadsEncrypted.handler as any
  );

  router.get(
    '/get-free-leads-chat',
    getFreeLeadChatEncrypted.middlewares as any,
    getFreeLeadChatEncrypted.handler as any
  );

  router.post('/spk-to-leads', addLeadsFromOffer.middlewares, addLeadsFromOffer.handler);

  router.post('/get-rating-free-leads', GetRatingFreeLeads.middlewares, GetRatingFreeLeads.handler);

  router.post('/add-rating-free-leads', AddRatingFreeLeads.middlewares, AddRatingFreeLeads.handler);

  router.get('/cold-leads', GetColdLeads.middlewares, GetColdLeads.handler);

  /* Webhook and Cron */
  router.post(
    '/webhook/notify-whatsapp-reply/raw-leads',
    RawLeadsAnswerNotificationHandler.middlewares as any,
    RawLeadsAnswerNotificationHandler.handler as any
  );
  router.post(
    '/webhook/notify-whatsapp-reply',
    leadsAnswerNotificationHandler.middlewares,
    leadsAnswerNotificationHandler.handler
  );

  router.post('/webhook/notification-whatsapp-failed', leadsFailedSendWANotification.handler);

  router.post(
    '/auto-add-free-leads',
    autoAddFreeLeadsCron.middlewares,
    autoAddFreeLeadsCron.handler
  );

  router.post(
    '/daily-report-agent-get-leads',
    dailyReportAgentGetLeads.middlewares as any,
    dailyReportAgentGetLeads.handler as any
  );

  router.post(
    '/client-data-completion-logs',
    clientDataCompletionCronHandler.middlewares,
    clientDataCompletionCronHandler.handler
  );
}
