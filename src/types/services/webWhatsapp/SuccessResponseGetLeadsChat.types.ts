export interface SuccessResponseGetLeadsChat {
  success: boolean;
  data: Data;
}

export interface Data {
  id: string;
  name: string;
  isGroup: boolean;
  timestamp: number;
  unreadCount: number;
  messages: Message[];
}

export interface Message {
  body: string;
  from: string;
  deviceType: string;
  fromMe: boolean;
  hasMedia: boolean;
  id: string;
  to: string;
  type: string;
  isForwarded?: boolean;
  timestamp: number;
}
