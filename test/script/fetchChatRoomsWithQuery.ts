import { myFirestore } from '../../src/services/firebaseAdmin';

const fetchChatRoomsWithQuery = async () => {
  const collection = myFirestore.collection('/projects/v8GGopG1v89nd46aajEX/chat_rooms');

  const get = await collection
    .where('blocked', '==', false)
    .orderBy('recent_chat.timestamp', 'desc')
    .limit(20)
    // .where("label", "==", myFirestore.doc("/projects/v8GGopG1v89nd46aajEX/labels/8ab7agmjHroXC5ycWqJ0"))
    // .where("doc_department", "==", myFirestore.doc("/projects/v8GGopG1v89nd46aajEX/departments/bRvMKtA9Txf5nqL4O5T9"))
    .count()
    .get();
  console.log(get.data());
};

fetchChatRoomsWithQuery();
