import { HandlerTypes } from '../../../types/handler.types';
import { json } from 'express';
import { body } from 'express-validator';
import dayjs from 'dayjs';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import { collect } from 'collect.js';
import successResponse from '../../../schema/successResponse';
import { ActiveLeadsTableTypes } from '../../../types/services/bigQuery/activeLeads.table.bigquery.types';
import { v4 } from 'uuid';
import {activeLeadsTable, bigQuery} from '../../../services/bigQueryService';

interface RequestBody {
  agentCodes: string[];
  organization: string;
  todayDate: string;
}

interface ResultAgent {
  agentCode: string;
  organization: string;
  todayActive: number;
  yesterdayActive: number;
  active: number;
  newLeadsWaConv: number;
  newLeadsQr: number;
  newLeadsBuy: number;
  updateWaLeadsConv: number;
}

const dailyReportAgentGetLeads: HandlerTypes<any, any, RequestBody> = {
  middlewares: [
    json(),
    body('agentCodes')
      .notEmpty()
      .withMessage('Agent codes are required')
      .isArray({ min: 1 })
      .withMessage('At least one agent code must be provided'),
    body('organization')
      .notEmpty()
      .withMessage('Organization is required')
      .isIn(['amartahonda', 'amartachery', 'amartaneta', 'amartavinfast'])
      .withMessage(
        'Invalid organization. Must be one of: amartahonda, amartachery, amartaneta, amartavinfast'
      ),
    body('todayDate')
      .notEmpty()
      .withMessage('Date is required')
      .custom(input => {
        if (!dayjs(input).isValid()) {
          throw new Error('Invalid date format');
        }
        return true;
      }),
    requestValidator,
  ],
  handler: async (req, res) => {
    const today = dayjs(req.body.todayDate);
    const startDateToday = today.startOf('day');
    const endDateToday = today.endOf('day');

    const leadsCollection = myFirestore.collection('leads');
    const leadsNotesCollection = myFirestore.collection('leads_notes');

    const getLeadsQueryToday = leadsCollection
      .withConverter(LeadsModel.converter)
      .where('createdAt', '>=', startDateToday.toDate())
      .where('createdAt', '<', endDateToday.toDate())
      .where('organization', '==', req.body.organization);

    const getLeadsNotesQueryToday = leadsNotesCollection
      .where('updatedAt', '>=', startDateToday.toDate())
      .where('updatedAt', '<', endDateToday.toDate())
      .where('organization', '==', req.body.organization);

    const getLeadsQueryActiveBeforeToday = leadsCollection
      .withConverter(LeadsModel.converter)
      .where('createdAt', '<', startDateToday.toDate())
      .where('isTracking', '==', true)
      .where('organization', '==', req.body.organization);

    const results: ResultAgent[] = [];
    const activeLeads: ActiveLeadsTableTypes[] = [];

    for (const agentCode of req.body.agentCodes) {
      const getLeadsToday = await getLeadsQueryToday.where('agentCode', '==', agentCode).get();

      const getLeadsNotesToday = await getLeadsNotesQueryToday
        .where('agentCode', '==', agentCode)
        .get();

      const getLeadsActiveBeforeToday = await getLeadsQueryActiveBeforeToday
        .where('agentCode', '==', agentCode)
        .get();

      const dataYesterdayLeads: LeadsModel[] = [];
      getLeadsActiveBeforeToday.forEach(result => {
        const data = result.data();
        dataYesterdayLeads.push(data);
      });

      const dataTodayLeads: LeadsModel[] = [];
      getLeadsToday.forEach(result => {
        const data = result.data() as LeadsModel;
        dataTodayLeads.push(data);
      });
      const dataTodayLeadsNotes: ILeadsNotesDocument[] = [];
      getLeadsNotesToday.forEach(result => {
        const data = result.data() as ILeadsNotesDocument;
        dataTodayLeadsNotes.push(data);
      });
      const dataLeadsTodayActiveCollect = collect(dataTodayLeads);
      const dataLeadsNotesTodayCollect = collect(dataTodayLeadsNotes);

      const dataTodayActiveCount = getLeadsToday.size;

      // New Leads Wa Conv
      const dataTodayCreatedFromTriforce = dataLeadsTodayActiveCollect
        .filter(value => {
          return value.source === 'triforce';
        })
        .count();

      // New Leads QR
      const dataTodayCreatedFromQr = dataLeadsTodayActiveCollect
        .filter(value => {
          return value.source === 'triforce-qrcode';
        })
        .count();

      // New Leads Buy
      const dataTodayCreatedFromBuy = dataLeadsTodayActiveCollect
        .filter(value => {
          return value.fromFreeLeads;
        })
        .count();

      // Update Wa Leads Conv
      const dataTodayUpdatedFromTriforce = dataLeadsNotesTodayCollect
        .filter(value => {
          return value.event === 'updateFileConversation';
        })
        .count();

      const dataYesterdayActiveCount = dataYesterdayLeads.length;

      results.push({
        agentCode: agentCode,
        organization: req.body.organization,
        newLeadsWaConv: dataTodayCreatedFromTriforce,
        newLeadsQr: dataTodayCreatedFromQr,
        newLeadsBuy: dataTodayCreatedFromBuy,
        updateWaLeadsConv: dataTodayUpdatedFromTriforce,
        todayActive: dataTodayActiveCount,
        yesterdayActive: dataYesterdayActiveCount,
        active: dataTodayActiveCount + dataYesterdayActiveCount,
      });

      const pushToActiveLeads = (leads: LeadsModel, uuid: string) => {
        activeLeads.push({
          uuid: uuid,
          agent_name: leads.agentName,
          agent_code: leads.agentCode,
          organization: leads.organization,
          city_group: leads.area,
          leads_phone_number: leads.phoneNumber,
          leads_full_name: leads.firstName,
          leads_salutation: leads.title,
          domicile: {
            province_code: leads.domicile?.provinceCode || null,
            province_name: leads.domicile?.provinceName || null,
            city_code: leads.domicile?.cityCode || null,
            city_name: leads.domicile?.cityName || null,
          },
          vehicle: leads.vehicleOptions?.[0] || null,
          source: leads.source,
          bq_created_at_timestamp: bigQuery.timestamp(today.toDate()),
          leads_created_at_timestamp: bigQuery.timestamp(leads.createdAt),
        });
      }

      dataYesterdayLeads.forEach(value => {
        const uuidV4 = v4();
        pushToActiveLeads(value, uuidV4);
      });

      dataTodayLeads.forEach(value => {
        const uuidV4 = v4();
        pushToActiveLeads(value, uuidV4);
      });
    }

    activeLeadsTable.insert(activeLeads)
      .then(() => {
        console.log('SUCCESS_INSERT_ACTIVE_LEADS');
      })
      .catch(e => {
        console.log('ERROR_INSERT_ACTIVE_LEADS', e);
      });

    res.send(
      successResponse({
        data: results,
        type: 'FETCHED',
      })
    );
  },
};

export default dailyReportAgentGetLeads;
