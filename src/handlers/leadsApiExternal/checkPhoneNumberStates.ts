import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import axios, { AxiosError } from 'axios';
import { myFirestore } from '../../services/firebaseAdmin';
import successResponse from '../../schema/successResponse';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import webWhatsappServices from '../../services/WebWhatsappServices';
import errorResponse from '../../schema/errorResponse';

interface Queries {
  phoneNumber: string;
  organization: string;
}

const checkPhoneNumberWhatsappIsRegistered = async (
  phoneNumber: string
): Promise<{ success: boolean; data: { isRegistered: boolean }; messages: string }> => {
  try {
    const get = await webWhatsappServices.checkPhoneNumberActive(phoneNumber);
    return get.data;
  } catch (e: any) {
    let error = e as AxiosError;
    return {
      success: false,
      data: {
        isRegistered: false,
      },
      messages: 'Error web whatsapp js: ' + error.message,
    };
  }
};

const checkPhoneNumberStates: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, any, Queries>;
} = {
  middlewares: [
    query('phoneNumber')
      .notEmpty()
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    query('organization').notEmpty(),

    requestValidator,
  ],
  handler: async (req, res) => {
    const collection = myFirestore.collection('leads');

    const checkWaResponse = {
      isActive: false,
      isError: false,
      messages: '',
    };

    const checkPhoneNumberInMyLeadsResponse = {
      isInMyLeads: false,
      messages: '',
    };

    // const checkWa = await checkPhoneNumberWhatsappIsRegistered(req.query.phoneNumber)

    // if(checkWa.success) {
    //     if(checkWa.data.isRegistered) checkWaResponse.isActive = true;
    //     else {
    //         checkWaResponse.isActive = false;
    //         checkWaResponse.messages = "Nomor Telepon belum teregistrasi di WA."
    //     }
    // } else {
    //     checkWaResponse.isError = true;
    //     checkWaResponse.messages = "Terjadi error ketika cek nomor telepon apakah teregistrasi di WA atau belum."
    // }

    const checkInMyLeads = await collection
      .doc(`${req.query.organization}-${req.query.phoneNumber}`)
      .get();

    if (checkInMyLeads.exists) {
      checkPhoneNumberInMyLeadsResponse.isInMyLeads = true;
      checkWaResponse.messages = 'Nomor Telepon dengan Organisasi sama sudah terdaftar.';
    }

    return res.send(
      successResponse({
        data: {
          // phoneNumberActiveWA: checkWaResponse,
          phoneNumberInMyLeads: checkPhoneNumberInMyLeadsResponse,
        },
      })
    );
  },
};

export default checkPhoneNumberStates;
