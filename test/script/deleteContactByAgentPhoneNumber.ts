import { myFirestore } from '../../src/services/firebaseAdmin';

async function deleteContactByAgentPhoneNumber() {
  const phoneNumberAgent = '628561118820';
  const collection = myFirestore.collection('agent_contacts');

  const get = await collection.where('agentPhoneNumber', '==', phoneNumberAgent).get();

  const batch = myFirestore.batch();

  get.forEach(r => {
    batch.delete(r.ref);
  });

  await batch.commit();
}

deleteContactByAgentPhoneNumber();
