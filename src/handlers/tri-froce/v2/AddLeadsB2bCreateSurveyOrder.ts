import { HandlerTypes } from '../../../types/handler.types';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import moment from 'moment';
import successResponse from '../../../schema/successResponse';
import { json } from 'express';

interface ReqBody {
  leadsPhoneNumber: string;
  agentCode: string;
  organization: string;
  offerCode: string;
}

const addLeadsB2bCreateSurveyOrder: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    triForceAuthChecker,
    json(),
    body('agentCode').notEmpty(),
    body('leadsPhoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('organization').notEmpty(),
    body('offerCode').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const collection = myFirestore.collection('leads');
    const getLeads = await collection
      .withConverter(LeadsModel.converter)
      .doc(`${req.body.organization}-${req.body.leadsPhoneNumber}`)
      .get();

    if (!getLeads.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    if (leads.agentCode !== req.body.agentCode) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages:
            'Leads tidak ditemukan atau Leads ini bukan didaftarkan oleh kode agen yang bersangkutan',
        })
      );
    }

    const now = moment();

    leads.b2bCreatedSurveyOrders.push({
      createdAt: now.toDate(),
      offerCode: req.body.offerCode,
    });

    await leads.ref.withConverter(LeadsModel.converter).set(leads);

    res.send(
      successResponse({
        type: 'UPDATED',
        data: null,
      })
    );
  },
};

export default addLeadsB2bCreateSurveyOrder;
