/* eslint-disable @typescript-eslint/no-explicit-any */
import { Router } from 'express';
import trimobiAddNewListingHandler from '../handlers/client/trimobi/trimobiAddNewListing.handler';
import trimobiBuyNewPlanHandler from '../handlers/client/trimobi/trimobiBuyNewPlan.handler';
import trimobiGetPurchasedPlanHandler from '../handlers/client/trimobi/trimobiGetPurchasedPlan.handler';
import trimobiUpgradePlanHandler from '../handlers/client/trimobi/trimobiUpgradePlanHandler';
import trimobiGetPlanCheckoutStatusHandler from '../handlers/client/trimobi/trimobiGetPlanCheckoutStatus.handler';

const router = Router();

const clientTrimobiRoutes = (parent: Router) => {
  parent.use('/client/trimobi', router);
  router.post(
    '/add-new-listing',
    trimobiAddNewListingHandler.middlewares,
    trimobiAddNewListingHandler.handler
  );
  router.post(
    '/buy-new-plan',
    trimobiBuyNewPlanHandler.middlewares,
    trimobiBuyNewPlanHandler.handler
  );
  router.get(
    '/get-purchased-plan',
    trimobiGetPurchasedPlanHandler.middlewares,
    trimobiGetPurchasedPlanHandler.handler
  );
  router.post(
    '/upgrade-plan',
    trimobiUpgradePlanHandler.middlewares,
    trimobiUpgradePlanHandler.handler
  );
  router.get(
    '/get-plan-checkout-status',
    trimobiGetPlanCheckoutStatusHandler.middlewares,
    trimobiGetPlanCheckoutStatusHandler.handler as any
  );
};

export default clientTrimobiRoutes;
