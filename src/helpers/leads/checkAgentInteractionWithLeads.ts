import webWhatsappServices from '../../services/WebWhatsappServices';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import moment, { now } from 'moment';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import fetchAgent from '../../services/agent/fetchAgent';
import aggregatesAgent from './aggregatesAgent';
import { log } from 'util';
import { AxiosError } from 'axios';
import { leadsNotesCounter } from './leadsNotes';

async function checkAgentInteractionWithLeads() {
  const agentCodes: string[] = [
    // "6400"
  ];
  try {
    const getLoggedInWA = await webWhatsappServices.checkActiveInstance();
    const data = getLoggedInWA.data.data;
    for (const dataKey in data) {
      if (data[dataKey].loggedIn) {
        agentCodes.push(dataKey);
      }
    }
  } catch (e) {}

  const collections = myFirestore.collection('leads');
  const momentObjNow = moment().subtract(1, 'day');
  console.log(
    'CRON_AGENT_CHECK_INTERACTION_WITH_LEADS',
    momentObjNow.format('YYYY-MM-DD HH:mm:ss')
  );
  for (const agentCode of agentCodes) {
    const batch = myFirestore.batch();

    const leadsList: LeadsModel[] = [];
    const getLeads = await collections
      .where('isTracking', '==', true)
      .where('agentCode', '==', agentCode)
      .where('organization', '==', 'amartahonda')
      .withConverter(LeadsModel.converter)
      .get();

    getLeads.forEach(result => {
      leadsList.push(result.data());
    });

    // console.log("LEADS", agentCode, leadsList.map(value => value.phoneNumber));

    for (const leads of leadsList) {
      try {
        // console.log("START_FETCH", agentCode, leads.phoneNumber);
        const fetchMessages = await webWhatsappServices.getChatFromLeads({
          agentCode: agentCode,
          leadsPhoneNumber: leads.phoneNumber,
        });
        // console.log("RESULT_FETCH", agentCode, leads.phoneNumber, fetchMessages.data.data);
        const messages = fetchMessages.data.data.messages;
        let untrackThis = false;
        let _notes: string = '';

        if (messages.length === 0) {
          untrackThis = true;
          _notes = 'Tidak ada riwayat percakapan sama sekali.';
          // console.log("ZERO_CHAT", agentCode, leads.phoneNumber)
        } else {
          messages.reverse();
          const findFromMe = messages.find(m => {
            const momentObj = moment.unix(m.timestamp);
            return momentObj.isSame(momentObjNow, 'day') && m.fromMe;
          });

          if (!findFromMe) {
            // console.log("NO_OUTBOUND_MESSAGE", agentCode, leads.phoneNumber)
            untrackThis = true;
            _notes =
              'Tidak ditemukan pesan keluar per tanggal ' + momentObjNow.format('YYYY-MM-DD');
          }
        }

        if (untrackThis) {
          // console.log("UNTRACK_LEADS", leads.agentCode, leads.phoneNumber)
          let agentName: string | null = null;
          if (!leads.agentName) {
            try {
              const fetch = await fetchAgent(leads.agentCode);
              agentName = fetch.name;
            } catch (e: any) {
              agentName = 'NO_NAME';
            }
          } else {
            agentName = leads.agentName;
          }

          const notesCounter = await leadsNotesCounter(leads);

          let notes: ILeadsNotesDocument<Date> = {
            agentCode: leads.agentCode,
            event: 'untrack',
            notes: 'Leads di Untrack oleh sistem : ' + _notes,
            organization: leads.organization,
            phoneNumber: leads.phoneNumber,
            statusLevel: leads.statusLevel,
            updatedAt: new Date(),
            updatedByUser: null,
            firstName: leads.firstName,
            lastName: leads.lastName,
            agentName: agentName,
            moveToCold: false,
            reactivate: {
              currentTotal: notesCounter.totalReactivate,
            },
            totalUpdateNotes: notesCounter.totalNotes + 1,
          };

          leads.notes = notes.notes;
          leads.isTracking = false;
          leads.updatedAt = new Date();
          leads.updateHistories.push({
            ...notes,
          });

          batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
          batch.set(myFirestore.collection('leads_notes').doc(), notes);
        } else {
          // console.log("TRACK_LEADS", leads.agentCode, leads.phoneNumber)
        }
      } catch (e: any) {
        let error: any;
        if ('request' in e) {
          const _e = e as AxiosError;
          error = {
            messages: _e.message,
            response: _e.response?.data,
          };
        } else {
          error = e;
        }
        console.log('ERROR_SET', agentCode, leads.phoneNumber, error);
      }
    }

    try {
      if (process.env.NODE_ENV === 'production') {
        await batch.commit();
        await aggregatesAgent(agentCode, 'amartahonda').then();
      }
    } catch (e) {
      console.log('ERROR_COMMIT', agentCode, e);
    }
  }
}

export default checkAgentInteractionWithLeads;
