import { HandlerTypes } from '../../types/handler.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import { myFirestore } from '../../services/firebaseAdmin';
import requestValidator from '../../middlewares/requestValidator';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import { json } from 'express';

interface ReqBody {
  phoneNumber: string;
  clientRefPath: string;
  type: 'owner' | 'guarantor' | 'orderMaker';
}

const setPhoneNumberAsHandler: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    json(),
    body('phoneNumber')
      .notEmpty()
      .isMobilePhone('id-ID')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('clientRefPath').notEmpty(),
    body('type').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const doc = myFirestore.doc(req.body.clientRefPath);

    let pathField = 'details.';

    switch (req.body.type) {
      case 'owner':
        pathField += 'owner_phone_number';
        break;
      case 'guarantor':
        pathField += 'guarantor_phone_number';
        break;
      case 'orderMaker':
        pathField += 'order_maker_phone_number';
        break;
    }

    let update = {
      [pathField]: req.body.phoneNumber,
    };

    try {
      await doc.update(update);
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
        })
      );
    }

    return res.status(200).send(
      successResponse({
        type: 'UPDATED',
        data: update,
      })
    );
  },
};

export default setPhoneNumberAsHandler;
