import { CommonRegionFields } from '../client/document/region-types';
import { EMaritalStatus } from '../client/document/marital-status-types';

export interface IFamilyRegistrationDocument {
  family_register_number: string;
  status_of_intra_group_relation: string;
  family_register_image?: string | null;
  last_education: string | null;
}

export enum EFamilyRelation {
  HEADS_OF_FAMILY = 'HEADS_OF_FAMILY',
  HUSBAND = 'HUSBAND',
  WIFE = 'WIFE',
  CHILD = 'CHILD',
  SON_IN_LAW = 'SON_IN_LAW',
  GRANDCHILD = 'GRANDCHILD',
  PARENTS = 'PARENTS',
  IN_LAWS = 'IN_LAWS',
  ANOTHER_FAMILY = 'ANOTHER_FAMILY',
  HELPER = 'HELPER',
  OTHER = 'OTHER',
}

export interface IFamilyRegisterDocument {
  family_register_number: string;
  full_address: string;
  zip_code: string;

  province: CommonRegionFields;
  city: CommonRegionFields;
  district: CommonRegionFields;
  sub_district: CommonRegionFields;

  family_register_image?: string | null;

  members: FamilyMember[];
}

export interface FamilyMember {
  id_card_number: string;
  full_name: string;
  family_relation: EFamilyRelation;
  marital_status: EMaritalStatus;
}

export interface IFamilyRegister<DATE = Date> {
  familyRegisterNumber: string;
  familyRegisterImage: string;
  updatedAt: DATE;
}
