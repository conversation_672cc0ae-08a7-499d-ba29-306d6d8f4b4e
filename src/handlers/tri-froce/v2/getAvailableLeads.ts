import { Request, Response } from 'express';
import triForceAuthChecker from '../../../middlewares/triforce<PERSON>uth<PERSON>he<PERSON>';
import { param } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import successResponse from '../../../schema/successResponse';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { LeadsOrganization } from '../../../types/firestore/leads_model.types';

const getAvailableLeads = {
  middlewares: [
    triForce<PERSON>uth<PERSON><PERSON><PERSON>,
    param('organization').notEmpty(),
    param('agentCode').notEmpty(),
    requestValidator,
  ],

  handler: async (
    req: Request<
      {
        organization: LeadsOrganization;
        agentCode: string;
      },
      ResponseSchema
    >,
    res: Response
  ) => {
    const collections = myFirestore.collection('leads');
    const getQuery = await collections
      .where('agentCode', '==', req.params.agentCode)
      .where('isTracking', '==', true)
      .where('organization', '==', req.params.organization)
      .orderBy('createdAt', 'desc')
      .withConverter(LeadsModel.converter)
      .get();

    const leads: LeadsModel[] = [];

    getQuery.forEach(result => {
      const data = result.data();
      leads.push(data);
    });

    res.send(
      successResponse({
        data: leads.map(value => value.toJsonResponse()),
        type: 'FETCHED',
      })
    );
  },
};

export default getAvailableLeads;
