import { j<PERSON>, RequestHand<PERSON> } from 'express';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../middlewares/requestValidator';
import catalogueService from '../services/catalogueService';
import { ResponseSchema } from '../schema/types/ResponseSchema';
import { VariantCatalogue } from '../types/services/catalogueServices.types';
import errorResponse from '../schema/errorResponse';
import moment, { Moment } from 'moment';
import generateRandomString from '../helpers/randomString';
import { bucketTrimitraIdeal } from '../services/cloudStorage';
import successResponse from '../schema/successResponse';
import { IPriceListFile } from '../types/priceListFile.types';
import authChecker from '../middlewares/authChecker';
import { IFirestoreAdminEntity } from '../types/firestore/messages/message_types';

interface PriceListRow {
  downPayment: number;
  tenor: number;
  installment: number;
  installmentDiscount: number;
  tenorDiscount: number;
}

interface ReqBody {
  title: string;
  subTitle: string;
  phoneNumber: string;
  variantCode: string;
  cityGroup: string;
  admin: string;
  priceListNormal: PriceListRow[];
  source: string;
  expiredAt: Moment;
}

const createPriceListFileHandler: {
  middlewares: RequestHandler<any, ResponseSchema, ReqBody, any>[];
  handler: RequestHandler<any, ResponseSchema, ReqBody, any, { account: IFirestoreAdminEntity }>;
} = {
  middlewares: [
    json(),
    body('source').notEmpty().trim().isString(),
    body('title').notEmpty().trim().isString(),
    body('admin').optional({ values: 'falsy' }).trim().isString(),
    body('expiredAt')
      .notEmpty()
      .trim()
      .custom(v => {
        return moment(v).isValid();
      })
      .customSanitizer(v => moment(v)),
    body('subTitle').optional({ values: 'falsy' }).trim().isString(),
    body('phoneNumber')
      .optional({ values: 'falsy' })
      .isNumeric()
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('variantCode').notEmpty().isString(),
    body('cityGroup').notEmpty().isString(),
    body('priceListNormal')
      .isArray({
        min: 1,
      })
      .withMessage('Pricelist minimal mempunyai satu baris'),

    body('priceListNormal.*.downPayment')
      .notEmpty()
      .isInt({
        gt: 0,
      })
      .withMessage('DP harus diatas Rp. 0')
      .toInt(),
    body('priceListNormal.*.tenor').notEmpty().isNumeric().toInt(),
    body('priceListNormal.*.installment').notEmpty().isNumeric().toFloat(),
    body('priceListNormal.*.installmentDiscount')
      .optional({ values: 'falsy' })
      .isNumeric()
      .toFloat(),
    body('priceListNormal.*.tenorDiscount').notEmpty().isNumeric().toInt(),
    requestValidator,
    (req, res, next) => {
      let messageErrors = '';
      for (const bodyElement of req.body.priceListNormal) {
        if (bodyElement.tenorDiscount > bodyElement.tenor) {
          messageErrors += `\nDiskon Tenor (${bodyElement.tenorDiscount}) harus kurang dari Tenor (${bodyElement.tenor})`;
        }
        if (bodyElement.installmentDiscount > bodyElement.installment) {
          messageErrors += `\nDiskon Angsuran (${bodyElement.installmentDiscount}) harus kurang dari Angsuran (${bodyElement.installment})`;
        }
      }

      if (messageErrors) {
        res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: messageErrors,
          })
        );
      } else {
        next();
      }
    },
  ],
  handler: async (req, res) => {
    let variant: VariantCatalogue | undefined;
    try {
      const getVariant = await catalogueService.getVariantByCityGroup({
        variantCode: req.body.variantCode,
        cityGroup: req.body.cityGroup,
      });
      variant = getVariant.data.data?.[0] || null;
    } catch (e) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Variant tidak ditemukan',
        })
      );
    }

    const now = moment();
    const priceList: IPriceListFile = {
      source: req.body.source,
      phoneNumber: req.body.phoneNumber,
      admin_name: req.body.admin || '',
      title: req.body.title,
      caption: req.body.subTitle,
      city_group: req.body.cityGroup,
      company: 'AMARTA',
      created_at: now.toISOString(),
      expired_at: req.body.expiredAt.toISOString(),
      pricelist_data: {
        otr: variant?.price || 0,
        normal: req.body.priceListNormal.map(value => {
          const dpPercentage = (value.downPayment / variant!.price) * 100;
          const dpCeil = Math.ceil(dpPercentage * 10) / 10;
          return {
            dp_nominal: value.downPayment,
            tenor: value.tenor,
            installment: value.installment,
            promo_tenor: value.tenorDiscount,
            promo_dp_nominal: null,
            promo_dp_percentage: null,
            promo_installment: value.installmentDiscount,
            dp_percentage: dpCeil,
          };
        }),
      },
      variant_code: variant?.variant_code || '',
    };

    const folder = now.format('YYMM');
    const fileName = `${folder}${generateRandomString(6)}`;
    const fullPath = `create_pricelist/${folder}/${fileName}.json`;
    const file = bucketTrimitraIdeal.file(fullPath);

    try {
      const jsonString = JSON.stringify(priceList);
      await file.save(jsonString, {
        public: true,
      });
    } catch (e) {
      return res.send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e?.toString(),
        })
      );
    }

    res.send(
      successResponse({
        data: fileName,
        type: 'CREATED',
      })
    );
  },
};

export default createPriceListFileHandler;
