import { BigQueryDatetime, BigQueryTimestamp } from '@google-cloud/bigquery';

export interface RawLeadsTable {
  uid: string;
  source: string | null;
  external_id: string | null;
  duplicated: boolean;
  phone_number: string | null;
  full_name: string | null;
  city: string | null;
  vehicle_model: string | null;
  vehicle_variant: string | null;
  organization: string | null;
  city_group: string | null;

  trimitra_city: {
    name: string;
    code: string;
  } | null;
  trimitra_province: {
    name: string;
    code: string;
  } | null;

  trimitra_vehicle: {
    model: {
      name: string;
    } | null;
    variant: {
      name: string;
      code: string;
    } | null;
    color: {
      name: string;
      code: string;
    } | null;
  } | null;

  whatsapp_delivered: boolean | null;
  whatsapp_delivered_at_timestamp: BigQueryTimestamp | null;
  data_leads_created_at_timestamp: BigQueryTimestamp | null;
  bq_created_at_timestamp: BigQueryTimestamp;
  bq_created_at_datetime: BigQueryDatetime;
  is_in_my_leads: boolean;
}
