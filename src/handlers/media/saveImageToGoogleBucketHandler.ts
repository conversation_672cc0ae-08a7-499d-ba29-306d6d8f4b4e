import { json, RequestHandler } from 'express';
import { body } from 'express-validator';
import requestChecker from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import { IFirestoreMessageEntity } from '../../types/firestore/messages/message_types';
import { qiscusServiceNew } from '../../services/QiscusServiceNew';
import { kataAiServices } from '../../services/KataAiServices';
import metaServices from '../../services/MetaServices';
import axios from 'axios';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import { File } from '@google-cloud/storage';
import { bucketTrimitraIdeal } from '../../services/cloudStorage';
import { v4 } from 'uuid';
import mime from 'mime-types';
import { firestore } from 'firebase-admin';

interface ReqBody {
  path: string;
}

const saveImageToGoogleBucketHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
} = {
  middlewares: [json(), body('path').notEmpty(), requestChecker],
  handler: async (req, res) => {
    const chatRef = myFirestore.doc(req.body.path);
    const roomRef = chatRef.parent.parent;

    const projectRef = roomRef?.parent.parent;

    const chatData = (await chatRef.get()).data()! as IFirestoreMessageEntity;
    const projectData = (await projectRef!.get()).data()!;

    let image: ArrayBuffer | null = null;

    let fileName = v4();

    if (chatData.message.type !== 'image' || !chatData.message.image) {
      return res.status(404).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Pesan tidak mengandung gambar.',
        })
      );
    }

    try {
      if (chatData.message.image.id) {
        if (projectData.provider === 'qiscus') {
          image = (await qiscusServiceNew.getMedia(chatData.message.image.id)).data;
        } else if (projectData.provider === 'kata.ai') {
          image = (await kataAiServices.showImage(chatData.message.image.id)).data;
        } else if (projectData.provider === 'meta') {
          image = (await metaServices.getFile(chatData.message.image.id, projectData.meta)).data;
        }
      } else if ('link' in chatData.message.image) {
        image = (await axios(chatData.message.image.link!, { responseType: 'arraybuffer' })).data;
      }

      if (image) {
        if (['qiscus', 'kata.ai', 'meta'].indexOf(projectData.provider) > -1) {
          let fileExtension = mime.extension(chatData.message.image.mime_type!);
          fileName =
            `${(chatData.origin.reference as firestore.DocumentReference).id}/` +
            fileName +
            '.' +
            fileExtension;
        }

        const buffer = Buffer.from(image);

        let file: File = bucketTrimitraIdeal.file(`chat-images/${fileName}`);
        await file.save(buffer, {
          public: true,
          contentType: chatData.message.image.mime_type!,
        });

        chatRef.update({
          'message.image.saved_link': file.publicUrl(),
        });
      }
    } catch (e: any) {
      console.log(e);
      return res.status(404).send(
        errorResponse({
          type: 'SERVER_ERROR',
        })
      );
    }

    res.send(
      successResponse({
        data: null,
      })
    );
  },
};

export default saveImageToGoogleBucketHandler;
