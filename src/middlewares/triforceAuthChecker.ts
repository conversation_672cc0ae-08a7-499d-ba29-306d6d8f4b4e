import { NextFunction, Request, Response } from 'express';
import axios from 'axios';
//
// const username = "trifoceClientApp";
// const password = "pDy<A2T.s(f3`6HF";

export default async function triForceAuthChecker(req: Request, res: Response, next: NextFunction) {
  let authorizationHeader = req.header('Authorization');
  let headerBuildVersion = req.header('app-build-code');

  try {
    const bearerToken = authorizationHeader?.replace('Bearer', '').trim();

    if (!bearerToken) throw new Error('Unauthenticated');
    await axios.get(
      'https://3krjkubmjk.execute-api.ap-southeast-3.amazonaws.com/v1/auth/validate',
      {
        headers: {
          Authorization: 'Bearer ' + bearerToken,
          'App-Code': 'triforce',
          'App-Build-Code': headerBuildVersion,
        },
      }
    );
    res.locals.token = bearerToken;
    next();
  } catch (e: any) {
    res.status(422).send({
      success: false,
      data: 'Token tidak valid',
    });
  }
}
