import { myFirestore } from '../../../src/services/firebaseAdmin';
import AdminModel from '../../../src/model/AdminModel';

const changeAllAdminToOwner = async () => {
  const adminCollection = myFirestore.collection('admins').withConverter(AdminModel.converter);
  const get = await adminCollection.where('active', '==', true).where('level', '==', 'admin').get();

  const admins: AdminModel[] = [];

  const batch = myFirestore.batch();
  get.forEach(result => {
    const data = result.data();
    admins.push(data);
  });

  for (const admin of admins) {
    batch.update(admin.doc_admin, {
      level: 'owner',
    });
  }

  await batch.commit();
};

changeAllAdminToOwner();
