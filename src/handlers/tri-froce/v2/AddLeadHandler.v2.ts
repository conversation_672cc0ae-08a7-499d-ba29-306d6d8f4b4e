import fetchAgent from '../../../services/agent/fetchAgent';
import { NextFunction, Request, Response } from 'express';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import { requestValidatorWithCb } from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import errorResponse from '../../../schema/errorResponse';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import LeadsModel from '../../../model/LeadsModel';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import aggregatesAgent from '../../../helpers/leads/aggregatesAgent';
import successResponse from '../../../schema/successResponse';
import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import { errorLogsAddLeads } from '../../../helpers/leads/errorLogs';
import afterAddLeads from '../../../helpers/leads/afterAddLeads';
import multer from 'multer';
import { leadsNotesCounter } from '../../../helpers/leads/leadsNotes';
import parseExportedWhatsappChat from '../../../helpers/parseExportedWhatsappChat';
import { bucketKataAi } from '../../../services/cloudStorage';
import { v4 } from 'uuid';
import path from 'path';
import moment from 'moment';
import leadsAlreadyAcquiredCheck from '../../../middlewares/leads/leadsAlreadyAcquiredCheck';
import { NewLeadsBigqueryTypes } from '../../../types/services/bigQuery/newLeads.bigquery.types';
import { bigQuery, newLeadsBigQueryTable } from '../../../services/bigQueryService';

const fileUpload = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

interface ReqBody {
  agentCode: string;
  title: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  provinceName: string;
  provinceCode: string;
  cityName: string;
  cityCode: string;
  vehicleUsage: 'individual' | 'shared' | 'corporate';
  paymentPlan: 'cash' | 'credit';
  downPaymentPlan: number;
  hasVehicleLoan: boolean;
  vehicleOptions: {
    brand: {
      name: string;
    };
    model: {
      name: string;
    };
    variant: {
      code: string;
      name: string;
    };
    color: {
      code: string;
      name: string;
    };
  }[];
  source: string;
  source2: string;
  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition';
  nextTotalVehicleOwnerShip: string;
  notes: string;

  idCard_number: string | null;
  driverLicense_number: string | null;
}

const addLeadHandlerV2 = {
  middlewares: [
    fileUpload.single('conversations'),
    (req: Request, res: Response, next: NextFunction) => {
      req.body = JSON.parse(req.body.data);
      next();
    },
    triForceAuthChecker,
    body('agentCode')
      .notEmpty()
      .trim()
      .withMessage('Kode Agen dibutuhkan')
      .custom((input, { req }) => {
        return new Promise(async (resolve, reject) => {
          try {
            const fetch = await fetchAgent(input);
            const validUserType = ['affiliate', 'internal'];
            if (validUserType.indexOf(fetch.user_type.toLowerCase()) < 0) {
              reject('Kode agen tidak terdaftar sebagai internal ataupun affiliate.');
            } else {
              resolve(fetch);
            }
          } catch (e) {
            reject('Kode Agen tidak terdaftar.');
          }
        })
          .then(value => {
            req.res.locals.agent = value;
          })
          .catch(reason => {
            throw new Error(reason);
          });
      }),
    body('title').notEmpty().withMessage('Title is required'),
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName')
      .optional()
      .customSanitizer(input => (!input ? '' : input)),
    body('phoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('email').optional({ values: 'falsy' }).isEmail().withMessage('Invalid email address'),
    body('provinceName').notEmpty().withMessage('Province name is required'),
    body('provinceCode').notEmpty().withMessage('Province code is required'),
    body('cityName').notEmpty().withMessage('City name is required'),
    body('cityCode').notEmpty().withMessage('City code is required'),
    body('vehicleUsage')
      .notEmpty()
      .withMessage('Vehicle usage is required')
      .isIn(['individual', 'shared', 'corporate'])
      .withMessage('Invalid vehicle usage'),
    body('paymentPlan')
      .notEmpty()
      .withMessage('Payment plan is required')
      .isIn(['cash', 'credit'])
      .withMessage('Invalid payment plan'),
    body('downPaymentPlan').optional({ values: 'falsy' }).isNumeric(),
    body('hasVehicleLoan')
      .optional()
      .default(false)
      .isBoolean()
      .withMessage('Invalid hasVehicleLoan format'),
    body('vehicleOptions')
      .isArray({ min: 1 })
      .withMessage('At least one vehicle option is required')
      .custom(value => {
        for (const option of value) {
          if (!option.brand?.name) {
            throw new Error('Brand name is required');
          }
          if (!option.model?.name) {
            throw new Error('Model name is required');
          }
          if (!option.variant?.code) {
            throw new Error('Variant code is required');
          }
          if (!option.variant?.name) {
            throw new Error('Variant name is required');
          }
          if (!option.color?.code) {
            throw new Error('Color code is required');
          }
          if (!option.color?.name) {
            throw new Error('Color name is required');
          }
        }
        return true;
      }),
    body('source2')
      .notEmpty()
      .withMessage('Source2 is required')
      .isIn(['amartahonda', 'amartachery', 'amartaneta', 'amartavinfast'])
      .withMessage(
        'Organization hanya boleh diisi amartahonda, amartachery, amartaneta, dan amartaneta '
      )
      .customSanitizer(input => input.replace(/ /g, '')),
    body('source').notEmpty().withMessage('Source is required'),
    body('purchasePlan')
      .notEmpty()
      .isIn(['firstVehicle', 'vehicleReplacement', 'vehicleAddition'])
      .withMessage('purchasePlan is required'),
    body('nextTotalVehicleOwnerShip').notEmpty(),
    body('notes')
      .notEmpty()
      .withMessage('Notes dibutuhkan')
      .isLength({ min: 30, max: 600 })
      .withMessage('Catatan Minimal 30 s.d 600 karakter.'),
    body('idCard_number')
      .optional()
      .customSanitizer(input => (!input ? null : input)),
    body('driverLicense_number')
      .optional()
      .customSanitizer(input => (!input ? null : input)),

    requestValidatorWithCb({
      notValidCb: errorLogsAddLeads,
    }),

    leadsAlreadyAcquiredCheck,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response) => {
    const now = moment();
    const org = req.body.source2;
    const collections = myFirestore.collection('leads');
    const docRef = collections.doc(`${org}-${req.body.phoneNumber}`);

    if (!req.file?.buffer) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
          messages: 'File percakapan diperlukan!',
        })
      );
    }

    const read = req.file.buffer.toString('utf-8');
    const parseConversations = parseExportedWhatsappChat(read);

    const minInboundMessageIfFirstMessageOutbound = 8;
    if (
      parseConversations.summary.firstMessageDirection === 'outbound' &&
      parseConversations.summary.inbound < minInboundMessageIfFirstMessageOutbound
    ) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
          messages: `Jika agen yang pertama kali mengirim chat maka pesan masuk dari customer minimal ${minInboundMessageIfFirstMessageOutbound} pesan`,
        })
      );
    }

    const minInboundMessageIfFirstMessageInbound = 3;
    if (
      parseConversations.summary.firstMessageDirection === 'inbound' &&
      parseConversations.summary.inbound < minInboundMessageIfFirstMessageInbound
    ) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
          messages: `Jika customer yang pertama kali mengirim chat maka pesan masuk dari customer minimal ${minInboundMessageIfFirstMessageInbound} pesan`,
        })
      );
    }

    const fileName = `wa-conversations_${v4()}_${req.body.phoneNumber}_${now.format('YYYY-MM-DD_HH:mm:ss')}`;
    const file = bucketKataAi.file(
      'leadsConversations/' +
        req.body.phoneNumber +
        '/' +
        fileName +
        path.extname(req.file.originalname)
    );

    let leadsDocData!: LeadsModel;

    const find = await docRef.withConverter(LeadsModel.converter).get();

    if (find.exists) {
      leadsDocData = find.data()!;
    }

    let agentName: string | null = null;
    let agentPhoneNumbers: string[] = [];
    try {
      const fetch = await fetchAgent(req.body.agentCode);
      agentName = fetch.name;
      agentPhoneNumbers = fetch.phones.map(value => value.phone);
    } catch (e: any) {
      agentName = 'NO_NAME';
    }

    const notes: ILeadsNotesDocument<Date> = {
      notes: req.body.notes,
      updatedAt: new Date(),
      updatedByUser: null,
      statusLevel: 0,
      agentCode: req.body.agentCode,
      event: 'init',
      phoneNumber: req.body.phoneNumber,
      organization: req.body.source2,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      agentName: agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: 0,
      },
      totalUpdateNotes: 1,
    };

    if (find.exists) {
      const notesCounter = await leadsNotesCounter(leadsDocData);
      notes.event = 'insertExistLeads';
      notes.notes = 'Free Leads sudah diakuisisi jadi hanya update saja';
      notes.totalUpdateNotes = notesCounter.totalNotes + 1;
      notes.reactivate = {
        currentTotal: notesCounter.totalReactivate,
      };
      leadsDocData.updateHistories.push(notes);
      leadsDocData.title = req.body.title;
      leadsDocData.domicile = {
        cityCode: req.body.cityCode,
        cityName: req.body.cityName,
        provinceCode: req.body.provinceCode,
        provinceName: req.body.provinceName,
      };
      leadsDocData.vehicleUsage = req.body.vehicleUsage;
      leadsDocData.paymentPlan = req.body.paymentPlan;
      leadsDocData.hasVehicleLoan = req.body.hasVehicleLoan;
      leadsDocData.vehicleOptions = req.body.vehicleOptions;
      leadsDocData.purchasePlan = req.body.purchasePlan;
      leadsDocData.nextTotalVehicleOwnerShip = req.body.nextTotalVehicleOwnerShip;
      leadsDocData.idCard_number = req.body.idCard_number;
      leadsDocData.driverLicense_number = req.body.driverLicense_number;
      leadsDocData.updatedAt = new Date();
    }

    if (!find.exists) {
      leadsDocData = new LeadsModel({
        agentCode: req.body.agentCode,
        agentName: agentName,
        phoneNumberAgent: agentPhoneNumbers,
        createdAt: new Date(),
        domicile: {
          cityCode: req.body.cityCode,
          cityName: req.body.cityName,
          provinceCode: req.body.provinceCode,
          provinceName: req.body.provinceName,
        },
        email: req.body.email || null,
        firstName: req.body.firstName,
        lastName: req.body.lastName,
        hasVehicleLoan: req.body.hasVehicleLoan,
        paymentPlan: req.body.paymentPlan,
        phoneNumber: req.body.phoneNumber,
        ref: docRef,
        source: req.body.source,
        organization: req.body.source2 as any,
        title: req.body.title,
        vehicleOptions: req.body.vehicleOptions,
        vehicleUsage: req.body.vehicleUsage,
        statusLevel: 0,

        purchasePlan: req.body.purchasePlan,
        nextTotalVehicleOwnerShip: req.body.nextTotalVehicleOwnerShip,

        isTracking: true,
        notes: req.body.notes,

        updateHistories: [
          {
            ...notes,
          },
        ],

        idCard_number: req.body.idCard_number ?? null,
        driverLicense_number: req.body.driverLicense_number ?? null,
        downPaymentPlan: req.body.downPaymentPlan,
      });
    }

    leadsDocData.firstMessageDirection = parseConversations.summary.firstMessageDirection || null;

    const leadsAgentConversationDocRef = myFirestore.collection('leads_agent_conversations').doc();

    let agentConversation = {
      firstMessageDirection: parseConversations.summary.firstMessageDirection,
      agentCode: req.body.agentCode,
      agentName: agentName,
      createdAt: moment().toDate(),
      bucketStorage: file.publicUrl(),
      docRef: leadsAgentConversationDocRef,
      startInboundChatAt: moment(
        parseConversations.summary.startInboundChatAt,
        'DD/MM/YYYY HH:mm'
      ).toDate(),
      endInboundChatAt: moment(
        parseConversations.summary.endInboundChatAt,
        'DD/MM/YYYY HH:mm'
      ).toDate(),
      startOutboundChatAt: moment(
        parseConversations.summary.startOutboundChatAt,
        'DD/MM/YYYY HH:mm'
      ).toDate(),
      endOutboundChatAt: moment(
        parseConversations.summary.endOutboundChatAt,
        'DD/MM/YYYY HH:mm'
      ).toDate(),
      startChatAt: moment(parseConversations.summary.startChatAt, 'DD/MM/YYYY HH:mm').toDate(),
      endChatAt: moment(parseConversations.summary.endChatAt, 'DD/MM/YYYY HH:mm').toDate(),
    };
    leadsDocData.agentWhatsappConversations.push(agentConversation);

    const leadsAgentConversationDocData = {
      ...agentConversation,
      phoneNumber: req.body.phoneNumber,
      firstName: req.body.firstName,
      lastName: req.body.lastName,
      leadsDocRef: leadsDocData.ref,
      organization: req.body.source2,
      conversations: parseConversations.messages.map(c => {
        return {
          datetime: moment(c.datetime, 'DD/MM/YYYY HH:mm').toDate(),
          name: c.name,
          type: c.type,
          message: c.message,
        };
      }),
    };

    try {
      const batch = myFirestore.batch();
      batch.set(leadsDocData.ref.withConverter(LeadsModel.converter), leadsDocData);
      batch.set(myFirestore.collection('leads_notes').doc(), notes);
      batch.set(leadsAgentConversationDocRef, leadsAgentConversationDocData);
      await file.save(req.file.buffer, {
        public: true,
      });

      await batch.commit();

      await aggregatesAgent(req.body.agentCode, org as any).then();
    } catch (e: any) {
      errorLogsAddLeads(req, e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }

    try {
      const row: NewLeadsBigqueryTypes = {
        event_source: 'triforce',
        agent_code: leadsDocData.agentCode,
        agent_name: leadsDocData.agentName,
        agent_phone_number: leadsDocData.phoneNumberAgent?.[0] || null,
        city_group: leadsDocData.area || null,
        city_code: leadsDocData.domicile?.cityCode || null,
        city_name: leadsDocData.domicile?.cityName || null,
        province_code: leadsDocData.domicile?.provinceCode || null,
        province_name: leadsDocData.domicile?.provinceName || null,
        create_date_time: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
        has_vehicle_loan: null,
        lead_conversation_count_inbound: parseConversations.summary.inbound,
        lead_conversation_count_outbound: parseConversations.summary.outbound,
        lead_conversation_file_url: file.publicUrl(),
        lead_conversation_resume: null,
        lead_email: leadsDocData.email,
        lead_name: leadsDocData.firstName + leadsDocData.lastName,
        lead_phone_number: leadsDocData.phoneNumber,
        notes: leadsDocData.notes,
        organization: leadsDocData.organization,
        payment_plan: leadsDocData.paymentPlan,
        source: leadsDocData.source,
        vehicle_usage: null,
        lead_conversation_start_time: bigQuery.datetime(
          moment(parseConversations.summary.startChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_end_time: bigQuery.datetime(
          moment(parseConversations.summary.endChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_inbound_start_time: bigQuery.datetime(
          moment(parseConversations.summary.startInboundChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_inbound_end_time: bigQuery.datetime(
          moment(parseConversations.summary.endInboundChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_outbound_start_time: bigQuery.datetime(
          moment(parseConversations.summary.startOutboundChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_outbound_end_time: bigQuery.datetime(
          moment(parseConversations.summary.endOutboundChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        type: 'newLeads',
      };

      await newLeadsBigQueryTable.insert(row);
    } catch (e) {
      console.log('FAILED_INSERT_NEW_LEADS_BIG_QUERY', JSON.stringify(e));
    }

    afterAddLeads(leadsDocData)
      .then()
      .catch(e => {
        console.log('ERROR_AFTER_ADD_LEADS', e);
      });

    res.send(
      successResponse({
        data: leadsDocData.toJsonResponse(),
        type: 'CREATED',
      })
    );
  },
};

export default addLeadHandlerV2;
