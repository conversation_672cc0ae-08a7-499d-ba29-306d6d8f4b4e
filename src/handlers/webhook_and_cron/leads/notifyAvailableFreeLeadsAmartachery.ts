import { RequestHand<PERSON> } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import moment from 'moment';
import successResponse from '../../../schema/successResponse';

import 'moment/locale/id';
import telegramServices from '../../../services/telegramServices';

const notifyAvailableFreeLeadsAmartachery: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [],
  handler: async (req, res) => {
    const now = moment();

    const freeLeadsCollection = myFirestore.collection('free_leads');
    const getFreeLeadsAmartachery = await freeLeadsCollection
      .where('organization', '==', 'amartachery')
      .where('isAcquired', '==', false)
      .orderBy('createdAt', 'desc')
      .withConverter(FreeLeadsModel.converter)
      .get();

    const itemSendToTelegram: {
      name: string;
      city: string;
      source: string;
      timeRelative: string;
    }[] = [];

    getFreeLeadsAmartachery.forEach(result => {
      const data = result.data();
      const createdAt = moment(data.createdAt);
      itemSendToTelegram.push({
        city: data.domicile?.cityName ?? '',
        name: data.firstName,
        source: data.source,
        timeRelative: createdAt.fromNow(true),
      });
    });

    let messageTelegram =
      `=== FREE LEADS AMARTA CHERY ===` +
      `\nTanggal: ${now.format('YYYY-MM-DD HH:mm')} total ${getFreeLeadsAmartachery.size} data` +
      `\n`;
    for (const item of itemSendToTelegram) {
      messageTelegram += `\n- ${item.name}, ${item.city}, ${item.source}, ${item.timeRelative}.`;
    }
    telegramServices.sendMessage('-1002118167928', messageTelegram).then().catch();

    res.send(
      successResponse({
        type: 'FETCHED',
        data: itemSendToTelegram,
      })
    );
  },
};

export default notifyAvailableFreeLeadsAmartachery;
