import axios from 'axios';
import * as querystring from 'querystring';

const base = axios.create({
  baseURL: 'https://yqoiukyvj8.execute-api.ap-southeast-1.amazonaws.com/v1',
  headers: {
    'X-Api-Key': 'JLNIhF54Qu122UMgCdQPJQZfK3YlQG09e7p1GsPg',
  },
});

const telegramServices = {
  sendMessage: async (chatId: string, message: string) => {
    try {
      const send = await base.post(
        '/ask-bot-to-send-chat',
        querystring.stringify({
          chat_id: chatId,
          message: message,
        })
      );
      return send.data;
    } catch (e) {
      throw e;
    }
  },
};

export default telegramServices;
