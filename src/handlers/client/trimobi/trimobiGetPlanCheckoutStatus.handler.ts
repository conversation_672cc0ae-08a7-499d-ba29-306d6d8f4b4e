import { query } from 'express-validator';
import amartavipService from '../../../services/amartavip/amartavip.services';
import { HandlerTypes } from '../../../types/handler.types';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import successResponse from '../../../schema/successResponse';
import authChecker from '../../../middlewares/authChecker';
import requestValidator from '../../../middlewares/requestValidator';
import { isAxiosError } from 'axios';
import errorResponse from '../../../schema/errorResponse';
import { PlanCheckoutStatusItem } from '../../../services/amartavip/amartavip.services.types';

interface ReqQuery {
  transaction_id: string;
  condition: string;
}

const trimobiGetPlanCheckoutStatusHandler: HandlerTypes<
  ReqQuery,
  ResponseSchema,
  any,
  ReqQuery,
  any
> = {
  middlewares: [
    authChecker,
    function (req, res, next) {
      amartavipService.setEmail(res.locals.account.email);
      next();
    },
    query('transaction_id')
      .optional({ values: 'falsy' })
      .isString()
      .withMessage('Transaction ID must be a string'),
    query('condition')
      .optional({ values: 'falsy' })
      .isString()
      .withMessage('Condition must be a string'),
    requestValidator,
  ],
  handler: async (req, res) => {
    let data: PlanCheckoutStatusItem[] = [];
    try {
      const response = await amartavipService.getPlanCheckoutStatus({
        condition: req.query.condition,
        transaction_id: req.query.transaction_id,
      });
      data = response.data;
    } catch (error) {
      console.log(error);
      let errorMessage = 'Terjadi kesalahan saat mengambil status checkout plan';
      if (isAxiosError(error)) {
        errorMessage = 'Error API get plan checkout status: ' + error.response?.data.message;
      }
      return res.send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: errorMessage,
        })
      );
    }

    res.send(
      successResponse({
        type: 'FETCHED',
        data: data,
      })
    );
  },
};

export default trimobiGetPlanCheckoutStatusHandler;
