import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import moment from 'moment';
import { IChatRoomDocument } from '../../../types/firestore/chat_room_document_types';
import { firestore } from 'firebase-admin';
import ClientModel from '../../../model/ClientModel';
import collect from 'collect.js';
import { RawLeadsTable } from '../../../types/services/bigQuery/rawLeadsTable.types';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import { v4 as uuidV4 } from 'uuid';
import { bigQuery, rawLeadsTable } from '../../../services/bigQueryService';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import catalogueService from '../../../services/catalogueService';
import organizations from '../../../configs/organization';
import dayjs from 'dayjs';

const addLeads = async () => {
  const twoDaysAgo = dayjs().subtract(48, 'hours');
  const oneDayAgo = dayjs().subtract(24, 'hours');

  const chatRoomCollections: {
    chatRooms: firestore.CollectionReference;
    oraganizationGroup: string;
  }[] = [
    {
      chatRooms: myFirestore.collection('/projects/WLdKug7hau0MbRzKcnqg/chat_rooms'),
      oraganizationGroup: 'amartamotor',
    },
    {
      chatRooms: myFirestore.collection('/projects/v8GGopG1v89nd46aajEX/chat_rooms'),
      oraganizationGroup: 'amartamotor',
    },
    {
      chatRooms: myFirestore.collection('/projects/kpsDdEcRtReOQrCYkzq2/chat_rooms'),
      oraganizationGroup: 'amartamobil',
    },
  ];

  const chatRooms: {
    ref: firestore.DocumentReference;
    data: IChatRoomDocument;
    organizationGroup: string;
  }[] = [];

  for (const chatRoomCollection of chatRoomCollections) {
    const get = await chatRoomCollection.chatRooms
      .where(
        'last_inbound',
        '<',
        chatRoomCollection.oraganizationGroup === 'amartamotor'
          ? twoDaysAgo.toDate()
          : oneDayAgo.toDate()
      )
      .where(
        'last_inbound',
        '>',
        chatRoomCollection.oraganizationGroup === 'amartamotor'
          ? twoDaysAgo.subtract(1, 'months').toDate()
          : oneDayAgo.subtract(1, 'months').toDate()
      )
      .orderBy('last_inbound', 'desc')
      .get();

    // console.log('get', get.size);

    get.forEach(r => {
      chatRooms.push({
        data: r.data() as IChatRoomDocument,
        ref: r.ref,
        organizationGroup: chatRoomCollection.oraganizationGroup,
      });
    });
  }

  const chatRoomsCollectChunk = collect(chatRooms).chunk(500);

  for (const chatRoomsCollectChunkElement of chatRoomsCollectChunk.all()) {
    for (const chatRoom of chatRoomsCollectChunkElement) {
      const clientRef = chatRoom.data.clients[0];
      const getDataClient = await clientRef.withConverter(ClientModel.converter).get();
      const dataClient = getDataClient.data();

      if (!dataClient) continue;
      if (dataClient?.order_histories?.length && dataClient.order_histories.length > 0) continue;
      if (dataClient.freeLeadsStatus?.submitted) continue;
      if (chatRoom.organizationGroup === 'amartamotor') {
        if (!dataClient?.profile.area?.value) continue;
        if (!dataClient.dream_vehicle?.model_name) continue;
      }

      let organization = 'amartahonda';
      if (chatRoom.organizationGroup === 'amartamobil') {
        organization = 'amartavinfast';
        if (chatRoom.data.doc_department) {
          switch (chatRoom.data.doc_department.id) {
            case '3B5DemNPCE36rt7qZbQj':
              organization = 'amartavinfast';
              break;
            case 'YObZr9uA1Zmb80wOHOmS':
              organization = 'amartaneta';
              break;
            case 'm77pP0WzLggbbtp6GTaU':
              organization = 'amartachery';
              break;
            default:
              break;
          }
        }
      }

      const freeLeadsCollection = myFirestore.collection('free_leads');
      const leadsCollection = myFirestore.collection('leads');
      const rawLeadsCollection = myFirestore.collection('raw_leads');
      const coldLeadsCollection = myFirestore.collection('cold_leads');

      const bigQueryRows: RawLeadsTable[] = [];
      const rawLeadsDocs: firestore.DocumentReference[] = [];

      const freeLeadsDocName = `${organization}-${dataClient.contacts.whatsapp}`;

      const acquiredLeads = await leadsCollection.doc(freeLeadsDocName).get();
      const findFreeLeads = await freeLeadsCollection
        .doc(freeLeadsDocName)
        .withConverter(FreeLeadsModel.converter)
        .get();

      if (acquiredLeads.exists) {
        continue;
      } else if (findFreeLeads.exists) {
        continue;
      }

      const findColdLeads = await coldLeadsCollection.doc(freeLeadsDocName).get();

      if (findColdLeads.exists) {
        continue;
      }

      // console.log("CAN_SEND_TO_FREE_LEADS", chatRoom.data.contacts[0])

      const now = moment();

      const organizationData = organizations.find(o => o.organization === organization);

      let city = {
        city_name: '',
        city_code: '',
        province_name: '',
        province_code: '',
      };

      if (dataClient.profile.area?.text) {
        const availableCities = await catalogueService.getAvailableCityFromCityGroup({
          cityGroup: dataClient.profile.area.text,
          companyCode: organizationData?.companyCode,
        });

        if (availableCities.data.data && availableCities.data.data?.length > 0) {
          city = availableCities.data.data?.[0];
        }
      }

      const batchAddFreLeads = myFirestore.batch();

      const rawLeadsDoc = rawLeadsCollection.doc();

      const rawLeads: any = {
        externalId: null,
        phoneNumber: dataClient.contacts.whatsapp,
        fullName: dataClient.profile.name,
        city: city.city_name,
        vehicleModel: dataClient.dream_vehicle?.model_name ?? null,
        vehicleVariant: dataClient.dream_vehicle?.variant_name ?? null,
        organization: organizationData?.organization ?? '',

        externalData: null,
        source: 'ideal',

        inFreeLeads: findFreeLeads.exists,
        shouldPushToFreeLeadsAt: now.toDate(),
        freeLeadsPath: findFreeLeads.exists ? findFreeLeads.ref : null,
        freeLeadsCreatedAt: findFreeLeads.exists ? findFreeLeads.get('createdAt') : null,

        shouldPushToBigQueryAt: now.toDate(),
        donePushToBigQuery: false,
        donePushToBigQueryAt: null,

        createdAt: now.toDate(),

        duplicated: false,
        mappedData: {
          domicile: city.city_code
            ? {
                provinceName: city.province_name,
                provinceCode: city.province_code,
                cityCode: city.city_code,
                cityName: city.city_name,
              }
            : null,
          vehicle: {
            brand: {
              name: organizationData?.brand ?? '',
            },
            model: {
              name: dataClient.dream_vehicle?.model_name ?? '',
            },
            variant: {
              name: dataClient.dream_vehicle?.variant_name ?? '',
              code: dataClient.dream_vehicle?.variant_code ?? '',
            },
            color: {
              name: dataClient.dream_vehicle?.color_name ?? '',
              code: dataClient.dream_vehicle?.color_code ?? '',
            },
          },
          area: dataClient.profile.area?.text || null,
          paymentPlan: null,
        },
        whatsapp: null,
        alreadyInMyLeads: acquiredLeads.exists,
      };

      batchAddFreLeads.create(rawLeadsDoc, rawLeads);

      rawLeadsDocs.push(rawLeadsDoc);

      const freeLeadsDocRef: firestore.DocumentReference =
        freeLeadsCollection.doc(freeLeadsDocName);

      let price = 20000;

      if (chatRoom.organizationGroup === 'amartamobil') {
        price = 50000;
      }

      const freeLeads = new FreeLeadsModel({
        rawLeadsRef: rawLeadsDoc,
        area: dataClient.profile.area?.text || null,
        externalId: null,
        title: null,
        firstName: dataClient.profile.name,
        lastName: '',
        phoneNumber: dataClient.contacts.whatsapp,
        paymentPlan: null,
        hasVehicleLoan: false,
        createdAt: now.toDate(),

        domicile: city.city_code
          ? {
              provinceName: city.province_name,
              provinceCode: city.province_code,
              cityCode: city.city_code,
              cityName: city.city_name,
            }
          : null,

        driverLicense_number: null,
        idCard_number: null,
        email: null,
        nextTotalVehicleOwnerShip: null,
        organization: organizationData?.organization ?? '',
        purchasePlan: null,
        source: 'ideal',
        vehicleOptions: [
          {
            brand: {
              name: organizationData?.brand ?? '',
            },
            model: {
              name: dataClient.dream_vehicle?.model_name ?? '',
            },
            variant: {
              name: dataClient.dream_vehicle?.variant_name ?? '',
              code: dataClient.dream_vehicle?.variant_code ?? '',
            },
            color: {
              name: dataClient.dream_vehicle?.color_name ?? '',
              code: dataClient.dream_vehicle?.color_code ?? '',
            },
          },
        ],
        vehicleUsage: null,
        ref: freeLeadsDocRef,

        isAcquired: false,
        acquiredAt: null,
        acquiredAgentCode: null,
        acquiredAgentName: null,
        price,
        isCustomPrice: false,
        notes: 'Auto Add Free Leads dikarenakan tidak ada aktifitas chat ideal',
        disableWhatsapp: true,
        ideal: {
          chat_room_ref: chatRoom.ref,
        },
      });

      batchAddFreLeads.set(freeLeads.ref.withConverter(FreeLeadsModel.converter), freeLeads);

      const uuid = uuidV4();
      const bigQueryRow: RawLeadsTable = {
        city: city.city_name || null,
        city_group: dataClient.profile.area?.text || null,
        duplicated: false,
        external_id: freeLeads.externalId,
        full_name: `${freeLeads.firstName} ${freeLeads.lastName}`,
        organization: freeLeads.organization,
        phone_number: freeLeads.phoneNumber,
        source: freeLeads.source,

        trimitra_city: freeLeads.domicile?.cityCode
          ? {
              code: freeLeads.domicile?.cityCode,
              name: freeLeads.domicile?.cityName,
            }
          : null,
        trimitra_province: freeLeads.domicile?.provinceCode
          ? {
              code: freeLeads.domicile?.provinceCode,
              name: freeLeads.domicile?.provinceName,
            }
          : null,

        trimitra_vehicle: {
          variant: {
            code: freeLeads.vehicleOptions?.[0]?.variant.code,
            name: freeLeads.vehicleOptions?.[0]?.variant.name,
          },
          model: {
            name: freeLeads.vehicleOptions?.[0]?.model.name,
          },
          color: {
            code: freeLeads.vehicleOptions?.[0]?.color?.code,
            name: freeLeads.vehicleOptions?.[0]?.color?.name,
          },
        },

        uid: uuid,
        vehicle_model: freeLeads.vehicleOptions?.[0]?.model.name,
        vehicle_variant: freeLeads.vehicleOptions?.[0]?.variant.name,
        whatsapp_delivered: null,
        whatsapp_delivered_at_timestamp: null,
        data_leads_created_at_timestamp: bigQuery.timestamp(now.toDate()),
        bq_created_at_datetime: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
        bq_created_at_timestamp: bigQuery.timestamp(now.toDate()),
        is_in_my_leads: acquiredLeads.exists,
      };

      bigQueryRows.push(bigQueryRow);

      dataClient.freeLeadsStatus = {
        submitted: true,
        createdAt: firestore.Timestamp.fromDate(now.toDate()),
        organization: organizationData?.organization ?? '',
      };

      batchAddFreLeads.set(clientRef.withConverter(ClientModel.converter), dataClient);
      batchAddFreLeads.update(chatRoom.ref, {
        in_free_leads: true,
      });

      try {
        await batchAddFreLeads.commit();
        // console.log('SUCCESS_AUTO_ADD_FREE_LEADS', freeLeads);
      } catch {
        console.error('ERROR_AUTO_ADD_FREE_LEADS', dataClient.profile);
        continue;
      }

      try {
        const batchUpdateStatusBigQuery = myFirestore.batch();
        await rawLeadsTable.insert(bigQueryRows);
        for (const doc of rawLeadsDocs) {
          batchUpdateStatusBigQuery.update(doc, {
            donePushToBigQuery: true,
            donePushToBigQueryAt: now.toDate(),
          });
        }
        await batchUpdateStatusBigQuery.commit();
      } catch {
        console.error('ERROR_AUTO_ADD_FREE_LEADS', dataClient.profile);
      }
    }
  }
};

const autoAddFreeLeadsCron: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [],
  handler: (req, res) => {
    addLeads()
      .then(() => {
        res.send(
          successResponse({
            data: null,
          })
        );
      })
      .catch(reason => {
        console.log(reason);
        res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: reason,
          })
        );
      });
  },
};

export default autoAddFreeLeadsCron;
