import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import moment from 'moment/moment';
import aggregatesAgent from './aggregatesAgent';
import { leadsNotesCounter } from './leadsNotes';
import { firestore } from 'firebase-admin';

async function checkAgentUpdatingLeadsNotes() {
  const momentObjOffset = moment().subtract(1, 'day').startOf('date');
  const momentObjNow = moment().startOf('date');
  // console.log("CRON_AGENT_UPDATE_LEADS_NOTES", momentObjOffset.format("YYYY-MM-DD HH:mm:ss"))
  const collection = myFirestore.collection('leads_agent_counter');

  const agentCodes: string[] = [];

  const find = await collection
    .where('organization', 'in', ['amartachery', 'amartahonda', 'amartaneta', 'amartavinfast'])
    .get();

  find.forEach(result => {
    const data = result.data();
    if (agentCodes.indexOf(data.agentId) === -1) {
      agentCodes.push(data.agentId);
    }
  });

  // console.log("TOTAL_AGENT", agentCodes.length)

  const leadsCollection = myFirestore.collection('leads');

  for (const agentCode of agentCodes) {
    // console.log("AGENT_CODE", agentCode)
    const find = await leadsCollection
      .where('agentCode', '==', agentCode)
      .where('organization', 'in', ['amartachery', 'amartahonda', 'amartaneta', 'amartavinfast'])
      .where('isTracking', '==', true)
      .where('createdAt', '<', firestore.Timestamp.fromDate(momentObjNow.toDate()))
      .orderBy('createdAt', 'desc')
      .withConverter(LeadsModel.converter)
      .limit(200)
      .get();

    const leadsList: LeadsModel[] = [];

    find.forEach(result => {
      const data = result.data();
      leadsList.push(data);
    });

    console.log('TOTAL_LEADS', leadsList.length);

    const batch = myFirestore.batch();

    for (const leads of leadsList) {
      try {
        const notesDocuments: ILeadsNotesDocument[] = [];

        leads.updateHistories.forEach(value => {
          notesDocuments.push(value as any);
        });

        const findNotes = notesDocuments.find(value => {
          const momentObj = moment(value.updatedAt);
          return momentObj.isSame(momentObjOffset, 'day');
        });

        let shouldDisable = false;
        let _notes = '';

        if (!findNotes) {
          shouldDisable = true;
          _notes = 'Notes pada leads terlihat tidak di update pada hari ini';
        }

        if (shouldDisable) {
          // console.log("UNTRACK", agentCode, leads.createdAt, leads.phoneNumber)
          const notesCounter = await leadsNotesCounter(leads);
          let notes: ILeadsNotesDocument<Date> = {
            agentCode: leads.agentCode,
            event: 'untrack',
            notes: 'Leads di Untrack oleh sistem: ' + _notes,
            organization: leads.organization,
            phoneNumber: leads.phoneNumber,
            statusLevel: leads.statusLevel,
            updatedAt: new Date(),
            updatedByUser: null,
            firstName: leads.firstName,
            lastName: leads.lastName,
            agentName: leads.agentName,
            moveToCold: false,
            reactivate: {
              currentTotal: notesCounter.totalReactivate,
            },
            totalUpdateNotes: notesCounter.totalNotes + 1,
          };

          leads.notes = notes.notes;
          leads.isTracking = false;
          leads.needToMoveToColdAt = moment().add(14, 'days').toDate();
          leads.updatedAt = new Date();
          leads.updateHistories.push({
            ...notes,
          });

          batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
          batch.set(myFirestore.collection('leads_notes').doc(), notes);
        } else {
          // console.log("KEEP_TRACK", agentCode, leads.createdAt, leads.phoneNumber)
        }
      } catch (e) {
        console.log('ERROR_LOOP_UNTRACK_LEADS', leads.ref.path, e?.toString());
      }
    }

    try {
      await batch.commit();
      await aggregatesAgent(agentCode, 'amartachery').then();
      await aggregatesAgent(agentCode, 'amartahonda').then();
      await aggregatesAgent(agentCode, 'amartaneta').then();
      await aggregatesAgent(agentCode, 'amartavinfast').then();
    } catch (e) {
      console.log('ERROR_COMMIT_UNTRACK_LEADS', agentCode, e);
      throw e;
    }
  }
}

export default checkAgentUpdatingLeadsNotes;
