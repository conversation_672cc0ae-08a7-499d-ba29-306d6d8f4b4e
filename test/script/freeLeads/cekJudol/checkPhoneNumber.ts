import fs from 'fs';
import csv from 'csv-parser';
import { myFirestore } from '../../../../src/services/firebaseAdmin';
import FreeLeadsModel from '../../../../src/model/FreeLeadsModel';

const getPhoneNumbers = () => {
  const path = './logs.csv';

  const phoneNumbers: string[] = [];

  fs.createReadStream(path)
    .pipe(csv())
    .on('data', data => phoneNumbers.push(data[' Phone'].trim()))
    .on('end', () => checkPhoneNumber(phoneNumbers));
};

const checkPhoneNumber = async (phoneNumbers: string[]) => {
  const clientCollection = myFirestore.collection('clients');
  for (const phoneNumber of phoneNumbers) {
    const find = clientCollection.where('contacts.whatsapp', '==', phoneNumber);
  }
};

getPhoneNumbers();
