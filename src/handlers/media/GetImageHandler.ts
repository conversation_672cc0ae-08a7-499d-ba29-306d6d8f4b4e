import { Request, Response } from 'express';
import { query } from 'express-validator';
import { myFirestore } from '../../services/firebaseAdmin';
import requestChecker from '../../middlewares/requestValidator';
import { IFirestoreMessageEntity } from '../../types/firestore/messages/message_types';
import axios from 'axios';
import { kataAiServices } from '../../services/KataAiServices';
import { qiscusServiceNew } from '../../services/QiscusServiceNew';
import metaServices from '../../services/MetaServices';
import errorResponse from '../../schema/errorResponse';
import mime from 'mime-types';

export default class GetImageHandler {
  public static middlewares = [query('path').notEmpty(), requestChecker];

  public static async handler(req: Request<any, any, any, { path: string }>, res: Response) {
    const chatRef = myFirestore.doc(req.query.path);
    const roomRef = chatRef.parent.parent;
    const projectRef = roomRef?.parent.parent;
    try {
      if (!projectRef) {
        return res.status(404).send(
          errorResponse({
            type: 'ENTITY_NOT_FOUND',
            messages: 'Project not found',
          })
        );
      }

      const [chatSnapshot, projectSnapshot] = await Promise.all([chatRef.get(), projectRef.get()]);

      if (!chatSnapshot.exists || !projectSnapshot.exists) {
        return res.status(404).send(
          errorResponse({
            type: 'ENTITY_NOT_FOUND',
            messages: 'Chat or project data not found',
          })
        );
      }

      const chatData = chatSnapshot.data() as IFirestoreMessageEntity;
      const projectData = projectSnapshot.data()!;

      if (!chatData.message.image) {
        return res.status(400).send(
          errorResponse({
            type: 'INVALID_REQUEST',
            messages: 'Image data not found',
          })
        );
      }

      let response: any;
      let contentType = 'image/jpeg'; // Default fallback

      if ('id' in chatData.message.image && chatData.message.image.id) {
        switch (projectData.provider) {
          case 'qiscus':
            response = await qiscusServiceNew.getMedia(chatData.message.image.id);
            contentType = response.headers['content-type'] || contentType;
            break;
          case 'kata.ai':
            response = await kataAiServices.showImage(chatData.message.image.id);
            contentType = response.headers['content-type'] || contentType;
            break;
          case 'meta':
            response = await metaServices.getFile(chatData.message.image.id, projectData.meta);
            contentType = response.headers['content-type'] || contentType;
            break;
          default:
            return res.status(400).send(
              errorResponse({
                type: 'INVALID_REQUEST',
                messages: 'Unsupported provider',
              })
            );
        }
      } else if ('link' in chatData.message.image && chatData.message.image.link) {
        response = await axios(chatData.message.image.link, {
          responseType: 'arraybuffer',
        });
        contentType = response.headers['content-type'] || contentType;
      } else {
        return res.status(400).send(
          errorResponse({
            type: 'INVALID_REQUEST',
            messages: 'Invalid image data',
          })
        );
      }

      const fileName = `image_${Date.now()}.${mime.extension(contentType) || 'jpg'}`;

      res.setHeader('Content-Disposition', `inline; filename="${fileName}"`);
      res.contentType(contentType);
      res.send(response.data);
    } catch (e: any) {
      if (axios.isAxiosError(e)) {
        console.error('Error in GetImageHandler:', e.response?.data, chatRef.id);
      } else {
        console.error('Error in GetImageHandler:', e);
      }
      res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Image not found',
        })
      );
    }
  }
}
