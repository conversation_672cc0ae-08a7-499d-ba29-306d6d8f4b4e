import axios, { AxiosError } from 'axios';
import {
  IPayloadQiscusPostMessage,
  IQiscusSendMessageParams,
} from '../types/services/qiscus/qiscuss_service_new_types';
import {
  IMetaSendMessageParamsV2,
  IResponseSendMessage,
} from '../types/services/meta/metaApi.types';
import { IMetaProjectConfig } from '../types/firestore/project.types';
import { GetTemplateResponseMeta } from '../types/services/meta/GetTemplateResponse.meta';

class MetaServices {
  private graphApi = axios.create({
    baseURL: 'https://graph.facebook.com/v20.0',
  });

  public async sendMessageNative(
    params: IPayloadQiscusPostMessage,
    metaProjectConfig: IMetaProjectConfig
  ) {
    const body = {
      ...params,
      messaging_product: 'whatsapp',
    };
    if (body.text) {
      body.text.body = body.text.body.replace(/(<br>|<\/br>|<br \/>)/gim, '\n');
    }

    console.log(body);

    try {
      return await this.graphApi.post<IResponseSendMessage>(
        `${metaProjectConfig.phoneNumberId}/messages`,
        body,
        {
          headers: {
            Authorization: `Bearer ${metaProjectConfig.bearer}`,
          },
        }
      );
    } catch (e: any) {
      throw e as AxiosError;
    }
  }

  public async getTemplate(params: {
    meta: {
      whatsappBusinessAccountId: string;
      bearerToken: string;
    };
    query?: {
      name?: string;
    };
  }) {
    return this.graphApi.get<GetTemplateResponseMeta>(
      `/${params.meta.whatsappBusinessAccountId}/message_templates`,
      {
        headers: {
          Authorization: `Bearer ${params.meta.bearerToken}`,
        },
        params: params.query,
      }
    );
  }

  public async sendMessage(
    params: IQiscusSendMessageParams,
    metaProjectConfig: IMetaProjectConfig
  ) {
    const payloadSendToMeta: IMetaSendMessageParamsV2 = {
      messaging_product: 'whatsapp',
      recipient_type: 'individual',
      to: params.target,
      type: 'text',
    };

    if (params.imageUrl) {
      payloadSendToMeta.type = 'image';
      payloadSendToMeta.image = {
        link: params.imageUrl,
        caption: params.text ?? '',
      };
    } else if (params.documentUrl) {
      payloadSendToMeta.type = 'document';
      payloadSendToMeta.document = {
        link: params.documentUrl,
        caption: params.text ?? '',
        filename: params.documentName,
      };
    } else {
      payloadSendToMeta.text = {
        body: params.text ?? '',
        preview_url: false,
      };
    }

    return await this.graphApi.post<IResponseSendMessage>(
      `${metaProjectConfig.phoneNumberId}/messages`,
      payloadSendToMeta,
      {
        headers: {
          Authorization: `Bearer ${metaProjectConfig.bearer}`,
        },
      }
    );
  }

  public async sendMessageV2(
    params: IMetaSendMessageParamsV2,
    metaProjectConfig: IMetaProjectConfig
  ) {
    return await this.graphApi.post<IResponseSendMessage>(
      `${metaProjectConfig.phoneNumberId}/messages`,
      params,
      {
        headers: {
          Authorization: `Bearer ${metaProjectConfig.bearer}`,
        },
      }
    );
  }

  public async downloadFileFromUrl(mediaUrl: string, metaProjectConfig: IMetaProjectConfig) {
    return await axios.get(mediaUrl, {
      responseType: 'arraybuffer',
      headers: {
        Authorization: `Bearer ${metaProjectConfig.bearer}`,
      },
    });
  }

  public async getFile(mediaId: string, metaProjectConfig: IMetaProjectConfig) {
    const getUrl = await this.getFileUrlById(mediaId, metaProjectConfig);
    return await this.downloadFileFromUrl(getUrl.data.url, metaProjectConfig);
  }

  public async uploadMedia(
    params: {
      file: File;
      type: string; // MIME type
      messagingProduct: 'whatsapp';
    },
    metaProjectConfig: IMetaProjectConfig
  ) {
    console.log('params', params);
    const formData = new FormData();
    formData.append('file', params.file);
    formData.append('type', params.type);
    formData.append('messaging_product', params.messagingProduct);

    return await this.graphApi.post<{ id: string }>(
      `${metaProjectConfig.phoneNumberId}/media`,
      formData,
      {
        headers: {
          Authorization: `Bearer ${metaProjectConfig.bearer}`,
        },
      }
    );
  }

  public async getFileUrlById(mediaId: string, metaProjectConfig: IMetaProjectConfig) {
    return await this.graphApi.get(`${mediaId}`, {
      headers: {
        Authorization: `Bearer ${metaProjectConfig.bearer}`,
      },
    });
  }
}

const metaServices = new MetaServices();

export default metaServices;
