import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import phoneNumberCountryCodeSanitizer from './phoneNumberCountryCodeSanitizer';
import stringSimilarity from 'string-similarity-js';
import cleanAndTokenizeString from './cleanAndTokenizeString';
dayjs.extend(customParseFormat);

const specialSentences = [
  {
    name: 'greeting',
    text: `⭐⭐Ingat honda, ingat Amarta Honda⭐⭐
                Beli Honda Online di amartahonda.com 
                Follow Media sosial kami
                •⁠  ⁠Tiktok : amartahonda
                •⁠  ⁠Facebook : amartahondacom
                •⁠  ⁠Instagram : amartahonda

                💰Dapatkan komisi hingga Rp.500ribu dengan mengirimkan data prospek motor kepada saya💰

                ❤️️Salam Satu Hati❤️️`,
  },
];

interface Message {
  datetime: string;
  name: string;
  message: string;
  type: 'inbound' | 'outbound';
}

export interface ParsedChat {
  messages: Message[];
  formattedRaw: string[];
  summary: {
    firstMessageDirection: 'outbound' | 'inbound';

    outboundName: string | null;
    inboundPhoneNumber: string | null;

    inbound: number;
    outbound: number;

    startInboundChatAt: string | null;
    endInboundChatAt: string | null;

    startOutboundChatAt: string | null;
    endOutboundChatAt: string | null;

    startChatAt: string | null;
    endChatAt: string | null;

    specialOutboundSentences: string[];
  };
}

export interface ParseExportedWhatsappChatOptions {
  minDate?: Date;
}

interface DatePattern {
  regex: RegExp;
  format: string;
  id: string;
}

/**
 * Pattern Tanggal
 * 11/08/2024 10.58 - DD/MM/YYYY HH.mm
 * 08/03/24 15.28 - DD/MM/YY HH.mm
 * 03/04/2021, 09:31 - DD/MM/YYYY, HH:mm
 *
 * 3/1/24, 7:11 PM - D/M/YY, h:mm A
 * 8/19/24, 9:53 AM - M/D/YY, h:mm A
 *
 * [19/06/24 08.38.33] - [DD/MM/YY HH.mm.ss]
 * [16/02/24 9.41.14 PM] - [DD/MM/YY h.mm.ss A]
 * [22/05/24, 15.38.53] - [DD/MM/YY, HH.mm.ss]
 */
const datePatterns: DatePattern[] = [
  // Pattern: DD/MM/YYYY HH.mm. Ex: 11/08/2024 10.58
  { regex: /^\d{2}\/\d{2}\/\d{4} \d{2}\.\d{2}/, format: 'DD/MM/YYYY HH.mm', id: '1' },

  // Pattern: DD/MM/YY HH.mm. Ex: 08/03/24 15.28
  { regex: /^\d{2}\/\d{2}\/\d{2} \d{2}\.\d{2}/, format: 'DD/MM/YY HH.mm', id: '2' },

  // Pattern: DD/MM/YYYY, HH:mm. Ex: 03/04/2021, 09:31
  { regex: /^\d{2}\/\d{2}\/\d{4}, \d{2}:\d{2}/, format: 'DD/MM/YYYY, HH:mm', id: '3' },

  // Pattern: D/M/YY, h:mm A. Ex: 3/1/24, 7:11 PM
  { regex: /^\d{1,2}\/\d{1,2}\/\d{2}, \d{1,2}:\d{2} (AM|PM)/, format: 'D/M/YY, h:mm A', id: '4' },

  // Pattern: M/D/YY, h:mm A. Ex: 8/19/24, 9:53 AM
  // { regex: /^\d{1,2}\/\d{1,2}\/\d{2}, \d{1,2}:\d{2} (AM|PM)/, format: 'M/D/YY, h:mm A', id: '5' },

  // Pattern: [DD/MM/YY HH.mm.ss]. Ex: [19/06/24 08.38.33]
  { regex: /^\[\d{2}\/\d{2}\/\d{2} \d{2}\.\d{2}\.\d{2}\]/, format: 'DD/MM/YY HH.mm.ss', id: '6' },

  // Pattern: [DD/MM/YY h.mm.ss A]. Ex: [16/02/24 9.41.14 PM]
  {
    regex: /^\[\d{2}\/\d{2}\/\d{2} \d{1,2}\.\d{2}\.\d{2} (AM|PM)\]/,
    format: 'DD/MM/YY h.mm.ss A',
    id: '7',
  },

  // Pattern: [DD/MM/YY, HH.mm.ss]. Ex: [22/05/24, 15.38.53]
  { regex: /^\[\d{2}\/\d{2}\/\d{2}, \d{2}\.\d{2}\.\d{2}\]/, format: 'DD/MM/YY, HH.mm.ss', id: '8' },

  // Pattern: [DD/MM/YY, HH:mm:ss]. Ex: [22/05/24, 15:38:53]
  { regex: /^\[\d{2}\/\d{2}\/\d{2}, \d{2}\:\d{2}\:\d{2}\]/, format: 'DD/MM/YY, HH:mm:ss', id: '9' },
];

const ignoreSentences = [
  'Messages and calls are end-to-end encrypted. No one outside of this chat, not even WhatsApp, can read or listen to them.',
  'Pesan dan panggilan terenkripsi secara end-to-end. Tidak seorang pun di luar chat ini, termasuk WhatsApp, yang dapat membaca atau mendengarkannya.',
];

const cleanUnicode = [
  { regex: /\u{202F}/gu, replace: ' ' },
  { regex: /\u{A0}/gu, replace: ' ' },
  { regex: /\u{202A}/gu, replace: '' },
  { regex: /\u{202C}/gu, replace: '' },
  { regex: /\u{200E}/gu, replace: '' },
];

function deleteEmptyRow(chatArray: string[]) {
  chatArray = chatArray.filter(line => !!line.trim());
  return chatArray;
}

function cleanWhatsAppChat(rawData: string): string[] {
  // Jadikan array untuk tiap baris
  let chatArray = rawData.split('\n');

  // Hilangkan baris yang kosong
  chatArray = deleteEmptyRow(chatArray);

  // Hapus unicode atau spesial karakter
  for (let i = 0; i < chatArray.length; i++) {
    let text = chatArray[i];

    // remove empty words
    text = text.replace(/\r/g, '');
    text = text.trim();

    //replace unicode
    for (const u of cleanUnicode) {
      text = text.replace(u.regex, u.replace);
    }
    chatArray[i] = text;
  }

  // Mengatasi chat yang mengandung baris baru
  chatArray = chatArray.reverse();
  for (let i = 0; i < chatArray.length; i++) {
    let text = chatArray[i];
    if (!text) break;
    let patternTrue: boolean[] = [];
    for (const datePattern of datePatterns) {
      patternTrue.push(datePattern.regex.test(text));
    }
    if (patternTrue.filter(p => p).length === 0) {
      if (i + 1 < chatArray.length) {
        chatArray[i + 1] = chatArray[i + 1] + `\n${text}`;
        chatArray[i] = '';
      }
    }
  }

  chatArray = chatArray.reverse();
  chatArray = deleteEmptyRow(chatArray);

  // Normalisasi format tanggal angar mempnunyai format tanggal yang sama
  for (let i = 0; i < chatArray.length; i++) {
    let text = chatArray[i];
    for (const { regex, format } of datePatterns) {
      const match = text.match(regex);
      if (match) {
        const matchedString = match[0];

        let dateString = matchedString;
        dateString = dateString.replace(/[\[\]]/g, '');

        const parsedDate = dayjs(dateString, format, true);
        if (!parsedDate.isValid()) {
          continue;
        }
        const formattedDate = parsedDate.format('DD/MM/YYYY HH:mm');
        text = text.replace(matchedString, formattedDate);
        break;
      }
    }
    chatArray[i] = text;
  }

  // Menghapus baris chat yang berisi kalimat tidak dibutuhkan
  for (let i = 0; i < chatArray.length; i++) {
    let text = chatArray[i];
    for (const string of ignoreSentences) {
      const match = text.match(string);
      if (match) {
        chatArray[i] = '';
      }
    }
  }
  chatArray = deleteEmptyRow(chatArray);

  // Tambakan tanda `-` sesudah tanggal jika belum ada
  for (let i = 0; i < chatArray.length; i++) {
    let text = chatArray[i];
    text = text.replace(/(\d{2}\/\d{2}\/\d{4} \d{2}:\d{2})(?! -)/, '$1 -');
    chatArray[i] = text;
  }

  return chatArray;
}

function parseWhatsAppChat(
  chatArray: string[],
  options?: ParseExportedWhatsappChatOptions
): ParsedChat {
  let messages: Message[] = [];

  const regex = /^(\d{2}\/\d{2}\/\d{4} \d{2}:\d{2}) - ([^:]+): ([\s\S]+)$/;

  chatArray.forEach(line => {
    const match = line.match(regex);
    if (match) {
      const [, datetime, name, message] = match;
      const type = name.match(/^\+\d+/) ? 'inbound' : 'outbound';
      messages.push({ datetime, name, message, type });
    }
  });

  const _inbound = messages.filter(value => value.type === 'inbound');
  const _outbound = messages.filter(value => value.type === 'outbound');

  const outboundName = _outbound?.[0]?.name || '';
  const inboundPhoneNumber = _inbound?.[0]?.name
    ? phoneNumberCountryCodeSanitizer(_inbound[0].name, '62', '62')
    : '';

  if (options?.minDate) {
    messages = messages.filter(m => {
      const date = dayjs(m.datetime, 'DD/MM/YYYY HH:mm');
      const minDate = dayjs(options.minDate);
      return date.isAfter(minDate);
    });
  }

  const inbound = messages.filter(value => value.type === 'inbound');
  const outbound = messages.filter(value => value.type === 'outbound');

  const _specialSentences: string[] = [];

  for (const message of outbound) {
    const textMessage = message.message;
    for (const sentence of specialSentences) {
      const result = stringSimilarity(
        cleanAndTokenizeString(sentence.text).join(' '),
        cleanAndTokenizeString(textMessage).join(' ')
      );

      if (result > 0.9) {
        _specialSentences.push(sentence.name);
      }
    }
  }

  return {
    messages: messages,
    summary: {
      firstMessageDirection: messages[0]?.type || 'outbound',
      outboundName: outboundName,
      inboundPhoneNumber: inboundPhoneNumber,
      inbound: inbound.length,
      outbound: outbound.length,
      startInboundChatAt: inbound?.[0]?.datetime || null,
      endInboundChatAt: inbound?.[inbound.length - 1]?.datetime || null,
      startOutboundChatAt: outbound?.[0]?.datetime || null,
      endOutboundChatAt: outbound?.[outbound.length - 1]?.datetime || null,
      startChatAt: messages?.[0]?.datetime || null,
      endChatAt: messages?.[messages.length - 1]?.datetime || null,
      specialOutboundSentences: _specialSentences,
    },
    formattedRaw: chatArray,
  };
}

function parseExportedWhatsappChat(chat: string, options?: ParseExportedWhatsappChatOptions) {
  const clean = cleanWhatsAppChat(chat);
  return parseWhatsAppChat(clean, options);
}

export { cleanWhatsAppChat, parseWhatsAppChat };
export default parseExportedWhatsappChat;
