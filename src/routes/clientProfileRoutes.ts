import { Router } from 'express';
import SetProfileHandler from '../handlers/client/document/SetProfileHandler';
import CustomerDecisionHandler from '../handlers/client/CustomerDecisionHandler';
import UpdatePhoneNumberHandler from '../handlers/client/UpdatePhoneNumberHandler';
import SetPhoneNumberAsHandler from '../handlers/client/setPhoneNumberAs.handler';
import AddToMyLeadsHandler from '../handlers/client/addToMyLeads.handler';

const router = Router();

export default function (parent: Router) {
  parent.use('/client/profile', router);
  router.post('/set-profile', SetProfileHandler.middlewares, SetProfileHandler.handler);
  router.post(
    '/set-phone-number-as',
    SetPhoneNumberAsHandler.middlewares,
    SetPhoneNumberAsHandler.handler
  );
  router.post(
    '/set-phone-number',
    UpdatePhoneNumberHandler.middlewares,
    UpdatePhoneNumberHandler.handler
  );
  router.post(
    '/set-customer-decision',
    CustomerDecisionHandler.middlewares,
    CustomerDecisionHandler.handler
  );
  router.post('/acquisition', AddToMyLeadsHandler.middlewares, AddToMyLeadsHandler.handler);
}
