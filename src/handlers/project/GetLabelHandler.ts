import { Request, Response } from 'express';
import { query } from 'express-validator';
import { myFirestore } from '../../services/firebaseAdmin';
import { firestore } from 'firebase-admin';
import requestChecker from '../../middlewares/requestValidator';

export default class GetLabelHandler {
  public static middlewares = [
    query('_p')
      .notEmpty()
      .isString()
      .custom(async (input, meta) => {
        const project = myFirestore.doc(input);
        const get = await project.get();
        if (!get.exists) {
          return Promise.reject('Invalid value');
        }

        meta.req.res.locals.project = get;
      }),
    requestChecker,
  ];

  public static async handler(
    req: Request<any, any, any, { _p: string }>,
    res: Response<any, { project: firestore.DocumentSnapshot<firestore.DocumentData> }>
  ) {
    const labels = await res.locals.project.ref
      .collection('labels')
      .where('active', '==', true)
      .orderBy('order', 'asc')
      .get();
    const labelResults: any[] = [];
    labels.forEach(result => {
      labelResults.push({
        ref: result.ref.path,
        ...result.data(),
      });
    });

    res.send({
      success: true,
      data: labelResults,
    });
  }
}
