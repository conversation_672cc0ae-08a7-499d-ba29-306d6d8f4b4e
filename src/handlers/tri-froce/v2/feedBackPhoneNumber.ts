import { json, RequestHandler } from 'express';
import triForceAuth<PERSON>hecker from '../../../middlewares/triforceAuthChecker';
import { FeedBackTypes } from '../../../types/firestore/leads_model.types';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import errorResponse from '../../../schema/errorResponse';
import LeadsModel from '../../../model/LeadsModel';
import successResponse from '../../../schema/successResponse';
import moment from 'moment';
import { LeadsFeedbackTableTypes } from '../../../types/services/bigQuery/leadsFeedbackTable.types';
import { bigQuery, feedbackLeadsTable } from '../../../services/bigQueryService';
import { v4 } from 'uuid';

interface ReqBody {
  phoneNumber: string;
  organization: string;
  text: FeedBackTypes;
  voice: FeedBackTypes;
}

const feedBackPhoneNumber: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    triForceAuthChecker,
    json(),
    body('phoneNumber').notEmpty(),
    body('organization').notEmpty(),
    body('text').notEmpty(),
    body('voice').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const collections = myFirestore.collection('leads');
    const docName = `${req.body.organization}-${req.body.phoneNumber}`;
    const getLeads = await collections.withConverter(LeadsModel.converter).doc(docName).get();

    if (!getLeads.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const now = moment();
    const leads = getLeads.data()!;

    if (leads.feedBackVoice || leads.feedBackText) {
      return res.status(500).send(
        errorResponse({
          type: 'OTHER_ERROR',
          messages: 'Leads feedback sudah terverifikasi sebelumnya',
        })
      );
    }

    leads.feedBackText = req.body.text;
    leads.feedBackVoice = req.body.voice;
    leads.feedbackUpdatedAt = now.toDate();

    const batch = myFirestore.batch();

    try {
      batch.set(getLeads.ref.withConverter(LeadsModel.converter), leads);
      await batch.commit();
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e?.toString(),
        })
      );
    }

    try {
      const uidV4 = v4();
      const rowBigQuery: LeadsFeedbackTableTypes = {
        organization: leads.organization as any,
        agentCode: leads.agentCode,
        agentName: leads.agentName,
        createdAt: bigQuery.timestamp(now.toDate()),
        domicile: leads.domicile!,
        feedback: {
          text: leads.feedBackText,
          voice: leads.feedBackVoice,
        },
        firstName: leads.firstName,
        lastName: leads.lastName,
        leadsCreatedAt: bigQuery.timestamp(leads.createdAt),
        phoneNumber: leads.phoneNumber,
        source: leads.source,
        uuid: uidV4,
        vehicles: leads.vehicleOptions.map(value => {
          return {
            brand: {
              name: value.brand.name,
            },
            model: {
              name: value.model.name,
            },
            variant: {
              code: value.variant.code,
              name: value.variant.name,
            },
            color: {
              code: value.color.code,
              name: value.color.name,
            },
          };
        }),
      };
      await feedbackLeadsTable.insert(rowBigQuery);
    } catch (e) {
      leads.feedbackUpdatedAt = null;
      leads.feedBackText = null;
      leads.feedBackVoice = null;

      await leads.ref.withConverter(LeadsModel.converter).set(leads);

      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e?.toString(),
        })
      );
    }

    res.send(
      successResponse({
        data: leads.toJsonResponse(),
        type: 'UPDATED',
      })
    );
  },
};

export default feedBackPhoneNumber;
