export interface ISendTemplateParams {
  vehicle?: {
    model_name: string;
    variant_code: string;
    variant_name: string;
    color_name?: string | null;
    color_code?: string | null;
    year?: string | null;
  };
  area?: string;
  target: string;
  template_name: string;
  components: IVariableComponent[];
  contactName?: string;
  bindDocuments?: {
    path: string;
    context: string;
  }[];
  notes?: string;
  projectId?: string;
}

interface IVariableComponent {
  type: string;
  parameters: IVariableComponentParameter[];
}

interface IVariableComponentParameter {
  type: string;
  text?: string;
  image?: {
    link: string;
  };
}
