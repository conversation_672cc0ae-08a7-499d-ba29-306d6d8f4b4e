import axios from 'axios';
import {
  CatalogueBaseResponse,
  GetAvailableAreaResponse,
  VariantCatalogue,
} from '../types/services/catalogueServices.types';

class CatalogueService {
  private readonly _baseUrl = 'https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1';
  private readonly _baseAxios = axios.create({
    baseURL: this._baseUrl,
    headers: {
      'x-api-key': 'TTliO9UPlx7qvPGLt73Y838gsdSvqhlY4QlUJYGe',
    },
  });

  public getAvailableArea(query?: { cityCode?: string }) {
    try {
      const queries: any = {};
      if (query?.cityCode) queries.city_code = query?.cityCode;
      return this._baseAxios.get<GetAvailableAreaResponse>('/area/amarta/city', {
        params: {
          ...queries,
        },
      });
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public getAvailableCityFromCityGroup(query: { cityGroup: string; companyCode?: string }) {
    try {
      const queries: any = {
        name: query.cityGroup,
      };
      const organizationCode = query.companyCode ?? 'amarta';
      return this._baseAxios.get<GetAvailableAreaResponse>(`/area/${organizationCode}/city`, {
        params: {
          ...queries,
        },
      });
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public async getAvailableModelByCityGroup(params: { cityGroup: string; modelName?: string }) {
    let queries: { code?: string; model_name?: string } = {};
    if (params.modelName) queries.model_name = params.modelName;

    try {
      return await this._baseAxios.get<CatalogueBaseResponse<VariantCatalogue[]>>(
        `/products/amarta/citygroup/${params.cityGroup}/model`,
        {
          params: {
            ...queries,
          },
        }
      );
    } catch (e: any) {
      throw new Error(e);
    }
  }

  public async getVariantByCityGroup(params: {
    cityGroup: string;
    variantCode?: string;
    modelName?: string;
  }) {
    try {
      let queries: { code?: string; model_name?: string } = {};

      if (params.variantCode) queries.code = params.variantCode;
      if (params.modelName) queries.model_name = params.modelName;

      return await this._baseAxios.get<CatalogueBaseResponse<VariantCatalogue[]>>(
        `/products/amarta/citygroup/${params.cityGroup}/variant`,
        {
          params: {
            ...queries,
          },
        }
      );
    } catch (e: any) {
      throw new Error(e);
    }
  }
}

const catalogueService = new CatalogueService();

export default catalogueService;
