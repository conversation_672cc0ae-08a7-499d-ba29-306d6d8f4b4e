import { describe, expect, test } from '@jest/globals';
import { encrypt, decrypt } from '../src/helpers/encryption';

describe('Encryption Module', () => {
  test('should encrypt and decrypt text correctly', () => {
    // Test data
    const plainText = 'This is a secret message';

    // Encrypt the text
    const encryptedText = encrypt(plainText);

    // Verify encrypted text is not the same as plain text
    expect(encryptedText).not.toBe(plainText);

    // Verify encrypted text contains the IV separator (:)
    expect(encryptedText).toContain(':');

    // Decrypt the text
    const decryptedText = decrypt(encryptedText);

    // Verify decrypted text matches the original plain text
    expect(decryptedText).toBe(plainText);
  });

  test('should encrypt different texts to different ciphertexts', () => {
    const text1 = 'First message';
    const text2 = 'Second message';

    const encrypted1 = encrypt(text1);
    const encrypted2 = encrypt(text2);

    // Different plain texts should result in different encrypted texts
    expect(encrypted1).not.toBe(encrypted2);
  });

  test('should encrypt same text to different ciphertexts due to random IV', () => {
    const text = 'Same message';

    const encrypted1 = encrypt(text);
    const encrypted2 = encrypt(text);

    // Same plain text should result in different encrypted texts due to random IV
    expect(encrypted1).not.toBe(encrypted2);

    // But both should decrypt to the same original text
    expect(decrypt(encrypted1)).toBe(text);
    expect(decrypt(encrypted2)).toBe(text);
  });

  test('should handle empty string', () => {
    const emptyText = '';

    const encrypted = encrypt(emptyText);
    const decrypted = decrypt(encrypted);

    expect(decrypted).toBe(emptyText);
  });

  test('should handle special characters', () => {
    const specialChars = 'Special characters: !@#$%^&*()_+{}[]|\:;"<>,.?/~`';

    const encrypted = encrypt(specialChars);
    const decrypted = decrypt(encrypted);

    expect(decrypted).toBe(specialChars);
  });

  test('should handle non-ASCII characters', () => {
    const nonAscii = 'Non-ASCII: 你好, こんにちは, 안녕하세요, Привет, مرحبا';

    const encrypted = encrypt(nonAscii);
    const decrypted = decrypt(encrypted);

    expect(decrypted).toBe(nonAscii);
  });

  test('should generate encrypted phone number', () => {
    const phoneNumber = '628561118820';
    const encrypted = encrypt(phoneNumber);
    expect(encrypted).not.toBe(phoneNumber);
  });

  test('should decrypt encrypted phone number', () => {
    const encryptedPhoneNumber = 'eewiTubAiyZTkj/DcnnHog==:ihB+r7EbgkszRiIIryidxw=='
    const decrypted = decrypt(encryptedPhoneNumber);
    console.log(decrypted);
    expect(decrypted).toBe('6285960422155');
  });
});
