import { myFirestore } from '../../src/services/firebaseAdmin';

const copyDepartment = {
  copy: async () => {
    const source = myFirestore.collection('/projects/v8GGopG1v89nd46aajEX/conversation_flow');
    const target = myFirestore.collection('/projects/WLdKug7hau0MbRzKcnqg/conversation_flow');

    const get = await source.get();

    const batch = myFirestore.batch();

    get.forEach(result => {
      batch.set(target.doc(result.ref.id), {
        ...result.data(),
      });
    });

    await batch.commit();
  },

  run: () => {
    copyDepartment.copy();
  },
};

copyDepartment.run();
