import { firestore } from 'firebase-admin';
import { IFirestoreMessageEntity } from './messages/message_types';

export interface IAdminSessionLog {
  last_heartbeat: firestore.Timestamp | Date;
  auto_end_session_at: firestore.Timestamp | Date;
  signed_in_at: firestore.Timestamp | Date;
  admin: {
    name: string;
    ref: firestore.DocumentReference;
  };
  messages: (Pick<IFirestoreMessageEntity, 'message' | 'origin'> & {
    ref: firestore.DocumentReference;
  })[];
  analytic_data: null;
  send_to_bigquery_at: firestore.Timestamp | Date | null;
}
