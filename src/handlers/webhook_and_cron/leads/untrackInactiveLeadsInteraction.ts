import { Request, Response } from 'express';
import checkAgentInteractionWithLeads from '../../../helpers/leads/checkAgentInteractionWithLeads';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';

const untrackInactiveLeadsInteraction = {
  middlewares: [],
  handler: async function (req: Request, res: Response<ResponseSchema>) {
    try {
      await checkAgentInteractionWithLeads();
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e?.toString() ?? JSON.stringify(e),
        })
      );
    }
  },
};

export default untrackInactiveLeadsInteraction;
