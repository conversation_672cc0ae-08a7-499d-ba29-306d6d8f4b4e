import { NextFunction, Request, Response } from 'express';
import errorResponse from '../../schema/errorResponse';

function triforceCheckVersionV2(req: Request, res: Response, next: NextFunction) {
  const triforceMinVersion = parseInt((process.env.TRIFORCE_MIN_VERSION ?? 31) as any);
  let headerBuildVersion = req.header('app-build-code');
  if (!headerBuildVersion) {
    return res.status(500).send(
      errorResponse({
        type: 'SERVER_ERROR',
        messages: 'Tidak ditemukan versi aplikasi',
      })
    );
  }
  let headerBuildVersionToInt = parseInt(headerBuildVersion);

  if (headerBuildVersionToInt < triforceMinVersion) {
    return res.status(426).send(
      errorResponse({
        type: 'UPGRADE_REQUIRED',
        messages: 'Versi aplikasi sudah kadaluarsa',
      })
    );
  }

  next();
}

export default triforceCheckVersionV2;
