import { json, <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import { body } from 'express-validator';
import { myAuth, myFirestore } from '../../../services/firebaseAdmin';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import errorResponse from '../../../schema/errorResponse';
import AdminModel from '../../../model/AdminModel';
import successResponse from '../../../schema/successResponse';
import requestValidator from '../../../middlewares/requestValidator';

interface ReqBody {
  email: string;
  createSource: string;
}

const deleteUserHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    json(),
    body('email').notEmpty().isEmail().trim(),
    body('createSource').notEmpty().trim(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const getAdmin = await myFirestore
      .collection('admins')
      .where('email', '==', req.body.email)
      .where('createdSource', '==', req.body.createSource)
      .withConverter(AdminModel.converter)
      .limit(1)
      .get();

    if (getAdmin.size === 0) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Email tidak dapat ditemukan',
        })
      );
    }

    let admin!: AdminModel;

    getAdmin.forEach(r => {
      admin = r.data();
    });

    try {
      await admin.doc_admin.delete();
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Gagal menghapus admin pada firestore',
        })
      );
    }

    try {
      await myAuth().deleteUser(admin.doc_admin.id);
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Gagal menghapus admin pada firebase auth',
        })
      );
    }

    return res.send(
      successResponse({
        type: 'DELETED',
        data: 'Sukses menghapus Admin Ideal',
      })
    );
  },
};

export default deleteUserHandler;
