import { body } from 'express-validator';
import successResponse from '../../../schema/successResponse';
import { HandlerTypes } from '../../../types/handler.types';
import requestValidator from '../../../middlewares/requestValidator';
import { json } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import errorResponse from '../../../schema/errorResponse';
import axios from 'axios';
import { qiscusServiceNew } from '../../../services/QiscusServiceNew';
import metaServices from '../../../services/MetaServices';
import mime from 'mime-types';

/**
 * Request body interface for media upload
 */
interface ReqBody {
  /** Project identifier */
  projectId: string;
  /** URL of the file to be uploaded */
  urlFile: string;
}

/**
 * Handler for uploading media files from URLs to different providers (Qiscus or Meta)
 */
const uploadMediaHandler: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [json(), body('projectId').notEmpty(), body('urlFile').notEmpty(), requestValidator],
  handler: async (req, res) => {
    try {
      // Get project data
      const projectRef = myFirestore.doc(`projects/${req.body.projectId}`);
      const projectData = (await projectRef.get()).data();

      if (!projectData) {
        return res.status(404).send(
          errorResponse({
            type: 'NOT_FOUND',
            messages: 'Project not found',
          })
        );
      }

      // Download file from URL
      const response = await axios.get(req.body.urlFile, {
        responseType: 'arraybuffer',
      });

      // Determine mime type and filename
      const contentType = response.headers['content-type'];
      const extension = mime.extension(contentType);
      const fileName = `file_${Date.now()}.${extension}`;

      // Create File object
      const file = new File([response.data], fileName, { type: contentType });

      let uploadResult;

      // Upload based on provider
      if (projectData.provider === 'qiscus') {
        uploadResult = await qiscusServiceNew.uploadMedia({
          file,
          type: contentType,
        });

        return res.send(
          successResponse({
            type: 'CREATED',
            data: {
              id: uploadResult.data.media[0].id,
            },
          })
        );
      } else if (projectData.provider === 'meta') {
        if (!projectData.meta) {
          return res.status(400).send(
            errorResponse({
              type: 'INVALID_REQUEST',
              messages: 'Meta configuration not found',
            })
          );
        }

        uploadResult = await metaServices.uploadMedia(
          {
            file,
            type: contentType,
            messagingProduct: 'whatsapp',
          },
          projectData.meta
        );

        return res.send(
          successResponse({
            type: 'CREATED',
            data: {
              id: uploadResult.data.id,
            },
          })
        );
      } else {
        return res.status(400).send(
          errorResponse({
            type: 'INVALID_REQUEST',
            messages: 'Unsupported provider',
          })
        );
      }
    } catch (error) {
      console.error('Upload media error:', error);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Failed to upload media',
        })
      );
    }
  },
};

export default uploadMediaHandler;
