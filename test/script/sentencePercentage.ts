import { ind, removeStopwords } from 'stopword';
import stringSimilarity from 'string-similarity-js';

const sentences = [
  {
    name: 'greeting',
    text: `⭐⭐Ingat honda, ingat Amarta Honda⭐⭐
                Beli Honda Online di amartahonda.com 
                Follow Media sosial kami
                •⁠  ⁠Tiktok : amartahonda
                •⁠  ⁠Facebook : amartahondacom
                •⁠  ⁠Instagram : amartahonda

                💰Dapatkan komisi hingga Rp.500ribu dengan mengirimkan data prospek motor kepada saya💰

                ❤️️Salam Satu Hati❤️️`,
  },
];

const cleanAndTokenizeString = (text: string) => {
  function removeNonAlphaNumeric(text: string): string {
    let _text = text;
    _text = _text.replace(/\n/g, ' ');
    _text = _text.replace(/[^a-zA-Z0-9 ]/g, '');
    _text = _text.toLowerCase();
    return _text;
  }

  let _text = removeNonAlphaNumeric(text);
  let _splitBySpace = _text.split(' ').filter(r => !!r);
  return removeStopwords(_splitBySpace, ind);
};

const sentencePercentage = () => {
  const sampleText = `
    â­â­Ingat honda, ingat Amarta Hondaâ­â­
Beli Honda Online di amartahonda.com 
Follow Media sosial kami
- Tiktok : amartahonda
- Facebook : amartahondacom
- Instagram : amartahonda

ðŸ’°Dapatkan komisi hingga Rp.500ribu dengan mengirimkan data prospek motor kepada sayaðŸ’°

â¤ï¸ï¸Salam Satu Hatiâ¤ï¸ï¸
`;

  for (const sentence of sentences) {
    const result = stringSimilarity(
      cleanAndTokenizeString(sentence.text).join(' '),
      cleanAndTokenizeString(sampleText).join(' ')
    );
    console.log(sentence.name, result);
  }
};

sentencePercentage();
