import { HandlerTypes } from '../../types/handler.types';
import multer from 'multer';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import errorResponse from '../../schema/errorResponse';
import { myFirestore } from '../../services/firebaseAdmin';
import { ILeadsDocument, LeadsVehicleOptions } from '../../types/firestore/leads_model.types';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import moment from 'moment';
import { RawLeads } from '../../types/firestore/raw_leads_types';
import { v4 } from 'uuid';
import { firestore } from 'firebase-admin';
import FreeLeadsModel from '../../model/FreeLeadsModel';
import { RawLeadsTable } from '../../types/services/bigQuery/rawLeadsTable.types';
import { bigQuery, rawLeadsTable } from '../../services/bigQueryService';
import successResponse from '../../schema/successResponse';
import { bucketKataAi } from '../../services/cloudStorage';
import otocomAddLeadsXlsAuthorizer from '../../middlewares/otocomAddLeadsXlsAuthorizer';
import { DecodedIdToken } from 'firebase-admin/lib/auth';
import sendTemplate from '../../services/sendTemplate/sendTemplate';

const fileUpload = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

interface Rows {
  Id: string;
  CreateTime: string;
  TentativeBuyingDate: string;
  'SC Name': string;
  'Lead Name': string;
  'Lead Phone Number': string;
  Model: string;
  Variant: string;
  City: string;
  Address: string;
  'Organization Name': string;
}

const headerColumnsTitle = ['Id', 'Lead Name', 'Lead Phone Number', 'Model', 'Variant', 'City'];

interface ReqBody {
  headers: string[];
  rows: Rows[];
}

const addFreeLeadsOtocomFromFileXls: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    otocomAddLeadsXlsAuthorizer,
    fileUpload.single('xlsFile'),
    function (req, res, next) {
      req.body = JSON.parse(req.body.data);
      next();
    },
    body('headers')
      .isArray()
      .custom((input: string[]) => {
        for (const string of headerColumnsTitle) {
          const indexOf = input.indexOf(string);
          if (indexOf < 0) {
            throw new Error(`Kolom "${string}" tidak ditemukan.`);
          }
        }
        return true;
      }),
    body('rows').notEmpty().isArray({ min: 1 }).withMessage('Minimal ada satu baris'),
    body('rows.*["Lead Phone Number"]')
      .notEmpty()
      .withMessage('Nomor telepon dibutuhkan')
      .bail()
      .customSanitizer(input => input.toString())
      .customSanitizer(input => {
        return phoneNumberCountryCodeSanitizer(input, '62', '62');
      }),
    body('rows.*["Lead Name"]').notEmpty().withMessage('Nama Konsumen dibutuhkan').bail(),
    body('rows.*["Model"]').notEmpty().withMessage('Model dibutuhkan').bail(),
    body('rows.*["Variant"]').notEmpty().withMessage('Variant dibutuhkan').bail(),
    body('rows.*["City"]').notEmpty().withMessage('Variant dibutuhkan').bail(),
    requestValidator,
  ],
  handler: async (req, res) => {
    if (!req.file?.buffer) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
          messages: 'File format xls/xlsx leads diperlukan!',
        })
      );
    }

    const user: DecodedIdToken = res.locals.decodedToken;

    const bigQueryRows: RawLeadsTable[] = [];

    const organization = 'amartahonda';
    const now = moment();

    const otocomUploadFileLogsCollection = myFirestore
      .collection('leads_logs')
      .doc('add_free_leads')
      .collection('otocom_upload_file_xls');

    const rawLeadsCollection = myFirestore.collection('raw_leads');
    const freeLeadsCollection = myFirestore.collection('free_leads');
    const leadsCollection = myFirestore.collection('leads');

    const otocomCityCollection = myFirestore.collection('/master/otocom/cities');
    const otocomVehicleCollection = myFirestore.collection('/master/otocom/vehicles');

    const allLeadsPhoneNumbersInFile: string[] = [];
    const leadsPhoneNumberFailedToInsert: string[] = [];
    const rawLeadsRefs: firestore.DocumentReference[] = [];

    for (const row of req.body.rows) {
      const globalVar: {
        batch: firestore.WriteBatch;
      } = {
        batch: myFirestore.batch(),
      };

      const phoneNumber = row['Lead Phone Number'];
      allLeadsPhoneNumbersInFile.push(phoneNumber);

      const freeLeadsDocName = `${organization}-${phoneNumber}`;

      const acquiredLeads = await leadsCollection.doc(freeLeadsDocName).get();
      if (acquiredLeads.exists) {
        continue;
      }

      const findFreeLeads = await freeLeadsCollection
        .doc(freeLeadsDocName)
        .withConverter(FreeLeadsModel.converter)
        .get();
      if (findFreeLeads.exists) {
        continue;
      }

      const findInRawLeads = await rawLeadsCollection.where('phoneNumber', '==', phoneNumber).get();

      let vehicle: LeadsVehicleOptions | undefined;

      const getVehicle = await otocomVehicleCollection
        .where('otoComModelName', '==', row.Model)
        .where('otoComVariantName', '==', row.Variant)
        .get();

      if (!getVehicle.empty) {
        getVehicle.forEach(r => {
          const vehicleMasterData: any = r.data();
          vehicle = {
            color: {
              code: '',
              name: '',
            },
            brand: {
              name: vehicleMasterData.trimitraBrandName,
            },
            variant: {
              name: vehicleMasterData.trimitraVariantName,
              code: vehicleMasterData.trimitraVariantCode,
            },
            model: {
              name: vehicleMasterData.trimitraModelName,
            },
          };
        });
      } else {
        console.log('OTOCOM_VEHICLE_NOT_MAPPED', JSON.stringify(row));
      }

      const getCity = await otocomCityCollection.where('otoComName', '==', row['City']).get();

      let city: ILeadsDocument['domicile'] = {
        cityCode: '',
        cityName: '',
        provinceCode: '',
        provinceName: '',
      };
      let area: string = '';
      if (!getCity.empty) {
        getCity.forEach(result => {
          const data = result.data() as any;
          city = {
            provinceName: data.trimitraProvinceName,
            provinceCode: data.trimitraProvinceCode,
            cityName: data.trimitraCityName,
            cityCode: data.trimitraCityCode,
          };

          area = data.trimitraCityGroup || '';
        });
      } else {
        console.log('OTOCOM_CITY_NOT_MAPPED', JSON.stringify(row));
      }

      const rawLeadsData: RawLeads<Date> = {
        id: row['Id'],
        phoneNumber: phoneNumber,
        fullName: row['Lead Name'],
        city: row['City'],
        vehicleModel: row['Model'],
        vehicleVariant: row['Variant'],
        organization: organization,
        externalData: {
          ...row,
        },
        source: 'otolms',
        externalId: row['Id'],
        duplicated: !findInRawLeads,
        inFreeLeads: false,
        shouldPushToFreeLeadsAt: now.toDate(),
        freeLeadsPath: null,
        freeLeadsCreatedAt: null,

        shouldPushToBigQueryAt: now.toDate(),
        donePushToBigQuery: false,
        donePushToBigQueryAt: null,

        createdAt: now.toDate(),

        mappedData: {
          domicile: city,
          vehicle: {
            brand: {
              name: vehicle ? 'HONDA' : '',
            },
            model: {
              name: vehicle?.model?.name || '',
            },
            variant: {
              name: vehicle?.variant?.name || '',
              code: vehicle?.variant?.code || '',
            },
            color: {
              code: '',
              name: '',
            },
          },
          area: area || '',
          paymentPlan: null,
        },
        alreadyInMyLeads: acquiredLeads.exists,
      };
      const rawLeadsDocRef = rawLeadsCollection.doc();
      globalVar.batch.set(rawLeadsDocRef, rawLeadsData);

      const freeLeadsDocRef: firestore.DocumentReference =
        freeLeadsCollection.doc(freeLeadsDocName);
      const price = rawLeadsData.organization === 'amartahonda' ? 20000 : 50000;
      const freeLeads = new FreeLeadsModel({
        rawLeadsRef: rawLeadsDocRef,
        area: rawLeadsData.mappedData?.area ?? null,
        externalId: rawLeadsData.externalId ?? null,
        title: null,
        firstName: rawLeadsData.fullName,
        lastName: '',
        phoneNumber: rawLeadsData.phoneNumber,
        paymentPlan: null,
        hasVehicleLoan: false,
        createdAt: now.toDate(),

        domicile: rawLeadsData.mappedData?.domicile ?? null,

        driverLicense_number: null,
        email: null,
        idCard_number: null,
        nextTotalVehicleOwnerShip: null,
        organization: rawLeadsData.organization as any,
        purchasePlan: null,
        source: 'otolms',
        vehicleOptions: rawLeadsData.mappedData?.vehicle ? [rawLeadsData.mappedData.vehicle] : [],
        vehicleUsage: null,
        ref: freeLeadsDocRef,

        isAcquired: false,
        acquiredAt: null,
        acquiredAgentCode: null,
        acquiredAgentName: null,

        createdBy: user.email,

        price,
        ideal: null,
      });
      globalVar.batch.set(freeLeadsDocRef.withConverter(FreeLeadsModel.converter), freeLeads);
      globalVar.batch.update(rawLeadsDocRef, {
        inFreeLeads: true,
        freeLeadsPath: freeLeadsDocRef,
        freeLeadsCreatedAt: now.toDate(),
      });

      try {
        await globalVar.batch.commit();
      } catch {
        leadsPhoneNumberFailedToInsert.push(phoneNumber);
        continue;
      }

      sendTemplate
        .sendTemplateMetaV2({
          projectId: 'WLdKug7hau0MbRzKcnqg',
          bindDocuments: [
            {
              path: rawLeadsDocRef.path,
              context: 'add_raw_leads',
            },
            {
              path: freeLeadsDocRef.path,
              context: 'add_leads',
            },
          ],
          template_name: 'leads1',
          components: [
            {
              type: 'body',
              parameters: [
                {
                  type: 'text',
                  text: rawLeadsData.fullName.toUpperCase(),
                },
                {
                  type: 'text',
                  text: 'OTOLMS',
                },
                {
                  type: 'text',
                  text: rawLeadsData.mappedData.vehicle.model.name,
                },
              ],
            },
          ],
          contactName: rawLeadsData.fullName.toUpperCase(),
          target: phoneNumber,
          area: area || undefined,
          vehicle: {
            model_name: vehicle?.model.name || '',
            variant_name: vehicle?.variant.name || '',
            variant_code: vehicle?.variant.code || '',
            color_code: vehicle?.color.code || '',
            color_name: vehicle?.color.name || '',
            year: '',
          },
        })
        .then()
        .catch(reason => {
          console.log('FAILED_SEND_TEMPLATE_LEADS', JSON.stringify(req.body), reason);
        });

      rawLeadsRefs.push(rawLeadsDocRef);
      bigQueryRows.push({
        city: rawLeadsData.city,
        city_group: rawLeadsData.mappedData.area,
        duplicated: rawLeadsData.duplicated,
        external_id: rawLeadsData.externalId,
        full_name: rawLeadsData.fullName,
        organization: rawLeadsData.organization,
        phone_number: rawLeadsData.phoneNumber,
        source: rawLeadsData.source,

        trimitra_city: rawLeadsData.mappedData.domicile
          ? {
              code: rawLeadsData.mappedData.domicile.cityCode,
              name: rawLeadsData.mappedData.domicile.cityName,
            }
          : null,
        trimitra_province: rawLeadsData.mappedData.domicile
          ? {
              code: rawLeadsData.mappedData.domicile.provinceCode,
              name: rawLeadsData.mappedData.domicile.provinceName,
            }
          : null,

        trimitra_vehicle: rawLeadsData.mappedData.vehicle
          ? {
              model: rawLeadsData.mappedData.vehicle.model,
              variant: rawLeadsData.mappedData.vehicle.variant,
              color: null,
            }
          : null,

        uid: rawLeadsDocRef.id,
        vehicle_model: rawLeadsData.vehicleModel,
        vehicle_variant: rawLeadsData.vehicleVariant,
        whatsapp_delivered: null,
        whatsapp_delivered_at_timestamp: null,
        data_leads_created_at_timestamp: bigQuery.timestamp(rawLeadsData.createdAt),
        bq_created_at_datetime: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
        bq_created_at_timestamp: bigQuery.timestamp(now.toDate()),
        is_in_my_leads: rawLeadsData.alreadyInMyLeads || false,
      });
    }

    const uidV4 = v4();
    const file = bucketKataAi.file(`otocomXlsFiles/[${uidV4}]_${req.file.originalname}`);

    file
      .save(req.file.buffer, {
        public: true,
      })
      .catch();

    otocomUploadFileLogsCollection
      .doc(uidV4)
      .set({
        fileLocation: file.publicUrl(),
        leadsPhoneNumberFailedToInsert,
        allLeadsPhoneNumbersInFile,
        createdAt: now.toDate(),
      })
      .catch();

    try {
      await rawLeadsTable.insert(bigQueryRows);
      for (const rawLeadsRef of rawLeadsRefs) {
        const batch = myFirestore.batch();
        batch.update(rawLeadsRef, {
          donePushToBigQuery: true,
          donePushToBigQueryAt: now.toDate(),
        });
        await batch.commit();
      }
    } catch (e) {
      console.log(
        'FAILED_INSERT_RAW_LEADS_TO_BIG_QUERY',
        JSON.stringify(bigQueryRows),
        e?.toString()
      );
    }

    res.send(
      successResponse({
        data: {
          leadsPhoneNumberFailedToInsert,
        },
        type: 'CREATED',
      })
    );
  },
};

export default addFreeLeadsOtocomFromFileXls;
