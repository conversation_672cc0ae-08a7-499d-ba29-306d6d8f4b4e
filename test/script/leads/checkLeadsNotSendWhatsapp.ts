import { myFirestore } from '../../../src/services/firebaseAdmin';
import moment from 'moment';
import sendTemplate from '../../../src/services/sendTemplate/sendTemplate';

const checkLeadsNotSendWhatsapp = async () => {
  const collection = myFirestore.collection('raw_leads');
  const startDate = moment('2023-12-03', 'YYYY-MM-DD').startOf('day');
  const endDate = startDate.clone().endOf('day');

  const get = await collection
    .where('createdAt', '>', startDate.toDate())
    .where('createdAt', '<', endDate.toDate())
    .get();

  const data: any[] = [];
  get.forEach(result => {
    const _data = result.data();
    if (!_data.whatsapp && _data.organization === 'amartahonda') {
      data.push({
        ..._data,
        ref: result.ref,
      });
    }
  });

  for (const datum of data) {
    try {
      const vehicle = datum.mappedData.vehicle;
      const params = {
        bindDocuments: [
          {
            path: datum.ref.path,
            context: 'add_raw_leads',
          },
        ],
        template_name: 'leads_welcome_1',
        components: [
          {
            type: 'body',
            parameters: [
              {
                type: 'text',
                text: datum.fullName.toUpperCase(),
              },
              {
                type: 'text',
                text: 'OTOCOM',
              },
              {
                type: 'text',
                text: vehicle.model.name,
              },
            ],
          },
        ],
        contactName: datum.fullName,
        target: datum.phoneNumber,
        area: datum.mappedData.area || undefined,
        vehicle: {
          model_name: vehicle?.model.name || '',
          variant_name: vehicle?.variant.name || '',
          variant_code: vehicle?.variant.code || '',
          color_code: vehicle?.color.code || '',
          color_name: vehicle?.color.name || '',
          year: '',
        },
      };

      await sendTemplate.sendTemplateQiscus(params);
    } catch (e) {
      console.log(datum);
    }
  }
};

checkLeadsNotSendWhatsapp();
