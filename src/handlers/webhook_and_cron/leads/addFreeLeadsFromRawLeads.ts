import { RequestHandler } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import moment from 'moment';
import { RawLeads } from '../../../types/firestore/raw_leads_types';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import successResponse from '../../../schema/successResponse';
import collect from 'collect.js';

const addFreeLeadsFromRawLeads: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [],
  handler: async (req, res) => {
    const now = moment();

    const rawLeadsCollection = myFirestore.collection('raw_leads');
    const freeLeadsCollection = myFirestore.collection('free_leads');
    const leadsCollection = myFirestore.collection('leads');

    const getRawLeads = await rawLeadsCollection
      .where('shouldPushToFreeLeadsAt', '<=', now.toDate())
      .where('inFreeLeads', '==', false)
      .where('alreadyInMyLeads', '==', false)
      .get();

    const rawLeadsResults: FirebaseFirestore.QueryDocumentSnapshot[] = [];

    getRawLeads.forEach(result => {
      rawLeadsResults.push(result);
    });

    const rawLeadsResultCollections = collect(rawLeadsResults);
    const rawLeadsResultChunk = rawLeadsResultCollections.chunk(200);

    for (let i = 0; i < rawLeadsResultChunk.all().length; i++) {
      const part = rawLeadsResultChunk.all()[i];
      const batchAddFreeLeads = myFirestore.batch();

      for (const rawLeads of part) {
        const data = rawLeads.data() as RawLeads;
        const acquiredLeads = await leadsCollection
          .doc(`${data.organization}-${data.phoneNumber}`)
          .get();

        if (!acquiredLeads.exists) {
          const freeLeadsDocName = `${data.organization}-${data.phoneNumber}`;
          const freeLeadsDocRef = freeLeadsCollection.doc(freeLeadsDocName);
          const price = data.organization === 'amartahonda' ? 20000 : 50000;

          const freeLeadsModel = new FreeLeadsModel({
            rawLeadsRef: rawLeads.ref,
            area: data.mappedData.area ?? null,
            externalId: data.externalId,
            title: null,
            firstName: data.fullName,
            lastName: '',
            phoneNumber: data.phoneNumber,
            paymentPlan: data.mappedData.paymentPlan ?? null,
            hasVehicleLoan: false,
            createdAt: now.toDate(),

            domicile: data.mappedData.domicile,
            driverLicense_number: null,
            email: null,
            idCard_number: null,
            nextTotalVehicleOwnerShip: null,
            organization: data.organization as any,
            purchasePlan: null,
            source: data.source,
            vehicleOptions: data.mappedData.vehicle ? [data.mappedData.vehicle] : [],
            vehicleUsage: null,
            ref: freeLeadsDocRef,

            isAcquired: false,
            acquiredAt: null,
            acquiredAgentCode: null,
            acquiredAgentName: null,

            whatsapp: data.whatsapp as any,

            price,
            ideal: null,
          });

          batchAddFreeLeads.set(
            freeLeadsDocRef.withConverter(FreeLeadsModel.converter),
            freeLeadsModel
          );
          batchAddFreeLeads.update(rawLeads.ref, {
            inFreeLeads: true,
            freeLeadsPath: freeLeadsDocRef,
            freeLeadsCreatedAt: now.toDate(),
          });
        } else {
          batchAddFreeLeads.update(rawLeads.ref, {
            alreadyInMyLeads: true,
          });
        }
      }

      try {
        await batchAddFreeLeads.commit();
      } catch {
        console.log('FAILED_ADD_FREE_LEADS_FROM_RAW_LEADS', part);
      }
    }

    res.send(
      successResponse({
        data: null,
      })
    );
  },
};

export default addFreeLeadsFromRawLeads;
