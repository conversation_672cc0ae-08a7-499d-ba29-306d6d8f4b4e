import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import errorResponse from '../../../schema/errorResponse';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { getAgent } from '../../../services/agent/fetchAgent';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import LeadsModel from '../../../model/LeadsModel';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import aggregatesAgent from '../../../helpers/leads/aggregatesAgent';
import successResponse from '../../../schema/successResponse';
import { errorLogsAddLeads } from '../../../helpers/leads/errorLogs';
import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import ClientModel from '../../../model/ClientModel';
import moment from 'moment';
import { firestore } from 'firebase-admin';
import amartavipService from '../../../services/amartavip/amartavip.services';
import { isAxiosError } from 'axios';

interface RequestBody {
  phoneNumber: string;
  organization: string;
  agentCode: string;
  transactionQrisId: string;
}

const leadsAcquisition: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, RequestBody>;
} = {
  middlewares: [
    triForceAuthChecker,
    json(),
    body('phoneNumber').notEmpty(),
    body('organization').notEmpty(),
    body('agentCode').notEmpty(),
    body('transactionQrisId').optional(),
    requestValidator,
  ],
  handler: async function (req, res) {
    const freeLeadsCollection = myFirestore.collection('free_leads');

    const leadsCollection = myFirestore.collection('leads');

    const docName = `${req.body.organization}-${req.body.phoneNumber}`;

    const getFreeLeads = await freeLeadsCollection
      .doc(docName)
      .withConverter(FreeLeadsModel.converter)
      .get();
    const agent = await getAgent(req.body.agentCode);

    const now = moment();

    if (!getFreeLeads.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Free Leads tidak ditemukan',
        })
      );
    }

    const dataFreeLeads = getFreeLeads.data()!;

    if (dataFreeLeads.isAcquired) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Free Leads sudah diakuisisi',
        })
      );
    }

    const getAcquiredLeads = await leadsCollection
      .withConverter(LeadsModel.converter)
      .doc(docName)
      .get();
    if (getAcquiredLeads.exists) {
      const dataAcquiredLeads = getAcquiredLeads.data()!;
      await dataFreeLeads.ref.update({
        acquiredAgentCode: dataAcquiredLeads.agentCode,
        acquiredAgentName: dataAcquiredLeads.agentName,
        acquiredAt: dataAcquiredLeads.createdAt,
        isAcquired: true,
      });

      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Free Leads sudah diakuisisi',
        })
      );
    }

    if (req.body.transactionQrisId) {
      try {
        const checkPayment = await amartavipService.checkLeadsPayment(
          req.body.transactionQrisId,
          res.locals.token
        );
        console.log(checkPayment.data);
        const results = checkPayment.data;
        if (results.data.length === 0) {
          return res.status(404).send(
            errorResponse({
              type: 'ENTITY_NOT_FOUND',
              messages: 'Free Leads belum dibayar',
            })
          );
        }

        const paymentResult = results.data[0];

        if (paymentResult.status !== 'PAID') {
          return res.status(404).send(
            errorResponse({
              type: 'ENTITY_NOT_FOUND',
              messages: 'Free Leads belum dibayar',
            })
          );
        }
      } catch (e) {
        if (isAxiosError(e)) {
          console.log(e.response?.data);
        }
        return res.status(404).send(
          errorResponse({
            type: 'ENTITY_NOT_FOUND',
            messages: 'Free Leads belum dibayar',
          })
        );
      }
    }

    dataFreeLeads.acquiredAgentCode = req.body.agentCode;
    dataFreeLeads.acquiredAgentName = agent?.name ?? null;
    dataFreeLeads.acquiredAt = now.toDate();
    dataFreeLeads.isAcquired = true;
    dataFreeLeads.transactionId = req.body.transactionQrisId || null;

    const notes: ILeadsNotesDocument<Date> = {
      notes: `Leads bersumber dari Akuisisi Free Leads ${dataFreeLeads.source}`,
      updatedAt: new Date(),
      updatedByUser: null,
      statusLevel: 0,
      agentCode: req.body.agentCode,
      event: 'init',
      phoneNumber: req.body.phoneNumber,
      organization: dataFreeLeads.organization,
      firstName: dataFreeLeads.firstName,
      lastName: dataFreeLeads.lastName ?? '',
      agentName: agent?.name ?? '',
      moveToCold: false,
      reactivate: {
        currentTotal: 0,
      },
      totalUpdateNotes: 1,
    };

    const leadsDoc = new LeadsModel({
      agentCode: req.body.agentCode,
      area: dataFreeLeads.area,
      agentName: agent?.name ?? '',
      phoneNumberAgent: agent?.phoneNumbers.map(value => value.phone) ?? [],
      downPaymentPlan: null,
      createdAt: now.toDate(),
      domicile: dataFreeLeads.domicile,
      email: dataFreeLeads.email,
      firstName: dataFreeLeads.firstName,
      lastName: dataFreeLeads.lastName ?? '',
      hasVehicleLoan: dataFreeLeads.hasVehicleLoan,
      paymentPlan: dataFreeLeads.paymentPlan,
      phoneNumber: dataFreeLeads.phoneNumber,
      ref: leadsCollection.doc(dataFreeLeads.ref.id),
      source: `freeLeads-${dataFreeLeads.source}`,
      organization: dataFreeLeads.organization,
      title: dataFreeLeads.title,
      vehicleOptions: dataFreeLeads.vehicleOptions,
      vehicleUsage: dataFreeLeads.vehicleUsage ?? 'individual',
      statusLevel: 0,

      purchasePlan: dataFreeLeads.purchasePlan ?? 'firstVehicle',
      nextTotalVehicleOwnerShip: '1',

      isTracking: true,
      notes: notes.notes,

      updateHistories: [
        {
          ...notes,
        },
      ],

      idCard_number: null,
      driverLicense_number: null,
      whatsapp: {
        idealPath: dataFreeLeads.whatsapp?.idealPath ?? null,
        messageId: dataFreeLeads.whatsapp?.messageId ?? '',
        response: dataFreeLeads.whatsapp?.response
          ? {
              ...dataFreeLeads.whatsapp.response,
              createdAt: dataFreeLeads.whatsapp.response.repliedAt,
            }
          : null,
        failed: !dataFreeLeads.whatsapp?.statuses?.delivered,
      },

      fromFreeLeads: true,
      freeLeadsCreatedAt: dataFreeLeads.createdAt,
    });

    try {
      const batch = myFirestore.batch();

      batch.set(leadsDoc.ref.withConverter(LeadsModel.converter), leadsDoc);
      batch.set(myFirestore.collection('leads_notes').doc(), notes);
      batch.set(dataFreeLeads.ref.withConverter(FreeLeadsModel.converter), dataFreeLeads);

      await batch.commit();

      await aggregatesAgent(req.body.agentCode, leadsDoc.organization).then();

      // if (leadsDoc.organization === "amartachery") {
      //     let messageTelegram = `=== AKUISISI LEADS BARU ===` +
      //         `\n` +
      //         `\nNama Depan: ${leadsDoc.firstName}` +
      //         `\nNama Akhir: ${leadsDoc.lastName}` +
      //         `\nNomor Telepon: ${hidePhoneNumber(leadsDoc.phoneNumber)}` +
      //         `\nNama Agen: ${leadsDoc.agentName}` +
      //         `\nKode Agen: ${leadsDoc.agentCode}` +
      //         `\nNotes: ${leadsDoc.notes}` +
      //         `\nModel: ${leadsDoc.vehicleOptions.map(value => value.model.name).join(", ")}`
      //     ;
      //     telegramServices.sendMessage("-1002118167928", messageTelegram)
      //         .then()
      //         .catch()
      // }
    } catch (e: any) {
      errorLogsAddLeads(req, e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }

    const getClientInChatIdeal = await myFirestore
      .collection('clients')
      .where('contacts.whatsapp', '==', req.body.phoneNumber)
      .withConverter(ClientModel.converter)
      .get();

    if (!getClientInChatIdeal.empty) {
      let dataClient!: ClientModel;
      getClientInChatIdeal.forEach(result => {
        dataClient = result.data()!;
      });

      await dataClient.ref.update({
        acquiredLeadsStatus: {
          agentCode: req.body.agentCode,
          agentName: agent?.name || '',
          acquired: true,
          acquiredAt: firestore.Timestamp.fromDate(now.toDate()),
          organization: req.body.organization,
        },
      });
    }

    res.send(
      successResponse({
        data: leadsDoc.toJsonResponse(),
        type: 'CREATED',
      })
    );
  },
};

export default leadsAcquisition;
