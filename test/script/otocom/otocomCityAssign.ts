import cities from './otocomCity.json';
import mapOtocomCityToTrimitra from './mapOtocomCityToTrimitra.json';
import axios from 'axios';
import { myFirestore } from '../../../src/services/firebaseAdmin';
import { firestore } from 'firebase-admin';

const otocomCityAssign = {
  generateJson: async () => {
    const results: any[] = [];
    for (const c of cities) {
      const get = await axios.get(
        'https://asia-southeast2-ideal-trimitra.cloudfunctions.net/api-open-search/search-region',
        {
          params: {
            q: c.otoComName,
          },
        }
      );
      const response = get.data.data[0];
      results.push({
        ...c,
        trimitraCityCode: response.city_code,
        trimitraCityName: response.city_name,
        trimitraProvinceName: response.province_name,
        trimitraProvinceCode: response.province_code,
      });
    }
    return results;
  },

  insertToFirestore: async () => {
    const collection = myFirestore.collection('/master/otocom/cities');
    const batch = myFirestore.batch();

    for (const c of mapOtocomCityToTrimitra) {
      const find = await collection.where('otoComId', '==', c.otoComId).get();

      let doc!: firestore.DocumentReference;
      if (find.empty) {
        doc = myFirestore.doc('');
      } else {
        find.forEach(d => {
          doc = d.ref;
        });
      }
      batch.set(doc, c);
    }
    await batch.commit();
  },
};

async function run() {
  await otocomCityAssign.insertToFirestore();
}

run();
