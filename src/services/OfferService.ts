import axios, { AxiosError } from 'axios';
import { IGetOffer } from '../types/services/offerService.types';

class OfferService {
  private base = axios.create({
    baseURL: 'https://42cjbxpaa8.execute-api.ap-southeast-1.amazonaws.com/v1',
    headers: {
      'X-Api-Key': '9bekwohY878MgiMsRL0Wk2Xsgv4QsxtW4jEIuBqb',
    },
  });

  public async getByOfferCode(offerCode: string) {
    try {
      const get = await this.base.get<IGetOffer>(`/offer/public/${offerCode}?company=amarta`);
      const { data } = get;
      return data.data;
    } catch (e: any) {
      const error = e as AxiosError;
      Object.assign(new Error(error.message), error);
    }
  }
}

const offerService = new OfferService();

export default offerService;
