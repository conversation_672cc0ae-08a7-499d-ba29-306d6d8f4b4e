import { json, Request, RequestHandler, Response } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import { myFirestore } from '../../services/firebaseAdmin';
import requestValidator from '../../middlewares/requestValidator';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import moment from 'moment';
import { firestore } from 'firebase-admin';
import FreeLeadsModel from '../../model/FreeLeadsModel';
import { v4 as uuidV4 } from 'uuid';
import { RawLeadsTable } from '../../types/services/bigQuery/rawLeadsTable.types';
import { bigQuery, rawLeadsTable } from '../../services/bigQueryService';

interface RequestBody {
  title: string;
  area: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  provinceName: string;
  provinceCode: string;
  cityName: string;
  cityCode: string;
  vehicleUsage: 'individual' | 'shared' | 'corporate';
  paymentPlan: 'cash' | 'credit';
  hasVehicleLoan: boolean;
  vehicleOptions:
    | {
        brand: {
          name: string;
        };
        model: {
          name: string;
        };
        variant: {
          code: string;
          name: string;
        };
        color: {
          code: string;
          name: string;
        };
      }[]
    | null;
  organization: string;
  source: string;
  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition';
  nextTotalVehicleOwnerShip: string;

  price: number;
  notes: string;

  idCard_number: string | null;
  driverLicense_number: string | null;

  customSourceSendToWhatsapp?: string | null;

  disableWhatsapp: boolean | null;
}

type ReqBody = RequestBody[];

function extractNumberFromString(inputString: string) {
  const startIndex = inputString.indexOf('[');
  const endIndex = inputString.indexOf(']');

  if (startIndex === -1 || endIndex === -1) {
    return null; // Kembalikan null jika tidak ada "[" atau "]"
  }

  const numberString = inputString.substring(startIndex + 1, endIndex);

  const number = parseInt(numberString);

  if (!isNaN(number)) {
    return number;
  } else {
    return null;
  }
}

const addFreeLeadHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [
    leadsAuthorizer,
    json(),
    function (req, res, next) {
      console.log('ADD_MULTI_FREE_LEADS', 'req_body', JSON.stringify(req.body));

      if (!Array.isArray(req.body)) {
        req.body = [req.body];
        res.locals.isSingle = true;
      }

      next();
    },
    body('*.title').optional({ values: 'falsy' }),
    body('*.area').optional({ values: 'falsy' }).toUpperCase(),
    body('*.firstName')
      .notEmpty()
      .withMessage((_value, meta) => {
        const row = extractNumberFromString(meta.path);
        return `Nama Depan pada baris ke ${(row ?? 0) + 1} dibutuhkan.`;
      }),
    body('*.lastName')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? '' : input)),
    body('*.phoneNumber')
      .notEmpty()
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62'))
      .withMessage((_value, meta) => {
        const row = extractNumberFromString(meta.path);
        return `Nomor Telepon pada baris ke ${(row ?? 0) + 1} dibutuhkan.`;
      })
      .isMobilePhone('id-ID')
      .withMessage((_value, meta) => {
        const row = extractNumberFromString(meta.path);
        return `Nomor Telepon pada baris ke ${(row ?? 0) + 1} tidak valid.`;
      })
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('*.email').optional({ values: 'falsy' }).isEmail(),
    body('*.provinceName')
      .notEmpty()
      .withMessage((_value, meta) => {
        const row = extractNumberFromString(meta.path);
        return `Nama Provinsi pada baris ke ${(row ?? 0) + 1} dibutuhkan.`;
      }),
    body('*.provinceCode')
      .notEmpty()
      .withMessage((_value, meta) => {
        const row = extractNumberFromString(meta.path);
        return `Kode Provinsi pada baris ke ${(row ?? 0) + 1} dibutuhkan.`;
      }),
    body('*.cityName')
      .notEmpty()
      .withMessage((_value, meta) => {
        const row = extractNumberFromString(meta.path);
        return `Nama Kota pada baris ke ${(row ?? 0) + 1} dibutuhkan.`;
      }),
    body('*.cityCode')
      .notEmpty()
      .withMessage((_value, meta) => {
        const row = extractNumberFromString(meta.path);
        return `Kode Kota pada baris ke ${(row ?? 0) + 1} dibutuhkan.`;
      }),
    body('*.vehicleUsage')
      .optional({ values: 'falsy' })
      .isIn(['individual', 'shared', 'corporate'])
      .withMessage('Invalid vehicle usage'),
    body('*.paymentPlan')
      .optional({ values: 'falsy' })
      .isIn(['cash', 'credit'])
      .withMessage('Invalid payment plan'),
    body('*.hasVehicleLoan')
      .customSanitizer(input => (!input ? false : input))
      .isBoolean()
      .toBoolean()
      .withMessage('Invalid hasVehicleLoan format'),
    body('*.vehicleOptions')
      .optional({ values: 'falsy' })
      .isArray({ max: 3 })
      .custom((value: RequestBody['vehicleOptions']) => {
        if (value) {
          for (const option of value) {
            if (
              !option.brand?.name ||
              !option.model?.name ||
              !option.variant?.code ||
              !option.variant?.name ||
              !option.color?.code ||
              !option.color?.name
            ) {
              throw new Error('Invalid vehicle option format');
            }
          }
        }
        return true;
      }),
    body('*.organization')
      .notEmpty()
      .withMessage('Organization is required')
      .customSanitizer(input => input.replace(/ /g, '')),
    body('*.purchasePlan')
      .optional({ values: 'falsy' })
      .isIn(['firstVehicle', 'vehicleReplacement', 'vehicleAddition'])
      .withMessage('purchasePlan is required'),
    body('*.nextTotalVehicleOwnerShip').optional({ values: 'falsy' }),
    body('*.idCard_number')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? null : input)),
    body('*.driverLicense_number')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? null : input)),

    body('*.price').optional({ values: 'falsy' }).isNumeric().toInt(),

    body('*.customSourceSendToWhatsapp')
      .optional({ values: 'falsy' })
      .isString()
      .withMessage('customSourceSendToWhatsapp harus string'),
    body('*.disableWhatsapp')
      .optional()
      .isBoolean()
      .withMessage('disableWhatsapp Harus true atau false')
      .toBoolean(),

    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response) => {
    const now = moment();
    const freeLeadsCollection = myFirestore.collection('free_leads');
    const leadsCollection = myFirestore.collection('leads');
    const rawLeadsCollection = myFirestore.collection('raw_leads');

    const batchAddFreLeads = myFirestore.batch();

    const freeLeadsArray: FreeLeadsModel[] = [];
    const bigQueryRows: RawLeadsTable[] = [];
    const rawLeadsDocs: firestore.DocumentReference[] = [];
    const notInserted: { phoneNumber: string; reason: string }[] = [];

    if (Array.isArray(req.body)) {
      for (const body of req.body) {
        const freeLeadsDocName = `${body.organization}-${body.phoneNumber}`;

        const acquiredLeads = await leadsCollection.doc(freeLeadsDocName).get();
        const findInRawLeads = await rawLeadsCollection
          .where('phoneNumber', '==', body.phoneNumber)
          .get();
        const findFreeLeads = await freeLeadsCollection
          .doc(freeLeadsDocName)
          .withConverter(FreeLeadsModel.converter)
          .get();

        if (acquiredLeads.exists) {
          const reason = 'Sudah ada di My Leads dan Sudah Diakusisi';
          if (res.locals.isSingle) {
            return res.status(500).send(
              errorResponse({
                type: 'UNPROCESSABLE',
                messages: reason,
              })
            );
          }
          notInserted.push({
            phoneNumber: body.phoneNumber,
            reason: reason,
          });
          continue;
        } else if (findFreeLeads.exists) {
          const reason = 'Sudah ada di Free Leads';
          if (res.locals.isSingle) {
            return res.status(500).send(
              errorResponse({
                type: 'UNPROCESSABLE',
                messages: reason,
              })
            );
          }
          notInserted.push({
            phoneNumber: body.phoneNumber,
            reason: reason,
          });
          continue;
        }

        const rawLeadsDoc = rawLeadsCollection.doc();
        const rawLeads: any = {
          externalId: null,
          phoneNumber: body.phoneNumber,
          fullName: `${body.firstName} ${body.lastName}`,
          city: body.cityName,
          vehicleModel: body.vehicleOptions?.[0]?.model.name ?? null,
          vehicleVariant: body.vehicleOptions?.[0]?.variant.name ?? null,
          organization: body.organization,

          externalData: {
            body,
          },
          source: body.source || 'form-free-leads',

          inFreeLeads: findFreeLeads.exists,
          shouldPushToFreeLeadsAt: now.clone().add(10, 'minutes').toDate(),
          freeLeadsPath: findFreeLeads.exists ? findFreeLeads.ref : null,
          freeLeadsCreatedAt: findFreeLeads.exists ? findFreeLeads.get('createdAt') : null,

          shouldPushToBigQueryAt: now.clone().add(10, 'minutes').toDate(),
          donePushToBigQuery: false,
          donePushToBigQueryAt: null,

          createdAt: now.toDate(),

          duplicated: !findInRawLeads.empty,
          mappedData: {
            domicile: {
              provinceName: body.provinceName,
              provinceCode: body.provinceCode,
              cityName: body.cityName,
              cityCode: body.cityCode,
            },
            vehicle: body.vehicleOptions?.[0] ?? null,
            area: body.area || '',
            paymentPlan: body.paymentPlan,
          },
          whatsapp: null,
          alreadyInMyLeads: acquiredLeads.exists,
          codingFile: 'AddMultiFreeLeads.ts',
        };
        batchAddFreLeads.create(rawLeadsDoc, rawLeads);
        rawLeadsDocs.push(rawLeadsDoc);

        const freeLeadsDocRef: firestore.DocumentReference =
          freeLeadsCollection.doc(freeLeadsDocName);

        let price = body.organization === 'amartahonda' ? 20000 : 50000;
        let isCustomPrice = false;

        if (body.price) {
          if (body.price > 50000) {
            price = body.price;
            isCustomPrice = true;
          } else if (body.organization === 'amartahonda' && body.price > 20000) {
            price = body.price;
            isCustomPrice = true;
          }
        }

        const freeLeads = new FreeLeadsModel({
          rawLeadsRef: rawLeadsDoc,
          area: body.area,
          externalId: null,
          title: body.title,
          firstName: body.firstName,
          lastName: body.lastName,
          phoneNumber: body.phoneNumber,
          paymentPlan: body.paymentPlan,
          hasVehicleLoan: body.hasVehicleLoan,
          createdAt: now.toDate(),

          domicile: {
            cityCode: body.cityCode,
            cityName: body.cityName,
            provinceCode: body.provinceCode,
            provinceName: body.provinceName,
          },

          driverLicense_number: body.driverLicense_number,
          idCard_number: body.idCard_number,
          email: body.email,
          nextTotalVehicleOwnerShip: body.nextTotalVehicleOwnerShip,
          organization: body.organization as any,
          purchasePlan: body.purchasePlan,
          source: body.source || 'form-free-leads',
          vehicleOptions: body.vehicleOptions ?? [],
          vehicleUsage: body.vehicleUsage,
          ref: freeLeadsDocRef,

          isAcquired: false,
          acquiredAt: null,
          acquiredAgentCode: null,
          acquiredAgentName: null,
          price,
          isCustomPrice: isCustomPrice,
          notes: body.notes,
          disableWhatsapp: body.disableWhatsapp || false,
          ideal: null,
        });

        batchAddFreLeads.set(freeLeadsDocRef.withConverter(FreeLeadsModel.converter), freeLeads);

        freeLeadsArray.push(freeLeads);

        const uuid = uuidV4();
        const bigQueryRow: RawLeadsTable = {
          city: body.cityName,
          city_group: freeLeads.area,
          duplicated: !findInRawLeads.empty,
          external_id: freeLeads.externalId,
          full_name: `${freeLeads.firstName} ${freeLeads.lastName}`,
          organization: freeLeads.organization,
          phone_number: freeLeads.phoneNumber,
          source: freeLeads.source,

          trimitra_city: freeLeads.domicile?.cityCode
            ? {
                code: freeLeads.domicile?.cityCode,
                name: freeLeads.domicile?.cityName,
              }
            : null,
          trimitra_province: freeLeads.domicile?.provinceCode
            ? {
                code: freeLeads.domicile?.provinceCode,
                name: freeLeads.domicile?.provinceName,
              }
            : null,

          trimitra_vehicle: {
            variant: {
              code: freeLeads.vehicleOptions?.[0]?.variant.code,
              name: freeLeads.vehicleOptions?.[0]?.variant.name,
            },
            model: {
              name: freeLeads.vehicleOptions?.[0]?.model.name,
            },
            color: {
              code: freeLeads.vehicleOptions?.[0]?.color.code,
              name: freeLeads.vehicleOptions?.[0]?.color.name,
            },
          },

          uid: uuid,
          vehicle_model: freeLeads.vehicleOptions?.[0]?.model.name,
          vehicle_variant: freeLeads.vehicleOptions?.[0]?.variant.name,
          whatsapp_delivered: null,
          whatsapp_delivered_at_timestamp: null,
          data_leads_created_at_timestamp: bigQuery.timestamp(now.toDate()),
          bq_created_at_datetime: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
          bq_created_at_timestamp: bigQuery.timestamp(now.toDate()),
          is_in_my_leads: acquiredLeads.exists,
        };

        bigQueryRows.push(bigQueryRow);
      }
    }

    try {
      await batchAddFreLeads.commit();
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi kesalahan pada server',
          data: {
            messages: 'Terjadi kesalahan pada server',
            error: e?.toString(),
          },
        })
      );
    }

    try {
      const batchUpdateStatusBigQuery = myFirestore.batch();
      await rawLeadsTable.insert(bigQueryRows);
      for (const doc of rawLeadsDocs) {
        batchUpdateStatusBigQuery.update(doc, {
          donePushToBigQuery: true,
          donePushToBigQueryAt: now.toDate(),
        });
      }
      await batchUpdateStatusBigQuery.commit();
    } catch (e) {
      console.log('FAILED_TO_INSERT_RAW_LEADS_FORM', JSON.stringify(e), e);
    }

    // for (const freeLeadsModel of freeLeadsArray) {

    //     // Disable Dulu
    //     if (freeLeadsModel.organization === "amartahonda" && !freeLeadsModel.disableWhatsapp) {
    //         await sendTemplate.sendTemplateMetaV2({
    //             projectId: "WLdKug7hau0MbRzKcnqg",
    //             template_name: "leads1",
    //             bindDocuments: [
    //                 {
    //                     path: freeLeadsModel.rawLeadsRef?.path || "",
    //                     context: "add_raw_leads",
    //                 },
    //                 {
    //                     path: freeLeadsModel.ref.path,
    //                     context: "add_leads",
    //                 }
    //             ],
    //             components: [
    //                 {
    //                     "type": "body",
    //                     "parameters": [
    //                         {
    //                             "type": "text",
    //                             "text": `${freeLeadsModel.firstName}`,
    //                         },
    //                         {
    //                             "type": "text",
    //                             "text": freeLeadsModel.source === "form-free-leads" ? "amartahonda.com" : freeLeadsModel.source,
    //                         },
    //                         {
    //                             "type": "text",
    //                             "text": freeLeadsModel.vehicleOptions[0].model.name,
    //                         }
    //                     ]
    //                 }
    //             ],
    //             contactName: freeLeadsModel.firstName,
    //             target: freeLeadsModel.phoneNumber,
    //             area: freeLeadsModel.area || undefined,
    //             vehicle: {
    //                 model_name: freeLeadsModel.vehicleOptions[0].model.name || "",
    //                 variant_name: freeLeadsModel.vehicleOptions[0].variant.name || "",
    //                 variant_code: freeLeadsModel.vehicleOptions[0].variant.code || "",
    //                 color_code: freeLeadsModel.vehicleOptions[0].color?.code || "",
    //                 color_name: freeLeadsModel.vehicleOptions[0].color?.name || "",
    //                 year: moment().year().toString(),
    //             }

    //         })
    //             .then()
    //             .catch(
    //                 reason => console.log(reason)
    //             )
    //     }

    // }

    res.send(
      successResponse({
        data: {
          totalCreated: freeLeadsArray.length,
          failed: notInserted,
        },
        type: 'CREATED',
      })
    );
  },
};

export default addFreeLeadHandler;
