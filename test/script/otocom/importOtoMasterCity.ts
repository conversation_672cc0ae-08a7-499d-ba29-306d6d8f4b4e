import cities from './mapOtocomCityToTrimitra.json';
import { myFirestore } from '../../../src/services/firebaseAdmin';

async function importOtoMasterVehicle() {
  const collection = myFirestore.collection('master').doc('otocom').collection('cities');

  const batch = myFirestore.batch();

  for (const city of cities) {
    const docRef = collection.doc();
    batch.set(docRef, { ...city });
  }

  try {
    await batch.commit();
    return;
  } catch (e) {
    console.log(e);
    return;
  }
}

importOtoMasterVehicle().then().catch();
