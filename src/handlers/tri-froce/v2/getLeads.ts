import { Request, Response } from 'express';
import { param } from 'express-validator';
import triForceAuthChecker from '../../../middlewares/triforceAuth<PERSON>hecker';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import requestValidator from '../../../middlewares/requestValidator';

const getLeads = {
  middlewares: [
    triForce<PERSON>uth<PERSON><PERSON><PERSON>,
    param('organization').notEmpty(),
    param('phoneNumber').notEmpty(),
    requestValidator,
  ],
  handler: async function (
    req: Request<{ organization: string; phoneNumber: string }>,
    res: Response
  ) {
    const collections = myFirestore.collection('leads');
    const get = await collections
      .doc(`${req.params.organization}-${req.params.phoneNumber}`)
      .withConverter(LeadsModel.converter)
      .get();

    if (get.exists) {
      const data = get.data()!;
      return res.send(
        successResponse({
          type: 'FETCHED',
          data: data.toJsonResponse(),
        })
      );
    } else {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Tidak ada Leads untuk nomor telepon ini',
        })
      );
    }
  },
};

export default getLeads;
