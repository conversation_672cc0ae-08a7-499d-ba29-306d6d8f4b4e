import { FeedBackTypes, LeadsVehicleOptions } from '../../firestore/leads_model.types';
import { BigQueryTimestamp } from '@google-cloud/bigquery';

export interface LeadsFeedbackTableTypes {
  uuid: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  domicile: {
    provinceName: string;
    provinceCode: string;
    cityName: string;
    cityCode: string;
  };
  vehicles: LeadsVehicleOptions[];
  feedback: {
    text: FeedBackTypes | null;
    voice: FeedBackTypes | null;
  };
  agentName: string;
  agentCode: string;
  source: string;
  organization: 'amartahonda' | 'amartachery' | 'amartaneta' | 'amartavinfast';
  leadsCreatedAt: BigQueryTimestamp;
  createdAt: BigQueryTimestamp;
}
