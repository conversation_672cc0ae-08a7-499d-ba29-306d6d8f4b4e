import { NextFunction, Request, Response } from 'express';
import errorResponse from '../schema/errorResponse';

const username = 'leadsClientApp';
const password = 'pDy<A2T.s(f3`6HZ';

export default function leadsAuthorizer(req: Request, res: Response, next: NextFunction) {
  let basicAuth = req.header('Authorization');

  try {
    const getEncodedFromHead = basicAuth?.replace('Basic ', '');

    if (!getEncodedFromHead) throw new Error('Unauthenticated');
    const decodedUsernamePassword = Buffer.from(getEncodedFromHead, 'base64').toString('ascii');

    const [decodedUsername, decodedPassword] = decodedUsernamePassword.split(':');

    if (decodedUsername !== username) throw new Error('NOT_AUTHENTICATED');
    if (decodedPassword !== password) throw new Error('NOT_AUTHENTICATED');

    next();
  } catch (e: any) {
    res.status(422).send(
      errorResponse({
        type: 'NOT_AUTHENTICATED',
        messages: 'Tidak terautentikasi',
      })
    );
  }
}
