import { myFirestore } from '../../../src/services/firebaseAdmin';

const deleteActiveConvFlow = async () => {
  const collection = myFirestore.collection('/projects/WLdKug7hau0MbRzKcnqg/chat_rooms');
  const get = await collection
    .where('wait_for_answer.question_id', '==', '2c6f2c30-19e3-4cb0-819c-e9924d0894c1')
    .get();

  const batch = myFirestore.batch();

  get.forEach(result => {
    const data = result.data()!;

    if (data.recent_chat.direction === 'OUT') {
      batch.update(result.ref, {
        wait_for_answer: null,
      });
    }
  });

  await batch.commit();
};

deleteActiveConvFlow();
