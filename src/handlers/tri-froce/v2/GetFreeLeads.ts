import { Request<PERSON><PERSON><PERSON> } from 'express';
import triForceAuthChecker from '../../../middlewares/triforceAuth<PERSON>hecker';
import { myFirestore } from '../../../services/firebaseAdmin';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import successResponse from '../../../schema/successResponse';
import { query } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';

interface IParams {
  organization: string;
}

interface IQueries {
  page: number;
  size: number;
  cityGroup: string;
}

const getFreeLeads: {
  middlewares: RequestHandler[];
  handler: RequestHandler<IParams, any, any, IQueries>;
} = {
  middlewares: [
    triForceAuth<PERSON>hecker,
    query('page').optional().toInt(),
    query('size').optional().toInt(),
    query('cityGroup').trim().toUpperCase().optional(),
    requestValidator,
  ],
  handler: async function (req, res) {
    const collection = myFirestore.collection('free_leads');

    let totalPage = 0;

    let query = collection
      .where('organization', '==', req.params.organization)
      .where('isAcquired', '==', false);
    if (req.query.cityGroup) {
      query = query.where('area', '==', req.query.cityGroup);
    }

    const getCount = await query.count().get();
    const totalAllFreeLeads = getCount.data().count;

    totalPage = Math.ceil(totalAllFreeLeads / req.query.size);

    let startAt = 0;

    if (req.query.page > 1) {
      startAt = req.query.size * (req.query.page - 1);
    }

    let getQuery = query.orderBy('createdAt', 'desc');

    // Pagination
    if (req.query.page && req.query.size) {
      getQuery = getQuery.offset(startAt).limit(req.query.size);
    }
    const get = await getQuery.withConverter(FreeLeadsModel.converter).get();

    const freeLeads: FreeLeadsModel[] = [];

    get.forEach(result => {
      const data = result.data();
      freeLeads.push(data);
    });

    res.send(
      successResponse({
        meta: {
          totalLeadsCurrentPage: freeLeads.length,
          totalLeadsAllPages: totalPage,
          totalAvailablePages: totalAllFreeLeads,
        },
        data: freeLeads.map(value => value.toJsonResponse()),
      })
    );
  },
};

export default getFreeLeads;
