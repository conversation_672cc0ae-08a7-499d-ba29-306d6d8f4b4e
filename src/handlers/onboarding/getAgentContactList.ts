import { Request<PERSON>and<PERSON> } from 'express';
import { myFirestore } from '../../services/firebaseAdmin';
import requestValidator from '../../middlewares/requestValidator';
import { param, query } from 'express-validator';
import { IAgentContactTypes } from '../../types/onboarding/agentContact.types';
import { firestore } from 'firebase-admin';
import successResponse from '../../schema/successResponse';

const getAgentContactList: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [param('agentPhoneNumber', requestValidator), requestValidator],
  handler: async (req, res) => {
    const collection = myFirestore.collection('agent_contacts');
    const get = await collection.where('agentPhoneNumber', '==', req.params.agentPhoneNumber).get();

    const contacts: IAgentContactTypes<firestore.Timestamp>[] = [];

    get.forEach(result => {
      const data = result.data()! as any;
      contacts.push(data);
    });

    res.send(
      successResponse({
        type: 'FETCHED',
        data: contacts.map(value => {
          return {
            ...value,
            createdAt: value.createdAt.toDate(),
            docRef: value.docRef.path,
          };
        }),
      })
    );
  },
};

export default getAgentContactList;
