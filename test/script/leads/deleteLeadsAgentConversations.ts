import { myFirestore } from '../../../src/services/firebaseAdmin';

const deleteLeadsAgentConversations = async () => {
  const collection = myFirestore.collection('leads_agent_conversations');
  const get = await collection.where('agentCode', '==', '999999').get();

  const batch = myFirestore.batch();
  get.forEach(result => {
    // console.log(result.get("agentCode"));
    batch.delete(result.ref);
  });
  await batch.commit();

  console.log(get.size);
};

deleteLeadsAgentConversations();
