import { HandlerTypes } from '../../types/handler.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { json } from 'express';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import FreeLeadsModel from '../../model/FreeLeadsModel';
import moment from 'moment';
import successResponse from '../../schema/successResponse';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';

interface ReqBody {
  leadsPhoneNumber: string;
  organization: string;
  agentCode: string;
  movedBy: string;
}

const moveLeadsToFreeLeads: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    json(),
    body('leadsPhoneNumber').trim().notEmpty(),
    body('organization').trim().notEmpty(),
    body('agentCode').trim().notEmpty(),
    body('movedBy').optional({ values: 'falsy' }).trim(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const docName = `${req.body.organization}-${req.body.leadsPhoneNumber}`;
    const leadsDocRef = myFirestore.collection('leads').doc(docName);
    const getLeads = await leadsDocRef.withConverter(LeadsModel.converter).get();

    const freeLeadsCollection = myFirestore.collection('free_leads');
    const freeLeadsDocRef = freeLeadsCollection.doc(docName);

    if (!getLeads.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leadsData = getLeads.data()!;

    if (leadsData.agentCode !== req.body.agentCode) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const batch = myFirestore.batch();
    const now = moment();

    const getFreeLeads = await freeLeadsDocRef.withConverter(FreeLeadsModel.converter).get();

    let freeLeads!: FreeLeadsModel;
    if (getFreeLeads.exists) {
      freeLeads = getFreeLeads.data()!;
      freeLeads.acquiredAt = null;
      freeLeads.acquiredAgentName = null;
      freeLeads.acquiredAgentCode = null;
      freeLeads.isAcquired = false;
      freeLeads.source = 'triforce';
      freeLeads.createdBy = req.body.movedBy || '';
      freeLeads.notes = 'Leads yang diakuisisi dijadikan Free Leads';
    } else {
      const price = leadsData.organization === 'amartahonda' ? 20000 : 50000;
      freeLeads = new FreeLeadsModel({
        rawLeadsRef: null,
        area: leadsData.area,
        externalId: null,
        title: leadsData.title,
        firstName: leadsData.firstName,
        lastName: leadsData.lastName,
        phoneNumber: leadsData.phoneNumber,
        paymentPlan: leadsData.paymentPlan,
        hasVehicleLoan: leadsData.hasVehicleLoan,
        createdAt: now.toDate(),

        domicile: leadsData.domicile,

        driverLicense_number: leadsData.driverLicense_number,
        email: leadsData.email,
        idCard_number: leadsData.idCard_number,
        nextTotalVehicleOwnerShip: leadsData.nextTotalVehicleOwnerShip,
        organization: leadsData.organization,
        purchasePlan: leadsData.purchasePlan,
        source: 'triforce',
        vehicleOptions: leadsData.vehicleOptions,
        vehicleUsage: leadsData.vehicleUsage,
        ref: freeLeadsDocRef,

        isAcquired: false,
        acquiredAt: null,
        acquiredAgentCode: null,
        acquiredAgentName: null,
        price,
        notes: 'Leads yang diakuisisi dijadikan Free Leads',
        createdBy: req.body.movedBy || '',
        ideal: null,
      });
    }

    batch.set(freeLeadsDocRef.withConverter(FreeLeadsModel.converter), freeLeads);
    batch.delete(leadsDocRef);

    try {
      await batch.commit();
      await aggregatesAgent(leadsData.agentCode, freeLeads.organization)
    } catch {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi error',
        })
      );
    }

    res.send(
      successResponse({
        data: null,
        type: 'CREATED',
      })
    );
  },
};

export default moveLeadsToFreeLeads;
