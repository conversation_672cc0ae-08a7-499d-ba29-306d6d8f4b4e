import { RequestHand<PERSON> } from 'express';
import { query } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import AdminModel from '../../../model/AdminModel';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';

interface ReqQuery {
  email: string;
  createSource: string;
}

const getAdminHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, any, ReqQuery>;
} = {
  middlewares: [
    query('email').notEmpty().isEmail().trim(),
    query('createSource').notEmpty().trim(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const adminCollection = myFirestore.collection('admins');

    const getAdmin = await adminCollection
      .where('email', '==', req.query.email)
      .where('createdSource', '==', req.query.createSource)
      .limit(1)
      .withConverter(AdminModel.converter)
      .get();

    if (getAdmin.size === 0) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Admin tidak ditemukan',
        })
      );
    }

    let admin!: AdminModel;
    getAdmin.forEach(r => {
      admin = r.data();
    });

    return res.send(
      successResponse({
        type: 'FETCHED',
        data: admin.toJsonResponse(),
      })
    );
  },
};

export default getAdminHandler;
