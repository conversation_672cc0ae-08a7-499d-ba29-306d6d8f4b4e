import axios from 'axios';
import { ResponseGetShipmentCost } from '../types/services/logisticService.types';

class LogisticService {
  private base = axios.create({
    baseURL: 'https://v0b1z99035.execute-api.ap-southeast-1.amazonaws.com/v1',
    headers: {
      'x-api-key': 'rXTFzVZPH45GwqKvybxJg98cOQ0deR7z3vlIo5Aw',
    },
  });

  async getShippingCost(params: { dealer_code: string; city_code: string }) {
    return await this.base.get<{ data: ResponseGetShipmentCost }>('/config/shipping-cost', {
      params: {
        ...params,
      },
    });
  }
}

const logisticService = new LogisticService();

export default logisticService;
