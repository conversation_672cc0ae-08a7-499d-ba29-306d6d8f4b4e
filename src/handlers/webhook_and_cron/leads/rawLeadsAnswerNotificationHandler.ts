import moment, { Moment } from 'moment/moment';
import { json, RequestHandler } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import { RawLeads } from '../../../types/firestore/raw_leads_types';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import { firestore } from 'firebase-admin';
import successResponse from '../../../schema/successResponse';

interface ReqBody {
  messageId: string;
  createdAt: Moment;
  text: string;
  path: string;
}

const rawLeadsNotificationHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    json(),
    body('path').notEmpty(),
    body('messageId').notEmpty(),
    body('text').notEmpty().trim(),
    body('createdAt')
      .notEmpty()
      .customSanitizer(input => moment(input)),
    requestValidator,
  ],
  handler: async (req, res) => {
    const freeLeadsCollection = myFirestore.collection('free_leads');

    const rawLeadsDoc = myFirestore.doc(req.body.path);
    const getDocRawLeads = await rawLeadsDoc.get();

    const now = moment();

    const dataRawLeads = getDocRawLeads.data() as RawLeads;

    const batchAddFreLeads = myFirestore.batch();
    if (req.body.text.toUpperCase() === 'HUBUNGI MARKETING') {
      const price = dataRawLeads.organization === 'amartahonda' ? 20000 : 50000;

      if (dataRawLeads.organization === 'amartachery') {
        const freeLeadsDocName = `${dataRawLeads.organization}-${dataRawLeads.phoneNumber}`;
        let freeLeadsDocRef!: firestore.DocumentReference;
        const find = await freeLeadsCollection
          .doc(freeLeadsDocName)
          .withConverter(FreeLeadsModel.converter)
          .get();

        if (!find.exists) {
          freeLeadsDocRef = find.ref;
        } else {
          freeLeadsDocRef = freeLeadsCollection.doc(freeLeadsDocName);
        }

        try {
          const freeLeads = new FreeLeadsModel({
            rawLeadsRef: rawLeadsDoc,
            area: dataRawLeads.mappedData?.area ?? null,
            externalId: dataRawLeads.externalId ?? null,
            title: null,
            firstName: dataRawLeads.fullName,
            lastName: '',
            phoneNumber: dataRawLeads.phoneNumber,
            paymentPlan: null,
            hasVehicleLoan: false,
            createdAt: now.toDate(),

            domicile: dataRawLeads.mappedData?.domicile ?? null,

            driverLicense_number: null,
            email: null,
            idCard_number: null,
            nextTotalVehicleOwnerShip: null,
            organization: dataRawLeads.organization,
            purchasePlan: null,
            source: 'otocom',
            vehicleOptions: dataRawLeads.mappedData?.vehicle
              ? [dataRawLeads.mappedData.vehicle]
              : [],
            vehicleUsage: null,
            ref: freeLeadsDocRef,

            isAcquired: false,
            acquiredAt: null,
            acquiredAgentCode: null,
            acquiredAgentName: null,

            whatsapp: dataRawLeads.whatsapp as any,

            price,
            ideal: null,
          });

          batchAddFreLeads.set(freeLeadsDocRef.withConverter(FreeLeadsModel.converter), freeLeads);
          batchAddFreLeads.update(rawLeadsDoc, {
            inFreeLeads: true,
            freeLeadsPath: freeLeadsDocRef,
            freeLeadsCreatedAt: freeLeads.createdAt,
          });
        } catch {
          console.log('FAILED_ADD_FREE_LEADS_FROM_RAW_LEADS', dataRawLeads.phoneNumber);
        }
      }
    } else if (req.body.text.toUpperCase() === 'BELI SECARA TUNAI') {
      batchAddFreLeads.update(rawLeadsDoc, {
        'mappedData.paymentPlan': 'cash',
      });
    } else if (req.body.text.toUpperCase() === 'LIHAT PAKET KREDIT') {
      batchAddFreLeads.update(rawLeadsDoc, {
        'mappedData.paymentPlan': 'credit',
      });
    }

    try {
      await batchAddFreLeads.commit();
    } catch {
      console.log('FAILED_ADD_FREE_LEADS_FROM_RAW_LEADS', dataRawLeads.phoneNumber);
    }

    res.send(
      successResponse({
        data: null,
      })
    );
  },
};

export default rawLeadsNotificationHandler;
