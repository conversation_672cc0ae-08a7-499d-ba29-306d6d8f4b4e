import { firestore } from 'firebase-admin';
import { LeadsOrganization } from './leads_model.types';

export default interface IFreeLeadsDocument<
  DATE = firestore.Timestamp,
  DocRef = firestore.DocumentReference,
> {
  area: string | null;
  title: string | null;
  firstName: string;
  lastName: string | null;
  phoneNumber: string;
  email: string | null;
  domicile: {
    provinceName: string;
    provinceCode: string;
    cityName: string;
    cityCode: string;
  } | null;
  vehicleUsage: 'individual' | 'shared' | 'corporate' | null;
  paymentPlan: 'cash' | 'credit' | null;
  hasVehicleLoan: boolean;
  vehicleOptions: {
    brand: {
      name: string;
    };
    model: {
      name: string;
    };
    variant: {
      code: string;
      name: string;
    };
    color: {
      code: string;
      name: string;
    };
  }[];
  source: string;
  organization: LeadsOrganization;
  createdAt: DATE;
  createdBy: string;

  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition' | null;
  nextTotalVehicleOwnerShip: string | null;

  idCard_number: string | null;
  driverLicense_number: string | null;

  externalId: string | null;

  isAcquired: boolean;
  acquiredAt: DATE | null;
  acquiredAgentCode: string | null;
  acquiredAgentName: string | null;
  price: number;
  isCustomPrice: boolean;

  notes: string;

  whatsapp?: {
    idealPath: DocRef | null;
    messageId: string;
    statuses: {
      delivered: DATE | null;
      read: DATE | null;
    } | null;
    response?: {
      text: string;
      repliedAt: DATE;
    } | null;
  } | null;

  disableWhatsapp: boolean;

  rawLeadsRef: DocRef | null;

  ideal: {
    chat_room_ref: DocRef | null;
  } | null;

  transactionId: string | null;
  rating: {
    average: number;
    total: number;
  }
}
