[{"otoComId": "53", "otoComSlug": "<PERSON><PERSON>i", "otoComName": "<PERSON><PERSON><PERSON>", "trimitraCityGroup": "BEKASI", "trimitraCityCode": "32.75", "trimitraCityName": "<PERSON><PERSON>i", "trimitraProvinceName": "jawa barat", "trimitraProvinceCode": "32"}, {"otoComId": "109", "otoComSlug": "depok", "otoComName": "De<PERSON>k", "trimitraCityGroup": "DEPOK", "trimitraCityCode": "32.76", "trimitraCityName": "depok", "trimitraProvinceName": "jawa barat", "trimitraProvinceCode": "32"}, {"otoComId": "144", "otoComSlug": "jakarta-barat", "otoComName": "Jakarta Barat", "trimitraCityGroup": "JAKARTA", "trimitraCityCode": "31.73", "trimitraCityName": "jakarta barat", "trimitraProvinceName": "dki jakarta", "trimitraProvinceCode": "31"}, {"otoComId": "145", "otoComSlug": "jakarta-pusat", "otoComName": "Jakarta Pusat", "trimitraCityGroup": "JAKARTA", "trimitraCityCode": "31.71", "trimitraCityName": "jakarta pusat", "trimitraProvinceName": "dki jakarta", "trimitraProvinceCode": "31"}, {"otoComId": "146", "otoComSlug": "jakarta-selatan", "otoComName": "Jakarta Selatan", "trimitraCityGroup": "JAKARTA", "trimitraCityCode": "31.74", "trimitraCityName": "jakarta selatan", "trimitraProvinceName": "dki jakarta", "trimitraProvinceCode": "31"}, {"otoComId": "147", "otoComSlug": "jakarta-timur", "otoComName": "Jakarta Timur", "trimitraCityGroup": "JAKARTA", "trimitraCityCode": "31.75", "trimitraCityName": "jakarta timur", "trimitraProvinceName": "dki jakarta", "trimitraProvinceCode": "31"}, {"otoComId": "148", "otoComSlug": "jakarta-utara", "otoComName": "Jakarta Utara", "trimitraCityGroup": "JAKARTA", "trimitraCityCode": "31.72", "trimitraCityName": "jakarta utara", "trimitraProvinceName": "dki jakarta", "trimitraProvinceCode": "31"}, {"otoComId": "163", "otoComSlug": "karawang", "otoComName": "<PERSON><PERSON>", "trimitraCityGroup": "KARAWANG", "trimitraCityCode": "32.15", "trimitraCityName": "karawang", "trimitraProvinceName": "jawa barat", "trimitraProvinceCode": "32"}, {"otoComId": "180", "otoComSlug": "kepulauan-seribu", "otoComName": "<PERSON><PERSON><PERSON><PERSON>", "trimitraCityGroup": "JAKARTA", "trimitraCityCode": "31.01", "trimitraCityName": "kep seribu", "trimitraProvinceName": "dki jakarta", "trimitraProvinceCode": "31"}, {"otoComId": "432", "otoComSlug": "tangerang", "otoComName": "Tangerang", "trimitraCityGroup": "TANGERANG", "trimitraCityCode": "36.71", "trimitraCityName": "tangerang", "trimitraProvinceName": "banten", "trimitraProvinceCode": "36"}, {"otoComId": "433", "otoComSlug": "tangerang-selatan", "otoComName": "Tangerang Selatan", "trimitraCityGroup": "TANGERANG", "trimitraCityCode": "36.74", "trimitraCityName": "tangerang selatan", "trimitraProvinceName": "banten", "trimitraProvinceCode": "36"}, {"otoComId": "502", "otoComSlug": "cibitung", "otoComName": "Cibitung", "trimitraCityGroup": "BEKASI", "trimitraCityCode": "32.02", "trimitraCityName": "<PERSON><PERSON><PERSON><PERSON>", "trimitraProvinceName": "jawa barat", "trimitraProvinceCode": "32"}, {"otoComId": "503", "otoComSlug": "tambun", "otoComName": "Tambun", "trimitraCityGroup": "BEKASI", "trimitraCityCode": "62.07", "trimitraCityName": "<PERSON><PERSON><PERSON>", "trimitraProvinceName": "kalimantan tengah", "trimitraProvinceCode": "62"}, {"otoComId": "504", "otoComSlug": "cikarang", "otoComName": "<PERSON><PERSON><PERSON>", "trimitraCityGroup": "BEKASI", "trimitraCityCode": "32.16", "trimitraCityName": "<PERSON><PERSON>i", "trimitraProvinceName": "jawa barat", "trimitraProvinceCode": "32"}, {"otoComId": "506", "otoComSlug": "<PERSON><PERSON><PERSON><PERSON>", "otoComName": "Karawaci", "trimitraCityGroup": "TANGERANG", "trimitraCityCode": "36.71", "trimitraCityName": "tangerang", "trimitraProvinceName": "banten", "trimitraProvinceCode": "36"}, {"otoComId": "507", "otoComSlug": "alam-sutera", "otoComName": "<PERSON><PERSON>", "trimitraCityGroup": "TANGERANG", "trimitraCityCode": "36.74", "trimitraCityName": "tangerang selatan", "trimitraProvinceName": "banten", "trimitraProvinceCode": "36"}, {"otoComId": "508", "otoComSlug": "serpong", "otoComName": "Ser<PERSON>ng", "trimitraCityGroup": "TANGERANG", "trimitraCityCode": "36.74", "trimitraCityName": "tangerang selatan", "trimitraProvinceName": "banten", "trimitraProvinceCode": "36"}, {"otoComId": "511", "otoComSlug": "cib<PERSON>ur", "otoComName": "Cibubur", "trimitraCityGroup": "JAKARTA", "trimitraCityCode": "31.75", "trimitraCityName": "jakarta timur", "trimitraProvinceName": "dki jakarta", "trimitraProvinceCode": "31"}, {"otoComId": "512", "otoComSlug": "<PERSON><PERSON>o", "otoComName": "Bintaro", "trimitraCityGroup": "JAKARTA", "trimitraCityCode": "31.74", "trimitraCityName": "jakarta selatan", "trimitraProvinceName": "dki jakarta", "trimitraProvinceCode": "31"}, {"otoComId": "514", "otoComSlug": "cikampek", "otoComName": "Cikampek", "trimitraCityGroup": "KARAWANG", "trimitraCityCode": "32.15", "trimitraCityName": "karawang", "trimitraProvinceName": "jawa barat", "trimitraProvinceCode": "32"}]