import { Request, Response } from 'express';
import { query } from 'express-validator';
import moment, { Moment } from 'moment';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import requestValidator from '../../middlewares/requestValidator';
import ColdLeadsModel from '../../model/ColdLeadsModel';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';

interface ReqQuery {
  startDate: string;
  endDate: string;
  agentCodes?: string | string[];
  spkNumber?: string;
  page?: string;
  limit?: string;
}

const getColdLeads = {
  middlewares: [
    leadsAuthorizer,
    query('startDate')
      .notEmpty()
      .withMessage('startDate is required')
      .custom(input => {
        const validDate = moment(input, 'YYYY-MM-DD HH:mm', true).isValid();
        if (!validDate) {
          throw new Error('Invalid startDate format. Expected format: YYYY-MM-DD HH:mm');
        }
        return true;
      }),
    query('endDate')
      .notEmpty()
      .withMessage('endDate is required')
      .custom(input => {
        const validDate = moment(input, 'YYYY-MM-DD HH:mm', true).isValid();
        if (!validDate) {
          throw new Error('Invalid endDate format. Expected format: YYYY-MM-DD HH:mm');
        }
        return true;
      }),
    query('agentCodes')
      .optional()
      .custom(input => {
        // Handle both single string and array of strings
        if (typeof input === 'string') {
          return true;
        }
        if (Array.isArray(input)) {
          if (input.length === 0) {
            throw new Error('agentCodes array cannot be empty');
          }
          for (const code of input) {
            if (typeof code !== 'string' || code.trim() === '') {
              throw new Error('All agentCodes must be non-empty strings');
            }
          }
          return true;
        }
        throw new Error('agentCodes must be a string or array of strings');
      }),
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 1000 })
      .withMessage('limit must be a positive integer between 1 and 1000'),
    requestValidator,
  ],
  handler: async function (
    req: Request<object, ResponseSchema, object, ReqQuery>,
    res: Response<ResponseSchema>
  ) {
    try {
      // Parse and validate dates
      const startDate: Moment = moment(req.query.startDate, 'YYYY-MM-DD HH:mm', true);
      const endDate: Moment = moment(req.query.endDate, 'YYYY-MM-DD HH:mm', true);

      // Validate date range
      if (endDate.isBefore(startDate)) {
        return res.status(422).send(
          errorResponse({
            type: 'UNPROCESSABLE_ENTITY',
            messages: 'endDate must be after startDate',
          })
        );
      }

      // Parse agentCodes
      let agentCodes: string[] | undefined;
      if (req.query.agentCodes) {
        if (typeof req.query.agentCodes === 'string') {
          agentCodes = [req.query.agentCodes];
        } else if (Array.isArray(req.query.agentCodes)) {
          agentCodes = req.query.agentCodes;
        }
      }

      // Parse pagination parameters
      const page = req.query.page ? parseInt(req.query.page, 10) : undefined;
      const limit = req.query.limit ? parseInt(req.query.limit, 10) : undefined;

      // Both page and limit must be provided for pagination to work
      const isPaginationEnabled = page !== undefined && limit !== undefined;

      let coldLeads: ColdLeadsModel[] = [];

      if (agentCodes && agentCodes.length > 0) {
        // Handle multiple agent codes with individual queries
        const queryPromises = agentCodes.map(async (agentCode) => {
          const collection = myFirestore.collection('cold_leads');
          let queryBuilder = collection
            .where('moveToColdAt', '>=', startDate.toDate())
            .where('moveToColdAt', '<=', endDate.toDate())
            .where('agentCode', '==', agentCode)
            .orderBy('moveToColdAt', 'desc');

          // Apply spkNumber filter if provided
          if (req.query.spkNumber) {
            queryBuilder = queryBuilder.where('spk.spkNumber', '==', req.query.spkNumber);
          }

          const querySnapshot = await queryBuilder.withConverter(ColdLeadsModel.converter).get();
          const results: ColdLeadsModel[] = [];
          querySnapshot.forEach(doc => {
            results.push(doc.data());
          });
          return results;
        });

        // Execute all queries in parallel
        const allResults = await Promise.all(queryPromises);

        // Flatten and combine all results
        coldLeads = allResults.flat();

        // Sort combined results by moveToColdAt DESC to maintain order
        coldLeads.sort((a, b) => {
          const dateA = a.moveToColdAt instanceof Date ? a.moveToColdAt : new Date(a.moveToColdAt);
          const dateB = b.moveToColdAt instanceof Date ? b.moveToColdAt : new Date(b.moveToColdAt);
          return dateB.getTime() - dateA.getTime();
        });
      } else {
        // Single query when no agent codes specified
        const collection = myFirestore.collection('cold_leads');
        let queryBuilder = collection
          .where('moveToColdAt', '>=', startDate.toDate())
          .where('moveToColdAt', '<=', endDate.toDate())
          .orderBy('moveToColdAt', 'desc');

        // Apply spkNumber filter if provided
        if (req.query.spkNumber) {
          queryBuilder = queryBuilder.where('spk.spkNumber', '==', req.query.spkNumber);
        }

        const querySnapshot = await queryBuilder.withConverter(ColdLeadsModel.converter).get();
        querySnapshot.forEach(doc => {
          coldLeads.push(doc.data());
        });
      }

      // Apply pagination if both page and limit are provided
      let paginatedLeads = coldLeads;
      let totalCount = coldLeads.length;

      if (isPaginationEnabled && page && limit) {
        const offset = (page - 1) * limit;
        paginatedLeads = coldLeads.slice(offset, offset + limit);
      }

      // Return response
      res.send(
        successResponse({
          type: 'FETCHED',
          data: coldLeads.map(lead => lead.toJsonResponse()),
          meta: {
            total: coldLeads.length,
            start_date: req.query.start_date,
            end_date: req.query.end_date,
            agent_codes: agentCodes || null,
          },
        })
      );
    } catch (error: any) {
      console.error('Error in GetColdLeads handler:', error);
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Failed to retrieve cold leads',
          data: error.message,
        })
      );
    }
  },
};

export default getColdLeads;
