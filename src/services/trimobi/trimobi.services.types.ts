interface IVehicleCondition {
  year: string;
  mileage: string;
}

interface IVehicleCertificate {
  year: string;
  license_plate: string;
  ownership: number;
  ownership_certificate: boolean;
  registration_certificate: boolean;
  active_certificate: boolean;
  owner_as_certificate: boolean;
}

interface IVehicleData {
  class: string;
  brand_uuid: string;
  brand_name: string;
  model_uuid: string;
  model_name: string;
  model_other_name: string;
  variant_uuid: string;
  variant_code: string;
  variant_name: string;
  color_code: string;
  color_name: string;
  image_url: string;
  fuel: string;
  seat: string;
  transmission: string;
  engine_capacity: string | number;
}

interface IIdCard {
  birth_date: string;
  birth_place: string;
  full_address: string;
  full_name: string;
  id_card_image: string;
  id_card_number: string;
  marital_status: string;
  occupation_code: string;
  occupation: string;
  sex: string;
}

interface IAddress {
  full_address: string;
  province_name: string;
  province_code: string;
  city_name: string;
  city_code: string;
  district_name: string;
  district_code: string;
  sub_district_name: string;
  sub_district_code: string;
  zip_code: string;
}

interface IVehicleImage {
  image_url: string;
  caption: string;
}

export interface ICreateAdRequest {
  plan_code: string;
  company: string;
  source: string;
  category: string;
  caption: string;
  description: string;
  price: number;
  area: string;
  city_group: string;
  name: string;
  phone_number: string;
  listing_option: string;
  permitted_make_offer: boolean;
  vehicle_condition: IVehicleCondition;
  vehicle_certificate: IVehicleCertificate;
  vehicle_data: IVehicleData;
  id_card: IIdCard;
  address: IAddress;
  vehicle_address: IAddress;
  vehicle_images: IVehicleImage[];
}
