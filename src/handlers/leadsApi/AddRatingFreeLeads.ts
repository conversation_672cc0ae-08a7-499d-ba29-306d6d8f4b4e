import { HandlerTypes } from '../../types/handler.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { json } from 'express';
import { body, query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import FreeLeadsRating from '../../model/FreeLeadsRating';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { firestore } from 'firebase-admin';
import { decrypt } from '../../helpers/encryption';
import FreeLeadsModel from '../../model/FreeLeadsModel';

interface ReqBody {
  phoneNumber: string;
  organization: string;
  agentCode: string;
  agentName: string;
  rating: number;
}

interface ReqQuery {
  encrypted: boolean;
}

const AddRatingFreeLeads: HandlerTypes<any, ResponseSchema, ReqBody, ReqQuery> = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('phoneNumber').notEmpty().trim(),
    body('organization').notEmpty().trim(),
    body('agentCode').notEmpty().trim(),
    body('agentName').notEmpty().trim(),
    body('rating').isInt({ min: 1, max: 5 }).toInt(),
    query('encrypted').optional().isBoolean().toBoolean(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const { organization, agentCode, agentName, rating } = req.body;

    let phoneNumber = req.body.phoneNumber;
    if (req.query.encrypted) {
      phoneNumber = decrypt(phoneNumber);
    }

    const docName = `${organization}-${phoneNumber}`;
    const docRefFreeLeads = myFirestore.collection('free_leads').doc(docName);

    // Check if the free lead exists
    const getFreeLeadsQuery = await docRefFreeLeads.withConverter(FreeLeadsModel.converter).get();

    if (!getFreeLeadsQuery.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Free Leads tidak ditemukan',
        })
      );
    }

    // Create a new rating document
    const ratingRef = docRefFreeLeads
      .collection('ratings')
      .withConverter(FreeLeadsRating.converter)
      .doc();

    try {
      const ratingData = new FreeLeadsRating({
        REF: ratingRef,
        agentName,
        agentCode,
        organization,
        rating: rating,
        createdAt: firestore.Timestamp.now(),
      });

      await ratingRef.set(ratingData, { merge: true });

    } catch (error) {
      console.error('Error adding rating to free lead:', error);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Failed to add rating',
          data: {
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        })
      );
    }

    // Calculate average rating
    try {
      const ratings = await docRefFreeLeads.collection('ratings').get();
      const totalRatings = ratings.size;
      const totalScore = ratings.docs.reduce((acc, doc) => acc + doc.data().rating, 0);
      const averageRating = totalScore / totalRatings;

      await docRefFreeLeads.update({
        rating: {
          average: averageRating,
          total: totalRatings,
        },
      });
    } catch (e) {
      console.error('Error updating average rating:', e);
    }

    res.send(
      successResponse({
        data: {
          ratingId: ratingRef.id,
          rating,
          phoneNumber: req.body.phoneNumber,
          organization: req.body.organization,
          agentCode: req.body.agentCode,
          agentName: req.body.agentName,
        },
        type: 'CREATED',
      })
    );
  },
};

export default AddRatingFreeLeads;
