import { HandlerTypes } from '../../types/handler.types';
import ClientModel from '../../model/ClientModel';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import errorResponse from '../../schema/errorResponse';
import { firestore } from 'firebase-admin';
import { v4 } from 'uuid';
import moment from 'moment';
import successResponse from '../../schema/successResponse';
import { json } from 'express';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';

interface ReqBody {
  _clientPath: string;

  brandUuid: string;
  brandName: string;
  modelUuid: string;
  modelName: string;
  modelCategory: string;
  variantFreeText: string;
  mileage: string;
  year: string;
  licensePlateCode: string;
  licensePlateNumber: string;
  licensePlateSeries: string;
  licensePlateCodeDetails: any;
}

const addNewOwnedVehicle: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    json(),
    body('brandUuid').notEmpty(),
    body('brandName').notEmpty(),
    body('modelUuid').notEmpty(),
    body('modelName').notEmpty(),
    body('modelCategory').notEmpty().toUpperCase(),
    body('variantFreeText').notEmpty().trim().toUpperCase(),
    body('licensePlateCode').notEmpty().trim(),
    body('licensePlateNumber').notEmpty().trim().isNumeric(),
    body('licensePlateSeries')
      .notEmpty()
      .trim()
      .toUpperCase()
      .isLength({ max: 3, min: 1 })
      .isAlpha(),
    body('licensePlateCodeDetails').notEmpty(),
    body('mileage').notEmpty().trim().isNumeric().isInt(),
    body('year').notEmpty().trim().isNumeric().isInt(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const clientRef = myFirestore.doc(req.body._clientPath);
    const getClient = await clientRef.withConverter(ClientModel.converter).get();

    const data = getClient.data();

    if (!data)
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          data: {
            messages: 'Customer tidak ditemukan',
          },
        })
      );

    const uid = v4();
    const now = moment();

    await clientRef.update({
      owned_vehicle: firestore.FieldValue.arrayUnion({
        id: uid,
        brand_name: req.body.brandName,
        brand_uuid: req.body.brandUuid,
        model_name: req.body.modelName,
        model_uuid: req.body.modelUuid,
        model_category: req.body.modelCategory,
        variant_free_text: req.body.variantFreeText,
        year: req.body.year,
        license_plate: `${req.body.licensePlateCode} ${req.body.licensePlateNumber} ${req.body.licensePlateSeries}`,
        license_plate_code: req.body.licensePlateCode,
        license_plate_number: req.body.licensePlateNumber,
        license_plate_series: req.body.licensePlateSeries,
        license_plate_code_details: req.body.licensePlateCodeDetails,
        mileage: req.body.mileage,
        createdAt: now.toDate(),
      }),
    });

    res.send(
      successResponse({
        type: 'CREATED',
        data: {
          messages: 'Kendaraan berhasil ditambahkan',
        },
      })
    );
  },
};

export default addNewOwnedVehicle;
