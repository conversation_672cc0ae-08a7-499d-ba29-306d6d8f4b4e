import { Router } from 'express';
import SendMessageHandler from '../handlers/SendMessageHandler';
import GetImageHandler from '../handlers/media/GetImageHandler';
import initMessageWithTemplate from '../handlers/initMessageWithTemplate';
import createPriceListFileHandler from '../handlers/createPriceListFileHandler';
import GetMediaHandler from '../handlers/media/GetMediaHandler';
import insertLogPricelistToBqHandler from '../handlers/insertLogPricelistToBqHandler';
import SendMessageInternalTemplateHandler from '../handlers/SendMessageInternalTemplateHandler';
import SaveImageToGoogleBucketHandler from '../handlers/media/saveImageToGoogleBucketHandler';
import sendMessageV2Handler from '../handlers/sendMessage/sendMessage.handler.v2';

const router = Router();

export default function messageRoutes(parent: Router) {
  parent.use('/message', router);
  router.post('/send', SendMessageHandler.middlewares, SendMessageHandler.handler);
  router.post('/send-v2', sendMessageV2Handler.middlewares, sendMessageV2Handler.handler);
  router.post(
    '/send-template-internal',
    SendMessageInternalTemplateHandler.middlewares,
    SendMessageInternalTemplateHandler.handler
  );
  router.post(
    '/send-template-init-message',
    initMessageWithTemplate.middlewares,
    initMessageWithTemplate.handler
  );
  router.get('/image', GetImageHandler.middlewares, GetImageHandler.handler);
  router.get('/media/:decodedFileName', GetMediaHandler.middlewares, GetMediaHandler.handler);
  router.post(
    '/image/save',
    SaveImageToGoogleBucketHandler.middlewares,
    SaveImageToGoogleBucketHandler.handler
  );
  router.post(
    '/generate-price-list',
    createPriceListFileHandler.middlewares,
    createPriceListFileHandler.handler
  );

  router.post(
    '/pricelist-bq-log',
    insertLogPricelistToBqHandler.middlewares,
    insertLogPricelistToBqHandler.handler
  );
}
