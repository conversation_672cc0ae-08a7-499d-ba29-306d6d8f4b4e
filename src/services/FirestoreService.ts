import { v4 } from 'uuid';
import { IFirestoreMessageEntity } from '../types/firestore/messages/message_types';
import { firestore } from 'firebase-admin';
import { IChatRoomModel } from '../types/firestore/i_chat_room_model';

class FirestoreService {
  public async addNewMessage(
    params: IFirestoreMessageEntity,
    chatRoomRef: firestore.DocumentReference,
    messageId?: string,
    batch?: firestore.WriteBatch
  ) {
    const messageRef = chatRoomRef.collection('chats').doc(messageId ?? v4());
    if (!batch) {
      await messageRef.set(params);
    } else {
      batch.set(messageRef, params);
    }
    return messageRef;
  }

  public async updateRoomRecentChat(
    params: IChatRoomModel['recent_chat'],
    chatRoomRef: firestore.DocumentReference,
    batch?: firestore.WriteBatch
  ) {
    if (!batch) {
      await chatRoomRef.update({
        recent_chat: params,
      });
    } else {
      batch.update(chatRoomRef, {
        recent_chat: params,
      });
    }
    return chatRoomRef;
  }

  public async updateTemplateSendCounter(
    templateRef: firestore.DocumentReference,
    batch?: firestore.WriteBatch
  ) {
    let out = 0;
    const get = await templateRef.get();
    out = get.data()?.out ?? 0;

    out++;

    if (!batch) {
      await templateRef.update({
        out,
      });
    } else {
      batch.update(templateRef, {
        out,
      });
    }

    return templateRef;
  }
}

export const firestoreService = new FirestoreService();
