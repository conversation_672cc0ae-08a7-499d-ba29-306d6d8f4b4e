import { Request, Response } from 'express';
import { body } from 'express-validator';
import multer from 'multer';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import requestChecker from '../../../middlewares/requestValidator';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import { File } from '@google-cloud/storage';
import { profileService } from '../../../services/profileService';
import { IBusinessDocumentFields } from '../../../types/services/profile_service_types';

export interface BusinessDocumentFields {
  documentTypes: string;
  documentNumber: string;
  expiredDate: Date;

  imageUrl: string;

  clientRef: string;
}

export interface SetBusinessDocumentReqBody extends BusinessDocumentFields {}

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export default class SetBusinessDocument {
  public static middlewares = [
    formDataParse.single('image'),
    body('documentTypes').notEmpty(),
    body('documentNumber').notEmpty(),
    body('expiredDate').notEmpty().toDate(),

    body('imageUrl').optional(),

    body('clientRef').notEmpty(),
    requestChecker,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, SetBusinessDocumentReqBody>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.get();

    const payload: IBusinessDocumentFields = {
      DocumentNumber: req.body.documentNumber,
      DocumentTypes: req.body.documentTypes,
      ExpiredDate: req.body.expiredDate,
    };

    let file: File | null = null;
    if (req.file?.buffer) {
      file = bucketTrimitraIdeal.file(`client-documents/${clientDocRef.id}/business-document.jpg`);
    }

    try {
      if (file) {
        if (req.file) {
          await file.save(req.file.buffer, {
            public: true,
          });
        }

        payload.BusinessDocumentImage = file.publicUrl();
      } else {
        payload.BusinessDocumentImage = req.body.imageUrl;
      }

      const update = await profileService.updateBusinessDocument(
        payload,
        getClient.data()!.details.owner_phone_number
      );

      res.send(
        successResponse({
          type: 'CREATED',
          data: update?.data,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
