import { <PERSON>quest<PERSON><PERSON><PERSON> } from 'express';
import { query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import ColdLeadsModel from '../../model/ColdLeadsModel';
import FreeLeadsModel from '../../model/FreeLeadsModel';
import successResponse from '../../schema/successResponse';
import { ResponseSchema } from '../../schema/types/ResponseSchema';

interface ReqQuery {
  phoneNumber: string;
}

type OrgProperties = {
  freeLeads: boolean;
  coldLeads: boolean;
  myLeads: boolean;
  agentCode: string | null;
};

interface ResBody {
  [key: string]: OrgProperties;
}

const checkPhoneNumberStoredLocation: {
  middlewares: RequestHandler[];
  handler: RequestHand<PERSON><any, any, ResponseSchema, ReqQuery>;
} = {
  middlewares: [query('phoneNumber').notEmpty(), requestValidator],
  handler: async (req, res) => {
    const leadsCollection = myFirestore.collection('leads');
    const coldLeadsCollection = myFirestore.collection('cold_leads');
    const freeLeadsCollection = myFirestore.collection('free_leads');

    const organizations = ['amartahonda', 'amartachery', 'amartaneta', 'amartavinfast'];

    const resBody: ResBody = {};

    for (const org of organizations) {
      resBody[org] = {
        agentCode: null,
        coldLeads: false,
        freeLeads: false,
        myLeads: false,
      };
      const docName = `${org}-${req.query.phoneNumber}`;
      const getLeads = await leadsCollection.doc(docName).withConverter(LeadsModel.converter).get();
      const getColdLeads = await coldLeadsCollection
        .doc(docName)
        .withConverter(ColdLeadsModel.converter)
        .get();
      const getFreeLeads = await freeLeadsCollection
        .doc(docName)
        .withConverter(FreeLeadsModel.converter)
        .get();

      if (getLeads.exists) {
        const data = getLeads.data()!;
        resBody[org].myLeads = true;
        resBody[org].agentCode = data.agentCode;
      }
      if (getColdLeads.exists) {
        const data = getColdLeads.data()!;
        resBody[org].coldLeads = true;
        resBody[org].agentCode = data.agentCode;
      }
      if (getFreeLeads.exists) {
        resBody[org].freeLeads = true;
        resBody[org].agentCode = null;
      }
    }

    res.send(
      successResponse({
        data: resBody,
        type: 'FETCHED',
      })
    );
  },
};

export default checkPhoneNumberStoredLocation;
