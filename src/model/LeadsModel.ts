import { firestore } from 'firebase-admin';
import { ILeadsDocument } from '../types/firestore/leads_model.types';
import FirestoreDataConverter = firestore.FirestoreDataConverter;

class LeadsModel implements ILeadsDocument<Date> {
  public static converter: FirestoreDataConverter<LeadsModel> = {
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot): LeadsModel {
      const data = snapshot.data() as ILeadsDocument;
      return new LeadsModel({
        agentCode: data.agentCode,
        area: data.area,
        title: data.title,
        firstName: data.firstName,
        lastName: data.lastName,
        phoneNumber: data.phoneNumber,
        email: data.email,
        domicile: data.domicile,
        vehicleUsage: data.vehicleUsage,
        paymentPlan: data.paymentPlan,
        downPaymentPlan: data.downPaymentPlan || null,
        hasVehicleLoan: data.hasVehicleLoan,
        vehicleOptions: data.vehicleOptions,
        source: data.source,
        organization: data.organization,
        createdAt: data.createdAt.toDate(),
        statusLevel: data.statusLevel,
        updatedAt: data.updatedAt?.toDate(),
        updateHistories:
          data.updateHistories?.map(value => ({
            ...value,
            updatedAt: value.updatedAt.toDate(),
            followUpScheduledAt: value.followUpScheduledAt?.toDate() ?? null,
          })) ?? [],
        purchasePlan: data.purchasePlan,
        nextTotalVehicleOwnerShip: data.nextTotalVehicleOwnerShip,
        isTracking: data.isTracking,
        notes: data.notes ?? '',
        idCard_number: data.idCard_number,
        driverLicense_number: data.driverLicense_number,
        agentName: data.agentName,
        phoneNumberAgent: data.phoneNumberAgent,
        whatsapp: data.whatsapp
          ? {
              ...data.whatsapp,
              response: data.whatsapp.response
                ? {
                    ...data.whatsapp.response,
                    createdAt: data.whatsapp.response?.createdAt?.toDate() ?? null,
                    repliedAt: data.whatsapp.response?.repliedAt?.toDate() ?? null,
                  }
                : null,
            }
          : null,
        followUp: data.followUp,
        followUpRequestedAt: data.followUpRequestedAt?.toDate(),
        followUpStartedAt: data.followUpStartedAt?.toDate(),
        followUpScheduledAt: data.followUpScheduledAt?.toDate(),
        hotLeads: data.hotLeads ?? false,
        hotLeadsRequestedAt: data.hotLeadsRequestedAt?.toDate() ?? null,
        pendingRequestReactivateTracking: data.pendingRequestReactivateTracking
          ? {
              ...data.pendingRequestReactivateTracking,
              requestedAt: data.pendingRequestReactivateTracking.requestedAt.toDate(),
              response: data.pendingRequestReactivateTracking.response
                ? {
                    ...data.pendingRequestReactivateTracking.response,
                    updatedAt: data.pendingRequestReactivateTracking.response.updatedAt.toDate(),
                  }
                : null,
            }
          : null,
        webWhatsapp: data.webWhatsapp
          ? {
              inbound: {
                ...data.webWhatsapp.inbound,
                updatedAt: data.webWhatsapp.inbound.updatedAt.toDate(),
              },
              outbound: {
                ...data.webWhatsapp.outbound,
                updatedAt: data.webWhatsapp.outbound.updatedAt.toDate(),
              },
            }
          : null,
        testDrive: data.testDrive
          ? {
              ...data.testDrive,
              date: data.testDrive.date.toDate(),
            }
          : null,
        spk: data.spk
          ? {
              ...data.spk,
              createdAt: data.spk.createdAt.toDate(),
            }
          : null,
        requestPromo: data.requestPromo
          ? {
              ...data.requestPromo,
              createdAt: data.requestPromo.createdAt.toDate(),
            }
          : null,
        needToMoveToColdAt: data.needToMoveToColdAt?.toDate() ?? null,
        fromFreeLeads: data.fromFreeLeads ?? false,
        freeLeadsCreatedAt: data.freeLeadsCreatedAt?.toDate(),
        feedBackVoice: data.feedBackVoice ?? null,
        feedBackText: data.feedBackText ?? null,
        feedbackUpdatedAt: data.feedbackUpdatedAt?.toDate(),
        agentWhatsappConversations:
          data.agentWhatsappConversations?.map(a => {
            return {
              ...a,
              createdAt: a.createdAt?.toDate() || null,
              startChatAt: a.startChatAt?.toDate() || null,
              endChatAt: a.endChatAt?.toDate() || null,
              startInboundChatAt: a.startInboundChatAt?.toDate() || null,
              endInboundChatAt: a.endInboundChatAt?.toDate() || null,
              startOutboundChatAt: a.startOutboundChatAt?.toDate() || null,
              endOutboundChatAt: a.endOutboundChatAt?.toDate() || null,
            };
          }) || [],

        firstMessageDirection: data.firstMessageDirection || null,
        b2bCreatedSurveyOrders: data.b2bCreatedSurveyOrders
          ? data.b2bCreatedSurveyOrders.map(value => {
              return {
                ...value,
                createdAt: value.createdAt.toDate(),
              };
            })
          : [],
        tradeIn: data.tradeIn || null,

        ref: snapshot.ref,
      });
    },
    toFirestore(modelObject: LeadsModel): FirebaseFirestore.DocumentData {
      return {
        agentCode: modelObject.agentCode,
        area: modelObject.area,
        title: modelObject.title,
        firstName: modelObject.firstName,
        lastName: modelObject.lastName,
        phoneNumber: modelObject.phoneNumber,
        email: modelObject.email,
        domicile: modelObject.domicile,
        vehicleUsage: modelObject.vehicleUsage,
        paymentPlan: modelObject.paymentPlan,
        downPaymentPlan: modelObject.downPaymentPlan || null,
        hasVehicleLoan: modelObject.hasVehicleLoan,
        vehicleOptions: modelObject.vehicleOptions,
        source: modelObject.source,
        organization: modelObject.organization,
        createdAt: modelObject.createdAt,
        statusLevel: modelObject.statusLevel,
        updatedAt: modelObject.updatedAt,
        updateHistories: modelObject.updateHistories,
        purchasePlan: modelObject.purchasePlan,
        nextTotalVehicleOwnerShip: modelObject.nextTotalVehicleOwnerShip,
        isTracking: modelObject.isTracking,
        notes: modelObject.notes,
        idCard_number: modelObject.idCard_number,
        driverLicense_number: modelObject.driverLicense_number,
        agentName: modelObject.agentName,
        phoneNumberAgent: modelObject.phoneNumberAgent,
        whatsapp: modelObject.whatsapp
          ? {
              ...modelObject.whatsapp,
              response: modelObject.whatsapp.response
                ? {
                    text: modelObject.whatsapp.response?.text ?? '',
                    repliedAt: modelObject.whatsapp.response?.repliedAt ?? null,
                  }
                : null,
            }
          : null,
        followUp: modelObject.followUp ?? false,
        followUpRequestedAt: modelObject.followUpRequestedAt,
        followUpStartedAt: modelObject.followUpStartedAt,
        followUpScheduledAt: modelObject.followUpScheduledAt,
        hotLeads: modelObject.hotLeads ?? false,
        hotLeadsRequestedAt: modelObject.hotLeadsRequestedAt,
        pendingRequestReactivateTracking: modelObject.pendingRequestReactivateTracking,
        webWhatsapp: modelObject.webWhatsapp,
        testDrive: modelObject.testDrive,
        spk: modelObject.spk,
        requestPromo: modelObject.requestPromo,
        needToMoveToColdAt: modelObject.needToMoveToColdAt,
        fromFreeLeads: modelObject.fromFreeLeads,
        freeLeadsCreatedAt: modelObject.freeLeadsCreatedAt,
        feedBackText: modelObject.feedBackText,
        feedBackVoice: modelObject.feedBackVoice,
        feedbackUpdatedAt: modelObject.feedbackUpdatedAt,
        firstMessageDirection: modelObject.firstMessageDirection,
        agentWhatsappConversations: modelObject.agentWhatsappConversations || [],
        tradeIn: modelObject.tradeIn || null,
        b2bCreatedSurveyOrders: modelObject.b2bCreatedSurveyOrders || [],
      };
    },
  };
  public agentCode!: ILeadsDocument['agentCode'];
  public area!: ILeadsDocument['area'];
  public title!: ILeadsDocument['title'];
  public firstName!: ILeadsDocument['firstName'];
  public lastName!: ILeadsDocument['lastName'];
  public phoneNumber!: ILeadsDocument['phoneNumber'];
  public email!: ILeadsDocument['email'];
  public domicile!: ILeadsDocument['domicile'];
  public vehicleUsage!: ILeadsDocument['vehicleUsage'];
  public paymentPlan!: ILeadsDocument['paymentPlan'];
  public vehicleOptions!: ILeadsDocument['vehicleOptions'];
  public source!: ILeadsDocument['source'];
  public createdAt!: ILeadsDocument<Date>['createdAt'];
  public hasVehicleLoan!: ILeadsDocument['hasVehicleLoan'];
  public statusLevel!: ILeadsDocument['statusLevel'];
  public updatedAt!: ILeadsDocument<Date>['updatedAt'];
  public updateHistories!: ILeadsDocument<Date>['updateHistories'];
  public purchasePlan!: ILeadsDocument['purchasePlan'];
  public downPaymentPlan!: ILeadsDocument['downPaymentPlan'];
  public nextTotalVehicleOwnerShip!: ILeadsDocument['nextTotalVehicleOwnerShip'];
  public organization!: ILeadsDocument['organization'];
  public isTracking!: ILeadsDocument['isTracking'];
  public notes!: ILeadsDocument['notes'];
  public idCard_number!: ILeadsDocument['idCard_number'];
  public driverLicense_number!: ILeadsDocument['driverLicense_number'];
  public agentName!: ILeadsDocument['agentName'];
  public phoneNumberAgent!: ILeadsDocument['phoneNumberAgent'];
  public followUp!: ILeadsDocument['followUp'];
  public followUpRequestedAt!: ILeadsDocument<Date>['followUpRequestedAt'];
  public followUpStartedAt!: ILeadsDocument<Date>['followUpStartedAt'];
  public followUpScheduledAt!: ILeadsDocument<Date>['followUpScheduledAt'];
  public hotLeads!: ILeadsDocument['hotLeads'];
  public hotLeadsRequestedAt!: ILeadsDocument<Date>['hotLeadsRequestedAt'];
  public whatsapp!: ILeadsDocument<Date>['whatsapp'];
  public pendingRequestReactivateTracking!: ILeadsDocument<Date>['pendingRequestReactivateTracking'];
  public webWhatsapp!: ILeadsDocument<Date>['webWhatsapp'];
  public testDrive!: ILeadsDocument<Date>['testDrive'];
  public spk!: ILeadsDocument<Date>['spk'];
  public requestPromo!: ILeadsDocument<Date>['requestPromo'];
  public needToMoveToColdAt!: ILeadsDocument<Date>['needToMoveToColdAt'];
  public fromFreeLeads!: ILeadsDocument<Date>['fromFreeLeads'];
  public freeLeadsCreatedAt!: ILeadsDocument<Date>['freeLeadsCreatedAt'];
  public feedBackText!: ILeadsDocument<Date>['feedBackText'];
  public feedBackVoice!: ILeadsDocument<Date>['feedBackVoice'];
  public feedbackUpdatedAt!: ILeadsDocument<Date>['feedbackUpdatedAt'];
  public firstMessageDirection!: ILeadsDocument<Date>['firstMessageDirection'];
  public b2bCreatedSurveyOrders!: ILeadsDocument<Date>['b2bCreatedSurveyOrders'];
  public agentWhatsappConversations!: ILeadsDocument<Date>['agentWhatsappConversations'];
  public tradeIn!: ILeadsDocument<Date>['tradeIn'];
  public ref!: firestore.DocumentReference;

  constructor(params?: {
    agentCode: ILeadsDocument['agentCode'];
    area?: ILeadsDocument['area'];
    title: ILeadsDocument['title'];
    firstName: ILeadsDocument['firstName'];
    lastName: ILeadsDocument['lastName'];
    phoneNumber: ILeadsDocument['phoneNumber'];
    email: ILeadsDocument['email'];
    domicile: ILeadsDocument['domicile'];
    vehicleUsage: ILeadsDocument['vehicleUsage'];
    paymentPlan: ILeadsDocument['paymentPlan'];
    vehicleOptions: ILeadsDocument['vehicleOptions'];
    source: ILeadsDocument['source'];
    organization: ILeadsDocument['organization'];
    createdAt: ILeadsDocument<Date>['createdAt'];
    hasVehicleLoan: ILeadsDocument['hasVehicleLoan'];
    statusLevel?: ILeadsDocument['statusLevel'];
    updatedAt?: ILeadsDocument<Date>['updatedAt'];
    updateHistories?: ILeadsDocument<Date>['updateHistories'];
    purchasePlan: ILeadsDocument['purchasePlan'];
    downPaymentPlan: ILeadsDocument['downPaymentPlan'];
    nextTotalVehicleOwnerShip: ILeadsDocument['nextTotalVehicleOwnerShip'];
    isTracking: ILeadsDocument['isTracking'];
    notes: ILeadsDocument['notes'];
    idCard_number: ILeadsDocument['idCard_number'];
    driverLicense_number: ILeadsDocument['driverLicense_number'];
    agentName?: ILeadsDocument['agentName'];
    phoneNumberAgent?: ILeadsDocument['phoneNumberAgent'];
    whatsapp?: ILeadsDocument<Date>['whatsapp'];
    followUp?: ILeadsDocument['followUp'];
    followUpRequestedAt?: ILeadsDocument<Date>['followUpRequestedAt'];
    followUpStartedAt?: ILeadsDocument<Date>['followUpStartedAt'];
    followUpScheduledAt?: ILeadsDocument<Date>['followUpScheduledAt'];
    hotLeads?: ILeadsDocument['hotLeads'];
    hotLeadsRequestedAt?: ILeadsDocument<Date>['hotLeadsRequestedAt'];
    pendingRequestReactivateTracking?: ILeadsDocument<Date>['pendingRequestReactivateTracking'];
    webWhatsapp?: ILeadsDocument<Date>['webWhatsapp'];
    testDrive?: ILeadsDocument<Date>['testDrive'];
    spk?: ILeadsDocument<Date>['spk'];
    requestPromo?: ILeadsDocument<Date>['requestPromo'];
    needToMoveToColdAt?: ILeadsDocument<Date>['needToMoveToColdAt'];
    fromFreeLeads?: ILeadsDocument<Date>['fromFreeLeads'];
    freeLeadsCreatedAt?: ILeadsDocument<Date>['freeLeadsCreatedAt'];
    feedBackText?: ILeadsDocument<Date>['feedBackText'];
    feedBackVoice?: ILeadsDocument<Date>['feedBackVoice'];
    feedbackUpdatedAt?: ILeadsDocument<Date>['feedbackUpdatedAt'];
    firstMessageDirection?: ILeadsDocument<Date>['firstMessageDirection'];
    agentWhatsappConversations?: ILeadsDocument<Date>['agentWhatsappConversations'];
    b2bCreatedSurveyOrders?: ILeadsDocument<Date>['b2bCreatedSurveyOrders'];
    tradeIn?: ILeadsDocument<Date>['tradeIn'];
    ref: firestore.DocumentReference;
  }) {
    if (params) {
      this.agentCode = params.agentCode;
      this.area = params.area ?? null;
      this.title = params.title;
      this.firstName = params.firstName;
      this.lastName = params.lastName;
      this.phoneNumber = params.phoneNumber;
      this.email = params.email;
      this.domicile = params.domicile;
      this.vehicleUsage = params.vehicleUsage;
      this.paymentPlan = params.paymentPlan;
      this.vehicleOptions = params.vehicleOptions;
      this.source = params.source;
      this.organization = params.organization;
      this.createdAt = params.createdAt;
      this.hasVehicleLoan = params.hasVehicleLoan;
      this.statusLevel = params.statusLevel ?? 0;
      this.updatedAt = params.updatedAt ?? null;
      this.updateHistories = params.updateHistories ?? [];
      this.purchasePlan = params.purchasePlan;
      this.downPaymentPlan = params.downPaymentPlan || null;
      this.nextTotalVehicleOwnerShip = params.nextTotalVehicleOwnerShip;
      this.isTracking = params.isTracking;
      this.notes = params.notes ?? '';
      this.idCard_number = params.idCard_number ?? null;
      this.driverLicense_number = params.driverLicense_number ?? null;
      this.agentName = params.agentName ?? '';
      this.phoneNumberAgent = params.phoneNumberAgent ?? [];
      this.followUp = params.followUp ?? false;
      this.followUpRequestedAt = params.followUpRequestedAt ?? null;
      this.followUpStartedAt = params.followUpStartedAt ?? null;
      this.followUpScheduledAt = params.followUpScheduledAt ?? null;
      this.hotLeads = params.hotLeads ?? false;
      this.hotLeadsRequestedAt = params.hotLeadsRequestedAt ?? null;
      this.whatsapp = params.whatsapp ?? null;
      this.pendingRequestReactivateTracking = params.pendingRequestReactivateTracking ?? null;
      this.webWhatsapp = params.webWhatsapp ?? null;
      this.testDrive = params.testDrive ?? null;
      this.spk = params.spk ?? null;
      this.requestPromo = params.requestPromo ?? null;
      this.needToMoveToColdAt = params.needToMoveToColdAt ?? null;
      this.fromFreeLeads = params.fromFreeLeads ?? false;
      this.freeLeadsCreatedAt = params.freeLeadsCreatedAt ?? null;
      this.feedBackVoice = params.feedBackVoice ?? null;
      this.feedBackText = params.feedBackText ?? null;
      this.feedbackUpdatedAt = params.feedbackUpdatedAt ?? null;
      this.firstMessageDirection = params.firstMessageDirection || null;
      this.agentWhatsappConversations = params.agentWhatsappConversations ?? [];
      this.b2bCreatedSurveyOrders = params?.b2bCreatedSurveyOrders || [];
      this.tradeIn = params?.tradeIn || null;

      this.ref = params.ref;
    }
  }

  toJsonResponse(): ILeadsDocument<Date> {
    return {
      ...this,
      ref: this.ref.path,
      documentId: this.ref.id,
      whatsapp: this.whatsapp
        ? {
            ...this.whatsapp,
            idealPath: this.whatsapp.idealPath?.path ?? null,
          }
        : null,
      agentWhatsappConversations: this.agentWhatsappConversations.map(c => {
        return {
          ...c,
          docRef: c.docRef?.path,
        };
      }),
    };
  }
}

export default LeadsModel;
