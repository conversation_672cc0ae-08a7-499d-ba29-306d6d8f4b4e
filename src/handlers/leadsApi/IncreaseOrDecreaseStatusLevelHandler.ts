import { json, Request, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body, param } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';
import leadsModel from '../../model/LeadsModel';
import { LeadsOrganization } from '../../types/firestore/leads_model.types';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import fetchAgent from '../../services/agent/fetchAgent';
import { leadsNotesCounter } from '../../helpers/leads/leadsNotes';

type StatusType = 'increment' | 'decrement';

interface ReqBody {
  leadsPhoneNumber: string;
  notes: string;
  updatedBy: string;
  organization: LeadsOrganization;
}

const IncreaseOrDecreaseStatusLevelHandler = {
  middlewares: [
    leadsAuthorizer,
    json(),
    param('type').notEmpty().isIn(['increment', 'decrement']),
    body('leadsPhoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('notes')
      .optional()
      .isLength({ min: 30, max: 600 })
      .withMessage('Catatan Minimal 30 s.d 600 karakter.'),
    body('updatedBy').notEmpty(),
    body('organization').notEmpty(),
    requestValidator,
  ],
  handler: async (req: Request<{ type: StatusType }, ResponseSchema, ReqBody>, res: Response) => {
    const collection = myFirestore.collection('leads');
    const getLeads = await collection
      .withConverter(LeadsModel.converter)
      .doc(`${req.body.organization}-${req.body.leadsPhoneNumber}`)
      .get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;
    let finalStatusLevel = leads.statusLevel;
    switch (req.params.type) {
      case 'increment':
        finalStatusLevel++;
        break;
      case 'decrement':
        finalStatusLevel--;
        break;
    }

    let agentName: string | null = null;
    if (!leads.agentName) {
      try {
        const fetch = await fetchAgent(leads.agentCode);
        agentName = fetch.name;
      } catch (e: any) {
        agentName = 'NO_NAME';
      }
    } else {
      agentName = leads.agentName;
    }

    const notesCounter = await leadsNotesCounter(leads);
    let notes: ILeadsNotesDocument<Date> | null = {
      statusLevel: finalStatusLevel,
      notes: req.body.notes,
      updatedAt: new Date(),
      updatedByUser: req.body.updatedBy,
      event: req.params.type === 'increment' ? 'increaseLevelStatus' : 'decreaseLevelStatus',
      agentCode: leads.agentCode,
      phoneNumber: req.body.leadsPhoneNumber,
      organization: leads.organization,

      firstName: leads.firstName,
      lastName: leads.lastName,

      agentName: agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.agentName = agentName;
    leads.statusLevel = finalStatusLevel;
    leads.notes = req.body.notes;
    leads.updatedAt = new Date();
    leads.updateHistories.push({
      ...notes,
    });

    try {
      const batch = myFirestore.batch();

      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      if (notes) {
        batch.set(myFirestore.collection('leads_notes').doc(), notes);
      }
      await batch.commit();

      await aggregatesAgent(leads.agentCode, req.body.organization).then();

      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: `Error: ${e.toString()}`,
        })
      );
    }
  },
};

export default IncreaseOrDecreaseStatusLevelHandler;
