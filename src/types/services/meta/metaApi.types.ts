export interface IMetaSendMessageParamsV2 {
  messaging_product: 'whatsapp';
  recipient_type: 'individual';
  to: string;
  type?: string;
  text?: Text;
  video?: MetaMediaContextSendMessage;
  image?: MetaMediaContextSendMessage;
  document?: MetaMediaContextSendMessage;
}

export interface Text {
  preview_url: boolean;
  body: string;
}

export interface MetaMediaContextSendMessage {
  id?: string;
  link?: string;
  caption?: string;
  filename?: string;
}

export interface IResponseSendMessage {
  messaging_product: string;
  contacts: Contact[];
  messages: Message[];
}

export interface Contact {
  input: string;
  wa_id: string;
}

export interface Message {
  id: string;
}
