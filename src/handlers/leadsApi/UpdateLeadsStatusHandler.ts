import { json, Request, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';
import { LeadsOrganization, LeadsUpdateEvent } from '../../types/firestore/leads_model.types';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import fetchAgent from '../../services/agent/fetchAgent';
import { leadsNotesCounter } from '../../helpers/leads/leadsNotes';

interface ReqBody {
  statusLevel: number;
  leadsPhoneNumber: string;
  notes: string;
  updatedBy: string;
  organization: LeadsOrganization;
}

const updateLeadsStatusHandler = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('statusLevel').notEmpty().isNumeric().toInt(),
    body('leadsPhoneNumber')
      .exists()
      .bail()
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('organization').notEmpty(),
    body('notes')
      .optional()
      .isLength({ min: 30, max: 600 })
      .withMessage('Catatan Minimal 30 s.d 600 karakter.'),
    body('updatedBy').notEmpty(),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response) => {
    const collection = myFirestore.collection('leads');
    const getLeads = await collection
      .withConverter(LeadsModel.converter)
      .doc(`${req.body.organization}-${req.body.leadsPhoneNumber}`)
      .get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;
    let notes: ILeadsNotesDocument<Date> | null = null;

    let type!: LeadsUpdateEvent;
    if (req.body.statusLevel !== leads.statusLevel) {
      if (req.body.statusLevel > leads.statusLevel) {
        type = 'increaseLevelStatus';
      } else if (req.body.statusLevel < leads.statusLevel) {
        type = 'decreaseLevelStatus';
      }

      let agentName: string | null = null;
      if (!leads.agentName) {
        try {
          const fetch = await fetchAgent(leads.agentCode);
          agentName = fetch.name;
        } catch (e: any) {
          agentName = 'NO_NAME';
        }
      } else {
        agentName = leads.agentName;
      }

      const notesCounter = await leadsNotesCounter(leads);
      notes = {
        statusLevel: req.body.statusLevel,
        notes: req.body.notes,
        updatedAt: new Date(),
        updatedByUser: req.body.updatedBy,
        event: type,
        agentCode: leads.agentCode,
        organization: req.body.organization,
        phoneNumber: req.body.leadsPhoneNumber,
        firstName: leads.firstName,
        lastName: leads.lastName,
        agentName: leads.agentName,
        moveToCold: false,
        reactivate: {
          currentTotal: notesCounter.totalReactivate,
        },
        totalUpdateNotes: notesCounter.totalNotes + 1,
      };

      leads.agentName = agentName;
      leads.statusLevel = req.body.statusLevel;
      leads.updatedAt = new Date();

      leads.notes = req.body.notes;
      leads.updateHistories.push({
        ...notes,
      });
    }

    try {
      const batch = myFirestore.batch();

      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      if (notes) {
        batch.set(myFirestore.collection('leads_notes').doc(), notes);
      }

      await batch.commit();

      await aggregatesAgent(leads.agentCode, req.body.organization).then();
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: `Error: ${e.toString()}`,
        })
      );
    }
  },
};

export default updateLeadsStatusHandler;
