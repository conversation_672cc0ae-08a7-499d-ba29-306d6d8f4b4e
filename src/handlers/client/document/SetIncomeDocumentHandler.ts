import { Request, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import multer from 'multer';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import { File } from '@google-cloud/storage';
import { IIncomeDocumentFields } from '../../../types/services/profile_service_types';
import { profileService } from '../../../services/profileService';

export interface SetIncomeDocumentFields {
  incomeType: string;
  documentType: string;
  incomeAmount: string;
  additionalInfo: string;
  bankName: string;
  bankAccountNumber: string;
  imageUrl?: string;
}

export interface SetIncomeDocumentReqBody extends SetIncomeDocumentFields {
  clientRef: string;
}

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export default class SetIncomeDocumentHandler {
  public static middlewares = [
    formDataParse.single('image'),
    body('incomeType').notEmpty(),
    body('documentType').notEmpty(),
    body('incomeAmount').notEmpty(),
    body('additionalInfo').optional(),
    body('bankName').optional(),
    body('bankAccountNumber').optional(),

    body('imageUrl').optional(),
    body('clientRef').notEmpty(),
    requestValidator,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, SetIncomeDocumentReqBody>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.get();

    const payload: IIncomeDocumentFields = {
      AdditionalInfo: req.body.additionalInfo ?? null,
      BankAccountNumber: req.body.bankAccountNumber ?? null,
      BankName: req.body.bankName ?? null,
      DocumentType: req.body.documentType,
      IncomeAmount: req.body.incomeAmount,
      IncomeType: req.body.incomeType,
    };

    let file: File | null = null;
    if (req.file?.buffer) {
      file = bucketTrimitraIdeal.file(`client-documents/${clientDocRef.id}/income-document.jpg`);
    }

    try {
      if (file) {
        if (req.file) {
          await file.save(req.file.buffer, {
            public: true,
          });
        }

        payload.ImageIncomeDocument = file.publicUrl();
      } else {
        payload.ImageIncomeDocument = req.body.imageUrl;
      }

      const update = await profileService.updateIncomeDocument(
        payload,
        getClient.data()!.details.owner_phone_number
      );

      res.send(
        successResponse({
          type: 'CREATED',
          data: update?.data,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
