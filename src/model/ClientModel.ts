import { IClientDocument } from '../types/firestore/client_document_types';
import { firestore } from 'firebase-admin';

class ClientModel implements IClientDocument {
  static converter: firestore.FirestoreDataConverter<ClientModel> = {
    fromFirestore(snapshot) {
      const data = snapshot.data();
      const clientModel = new ClientModel();

      clientModel.ref = snapshot.ref;
      clientModel.contacts = data.contacts;
      clientModel.created_time = data.created_time;
      clientModel.doc_admin = data.doc_admin || null;
      clientModel.doc_id = data.doc_id || null;
      clientModel.doc_project_origin = data.doc_project_origin || null;
      clientModel.details = data.details;
      clientModel.profile = data.profile;
      clientModel.freeLeadsStatus = data.freeLeadsStatus || null;
      clientModel.acquiredLeadsStatus = data.acquiredLeadsStatus || null;
      clientModel.dream_vehicle = data.dream_vehicle || null;
      clientModel.survey = data.survey || null;
      clientModel.leads = data.leads || null;
      clientModel.order_histories = data.order_histories || [];
      clientModel.finalDecision = data.finalDecision || null;

      return clientModel;
    },
    toFirestore(clientModel: ClientModel) {
      return {
        contacts: clientModel.contacts,
        created_time: clientModel.created_time,
        doc_admin: clientModel.doc_admin || null,
        doc_id: clientModel.doc_id || null,
        doc_project_origin: clientModel.doc_project_origin || null,
        details: clientModel.details || null,
        profile: clientModel.profile,
        freeLeadsStatus: clientModel.freeLeadsStatus || null,
        acquiredLeadsStatus: clientModel.acquiredLeadsStatus || null,
        dream_vehicle: clientModel.dream_vehicle || null,
        survey: clientModel.survey || null,
        leads: clientModel.leads || null,
        order_histories: clientModel.order_histories || [],
        finalDecision: clientModel.finalDecision || null,
      };
    },
  };

  public ref!: firestore.DocumentReference;
  public contacts!: IClientDocument['contacts'];
  public created_time!: IClientDocument['created_time'];
  public doc_admin!: IClientDocument['doc_admin'];
  public doc_id!: IClientDocument['doc_id'];
  public doc_project_origin!: IClientDocument['doc_project_origin'];
  public details?: IClientDocument['details'];
  public profile!: IClientDocument['profile'];
  public freeLeadsStatus!: IClientDocument['freeLeadsStatus'];
  public acquiredLeadsStatus!: IClientDocument['acquiredLeadsStatus'];
  public dream_vehicle?: IClientDocument['dream_vehicle'];
  public survey?: IClientDocument['survey'];
  public leads?: IClientDocument['leads'];
  public order_histories?: IClientDocument['order_histories'];
  public finalDecision!: IClientDocument['finalDecision'];
}

export default ClientModel;
