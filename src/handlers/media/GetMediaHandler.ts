import { Request, Response } from 'express';
import { query } from 'express-validator';
import { myFirestore } from '../../services/firebaseAdmin';
import requestChecker from '../../middlewares/requestValidator';
import { IFirestoreMessageEntity } from '../../types/firestore/messages/message_types';
import { kataAiServices } from '../../services/KataAiServices';
import { qiscusServiceNew } from '../../services/QiscusServiceNew';
import metaServices from '../../services/MetaServices';
import axios from 'axios';

export default class GetMediaHandler {
  public static middlewares = [query('path').notEmpty(), requestChecker];

  public static async handler(
    req: Request<{ decodedFileName: string }, any, any, { path: string }>,
    res: Response
  ) {
    try {
      const chatRef = myFirestore.doc(req.query.path);
      const roomRef = chatRef.parent.parent;
      const projectRef = roomRef?.parent.parent;

      if (!projectRef) {
        return res.status(404).send({ success: false, message: 'Project not found' });
      }

      const [chatSnapshot, projectSnapshot] = await Promise.all([chatRef.get(), projectRef.get()]);

      if (!chatSnapshot.exists || !projectSnapshot.exists) {
        return res.status(404).send({
          success: false,
          message: 'Chat or project data not found',
        });
      }

      const chatData = chatSnapshot.data() as IFirestoreMessageEntity;
      const projectData = projectSnapshot.data()!;

      let mediaId: string = '';
      switch (chatData.message.type) {
        case 'document':
          mediaId = chatData.message.document?.id || '';
          break;
        case 'image':
          mediaId = chatData.message.image?.id || '';
          break;
        case 'audio':
          mediaId = chatData.message.audio?.id || '';
          break;
        case 'video':
          mediaId = chatData.message.video?.id || '';
          break;
        default:
          return res.status(400).send({
            success: false,
            message: 'Unsupported media type',
          });
      }

      let response;
      switch (projectData.provider) {
        case 'qiscus':
          response = await qiscusServiceNew.getMedia(mediaId);
          break;
        case 'kata.ai':
          response = await kataAiServices.showImage(mediaId);
          break;
        case 'meta':
          response = await metaServices.getFile(mediaId, projectData.meta);
          break;
        default:
          return res.status(400).send({
            success: false,
            message: 'Unsupported provider',
          });
      }

      const fileName = req.params.decodedFileName;
      const contentType = response.headers['content-type'] || 'application/octet-stream';

      res.setHeader('Content-Disposition', `inline; filename="${fileName}"`);
      res.contentType(contentType);
      res.send(response.data);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Error in GetMediaHandler:', error.response?.data);
      } else {
        console.error('Error in GetMediaHandler:', error);
      }
      res.status(500).send({
        success: false,
        message: 'Internal server error',
      });
    }
  }
}
