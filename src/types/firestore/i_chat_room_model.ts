import { firestore } from 'firebase-admin';

export interface IChatRoomModel<DATE = firestore.Timestamp, REF = firestore.DocumentReference> {
  ref: REF;
  blockReason: null | string;
  blocked: boolean;
  clients: REF[];
  contacts: string[];
  created_at: DATE;
  doc_department: REF | null;
  cityGroup: null | string;
  cityGroupUpdatedBy: string | null;
  cityGroupUpdatedAt: DATE | null;
  organization: string | null;
  organizationUpdatedBy: string | null;
  organizationGroup: string | null;
  organizationUpdatedAt: DATE | null;
  dream_vehicle: {
    buy_time: DATE | null;
    color_code: string | null;
    color_name: string | null;
    model_name: string | null;
    variant_code: string | null;
    variant_name: string | null;
    year: string | null;
  } | null;
  headers: {
    title: string;
  };
  label: REF | null;
  label_updated_at: DATE | null;
  last_inbound: DATE | null;
  last_message_type: 'IN' | 'OUT';
  recent_chat: {
    contact: string;
    direction: 'IN' | 'OUT';
    display_name: string;
    read: boolean;
    statuses: {
      delivered: DATE | null;
      failed: DATE | null;
      read: DATE | null;
      sent: DATE | null;
    };
    text: string;
    timestamp: DATE;
    type: string;
    unixtime: number;
  } | null;
  wait_for_answer: {
    asked_at: DATE | null;
    message_ref: REF;
    question_id: null;
  } | null;
  exclusive_admin: {
    email: string;
    ref: string;
    name: string;
  } | null;
}
