import { json, <PERSON>questH<PERSON><PERSON> } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myAuth, myFirestore } from '../../services/firebaseAdmin';
import AdminModel from '../../model/AdminModel';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import authChecker from '../../middlewares/authChecker';
import { IFirestoreAdminEntity } from '../../types/firestore/messages/message_types';

interface ReqBody {
  name: string;
  email: string;
  password: string;
  departmentPath: string | null;
  projectPath: string;
  role: 'owner' | 'admin';
}

const addNewAdminHandler: {
  handler: RequestHandler<any, ResponseSchema, ReqBody, any, { account: IFirestoreAdminEntity }>;
  middlewares: RequestHandler[];
} = {
  middlewares: [
    json(),
    authChecker,
    body('name').trim().notEmpty().withMessage('Nama dibutuhkan'),
    body('email')
      .notEmpty()
      .withMessage('Email dibutuhkan')
      .trim()
      .isEmail()
      .withMessage('Email tidak valid')
      .custom(async input => {
        const collection = myFirestore.collection('admins');
        const get = await collection.where('email', '==', input).count().get();

        if (get.data().count > 0) {
          throw new Error('E-mail already in use');
        }
      }),
    body('password')
      .trim()
      .notEmpty()
      .withMessage('Password dibutuhkan')
      .isLength({ min: 6 })
      .withMessage('Password minimal 6 karakter'),
    body('role').notEmpty().withMessage('Role dibutuhkan'),
    body('projectPath').notEmpty().withMessage('Project/Provider dibutuhkan'),
    body('departmentPath').optional(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const adminCollection = myFirestore.collection('admins');

    let uid = '';
    try {
      const createUserFirestore = await myAuth().createUser({
        email: req.body.email.trim(),
        password: req.body.password.trim(),
        displayName: req.body.name.toUpperCase().trim(),
      });
      uid = createUserFirestore.uid;
    } catch (e) {
      return res.send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
          messages: 'Error ketika membuat user ke firebase auth',
        })
      );
    }

    const doc = adminCollection.doc(uid);
    const adminModel = new AdminModel({
      ref: doc,
      name: req.body.name,
      level: req.body.role,
      doc_admin: adminCollection.doc(uid),
      email: req.body.email,
      doc_department: req.body.departmentPath ? myFirestore.doc(req.body.departmentPath) : null,
      project: null,
      department: null,
      active: true,
      doc_project: myFirestore.doc(req.body.projectPath),
      createdBy: res.locals.account.email,
      createdSource: 'ideal',
    });

    const getProject = await adminModel.doc_project.get();
    const dataProject: any = getProject.data()!;
    adminModel.project = {
      name: dataProject.legal_name,
      provider: dataProject.provider,
    };

    if (req.body.departmentPath) {
      const getDepartment = await adminModel.doc_department!.get();
      const dataDepartment = getDepartment.data()!;

      adminModel.department = {
        name: dataDepartment.name,
      };
    }

    try {
      await doc.withConverter(AdminModel.converter).set(adminModel);
    } catch (e) {
      await myAuth()
        .deleteUser(uid)
        .catch(() => {});
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
          messages: 'Error ketika membuat user ke firebase firestore',
        })
      );
    }

    return res.send(
      successResponse({
        type: 'CREATED',
        data: null,
      })
    );
  },
};

export default addNewAdminHandler;
