import { json, RequestHandler } from 'express';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../../middlewares/requestValidator';
import { myAuth, myFirestore } from '../../../services/firebaseAdmin';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import AdminModel from '../../../model/AdminModel';
import moment from 'moment';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';

interface ReqBody {
  email: string;
  name: string;
  password: string;
  phoneNumber: string;
  createdBy: string;
  createSource: string;
  projectId: string;
  amartaVip?: {
    mediatorCode: string;
    mediatorName: string;
  };
}

const createUserHandler: {
  middlewares: RequestHandler[];
  handler: Request<PERSON>andler<any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    json(),
    body('email')
      .notEmpty()
      .trim()
      .isEmail()
      .custom(async input => {
        const getAdmin = await myFirestore.collection('admins').where('email', '==', input).get();

        if (getAdmin.size > 0) {
          throw new Error('E-mail already in use');
        }
      }),
    body('amartaVip.mediatorCode')
      .if((input, meta) => {
        return meta.req.body['createSource'] === 'amarta.vip';
      })
      .trim()
      .notEmpty(),
    body('amartaVip.mediatorName')
      .if((input, meta) => {
        return meta.req.body['createSource'] === 'amarta.vip';
      })
      .trim()
      .notEmpty(),
    body('projectId')
      .notEmpty()
      .trim()
      .custom(async input => {
        const getProject = await myFirestore.collection('projects').doc(input).get();

        if (!getProject.exists) {
          throw new Error('ID Project Tidak Tersedia');
        }
      }),
    body('name').notEmpty().trim(),
    body('phoneNumber')
      .optional({ values: 'falsy' })
      .trim()
      .isMobilePhone('id-ID')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('password').notEmpty().trim().isStrongPassword({
      minLength: 6,
      returnScore: true,
    }),
    body('createdBy').notEmpty().trim(),
    body('createSource').notEmpty().trim(),
    requestValidator,
  ],
  handler: async (req, res) => {
    let uid!: string;
    try {
      const createUser = await myAuth().createUser({
        email: req.body.email,
        password: req.body.password,
        displayName: req.body.name,
        phoneNumber: req.body.phoneNumber,
      });
      uid = createUser.uid;
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Gagal buat user pada Firebase Auth',
        })
      );
    }

    const collectionAdmin = myFirestore.collection('admins');
    const docAdmin = collectionAdmin.doc(uid);
    const docProject = myFirestore.collection('projects').doc(req.body.projectId);
    const getProject = await docProject.get();
    const dataProject = getProject.data() as any;

    const newAdmin = new AdminModel({
      ref: docAdmin,
      name: req.body.name,
      phone_number: req.body.phoneNumber,
      doc_admin: docAdmin,
      project: {
        name: dataProject.legal_name,
        provider: dataProject.provider,
      },
      email: req.body.email,
      department: null,
      doc_project: docProject,
      level: 'owner',
      address: '',
      created_time: moment().toDate(),
      active: true,
      last_session_active: null,
      doc_department: null,
      createdBy: req.body.createdBy,
      createdSource: req.body.createSource,
      amartaVip: req.body.amartaVip
        ? {
            mediatorCode: req.body.amartaVip.mediatorCode,
            mediatorName: req.body.amartaVip.mediatorName,
          }
        : null,
    });

    try {
      await docAdmin.withConverter(AdminModel.converter).set(newAdmin);
    } catch (e) {
      console.log('Failed to create admin document', e);
      await myAuth().deleteUser(uid);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Gagal membuat admin baru',
        })
      );
    }

    return res.send(
      successResponse({
        type: 'CREATED',
        data: {
          uid: uid,
          name: req.body.name,
          email: req.body.email,
        },
      })
    );
  },
};

export default createUserHandler;
