import { json } from 'express';
import { HandlerTypes } from '../../../types/handler.types';
import { body } from 'express-validator';
import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import requestValidator from '../../../middlewares/requestValidator';
import successResponse from '../../../schema/successResponse';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import LeadsTradeInDocumentModel from '../../../model/LeadsTradeInDocumentModel';
import { firestore } from 'firebase-admin';

interface ReqBody {
  agentCode: string;
  agentName: string;
  phoneNumber: string;
  organization: string;

  brand: {
    uuid: string;
    name: string;
  };
  model: {
    uuid: string;
    name: string;
  };
  year: string;
  images: string[];
  price: number;
}

const AddLeadsTradeIn: HandlerTypes<any, any, ReqBody> = {
  middlewares: [
    triForceAuthChecker,
    json(),
    body('agentCode').notEmpty(),
    body('agentName').notEmpty(),
    body('phoneNumber').notEmpty(),
    body('organization').notEmpty(),
    body('brand').notEmpty(),
    body('model').notEmpty(),
    body('year').notEmpty(),
    body('images').notEmpty(),
    body('price').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const leadsCollection = myFirestore.collection('leads');
    const docName = `${req.body.organization}-${req.body.phoneNumber}`;
    const leadsDocRef = leadsCollection.doc(docName);
    const getLeads = await leadsDocRef.withConverter(LeadsModel.converter).get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    if (leads.agentCode !== req.body.agentCode) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leadsTradeInCollection = leadsDocRef.collection('trade_in');
    const tradeInDocRef = leadsTradeInCollection.doc();
    const getTradeInTotal = await leadsTradeInCollection.count().get();

    const leadsTradeIn = new LeadsTradeInDocumentModel({
      brand: req.body.brand,
      model: req.body.model,
      year: req.body.year,
      images: req.body.images,
      price: req.body.price,
      createdAt: firestore.Timestamp.now(),
      ref: tradeInDocRef,
    });

    try {
      const batch = myFirestore.batch();
      batch.set(tradeInDocRef.withConverter(LeadsTradeInDocumentModel.converter), leadsTradeIn);
      batch.update(leadsDocRef, {
        tradeIn: {
          count: getTradeInTotal.data().count + 1,
        }
      });
      await batch.commit();
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }

    res.send(
      successResponse({
        data: {
          id: tradeInDocRef.id,
          createdAt: leadsTradeIn.createdAt.toDate(),
          price: leadsTradeIn.price,
          year: leadsTradeIn.year,
          images: leadsTradeIn.images,
          model: leadsTradeIn.model,
          brand: leadsTradeIn.brand,
        },
        type: 'CREATED',
      })
    );
  },
};

export default AddLeadsTradeIn;
