import { checkSchema, Location, ParamSchema, ValidationChain } from 'express-validator';

/**
 * Gunakan ini sebagai pengganti fungsi checkSchema() milik 'express-validator';
 * Fungsi ini mendukung tipe data sebagai generic
 *
 * Contoh : validationSchema<InterfaceAnda>(parameterCheckSchema);
 */
export default function validationSchema<T>(
  params: { [K in keyof Partial<T> | string]: ParamSchema },
  locations: Location[]
): ValidationChain[] {
  return checkSchema({ ...params }, locations);
}
