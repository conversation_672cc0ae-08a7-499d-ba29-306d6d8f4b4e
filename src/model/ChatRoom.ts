import { firestore } from 'firebase-admin';
import { IChatRoomModel } from '../types/firestore/i_chat_room_model';

class ChatRoom implements IChatRoomModel {
  ref: IChatRoomModel['ref'];
  blockReason: string | null = '';
  blocked: boolean;
  cityGroup: null | string;
  cityGroupUpdatedBy: string | null;
  cityGroupUpdatedAt: firestore.Timestamp | null;
  organization: string | null;
  organizationGroup: string | null;
  organizationUpdatedBy: string | null;
  organizationUpdatedAt: firestore.Timestamp | null;
  clients: firestore.DocumentReference[];
  contacts: string[];
  created_at: firestore.Timestamp;
  doc_department: firestore.DocumentReference | null;
  dream_vehicle: {
    buy_time: firestore.Timestamp | null;
    color_code: string | null;
    color_name: string | null;
    model_name: string | null;
    variant_code: string | null;
    variant_name: string | null;
    year: string | null;
  } | null;
  headers: {
    title: string;
  };
  label: firestore.DocumentReference | null;
  label_updated_at: firestore.Timestamp | null;
  last_inbound: firestore.Timestamp | null;
  last_message_type: 'IN' | 'OUT';
  recent_chat: IChatRoomModel['recent_chat'];
  wait_for_answer: IChatRoomModel['wait_for_answer'];
  exclusive_admin: IChatRoomModel['exclusive_admin'];

  constructor(data: IChatRoomModel) {
    this.ref = data.ref;
    this.blockReason = data.blockReason;
    this.blocked = data.blocked;
    this.cityGroup = data.cityGroup;
    this.cityGroupUpdatedBy = data.cityGroupUpdatedBy;
    this.cityGroupUpdatedAt = data.cityGroupUpdatedAt;
    this.organization = data.organization;
    this.organizationUpdatedBy = data.organizationUpdatedBy;
    this.organizationGroup = data.organizationGroup;
    this.organizationUpdatedAt = data.organizationUpdatedAt;
    this.clients = data.clients;
    this.contacts = data.contacts;
    this.created_at = data.created_at;
    this.doc_department = data.doc_department;
    this.dream_vehicle = data.dream_vehicle;
    this.headers = data.headers;
    this.label = data.label;
    this.label_updated_at = data.label_updated_at;
    this.last_inbound = data.last_inbound;
    this.last_message_type = data.last_message_type;
    this.recent_chat = data.recent_chat;
    this.wait_for_answer = data.wait_for_answer;
    this.exclusive_admin = data.exclusive_admin;
  }

  static converter: firestore.FirestoreDataConverter<ChatRoom> = {
    toFirestore(modelObject: ChatRoom): firestore.DocumentData {
      return {
        ...modelObject,
        cityGroupUpdatedAt: modelObject.cityGroupUpdatedAt,
        organizationUpdatedAt: modelObject.organizationUpdatedAt,
        created_at: modelObject.created_at,
        doc_department: modelObject.doc_department,
        dream_vehicle: modelObject.dream_vehicle,
        label: modelObject.label,
        label_updated_at: modelObject.label_updated_at,
        last_inbound: modelObject.last_inbound,
        recent_chat: modelObject.recent_chat && {
          ...modelObject.recent_chat,
          statuses: {
            delivered: modelObject.recent_chat.statuses.delivered,
            failed: modelObject.recent_chat.statuses.failed,
            read: modelObject.recent_chat.statuses.read,
            sent: modelObject.recent_chat.statuses.sent,
          },
        },
        wait_for_answer: {
          ...modelObject.wait_for_answer,
          asked_at: modelObject.wait_for_answer?.asked_at || null,
        },
        exclusive_admin: modelObject.exclusive_admin || null,
      };
    },
    fromFirestore(snapshot: firestore.QueryDocumentSnapshot): ChatRoom {
      const data = snapshot.data() as any;
      return new ChatRoom({
        ...data,
        created_at: data.created_at,
        doc_department: data.doc_department,
        dream_vehicle: data.dream_vehicle,
        label: data.label,
        label_updated_at: data.label_updated_at || null,
        last_inbound: data.last_inbound || null,
        recent_chat: data.recent_chat && {
          ...data.recent_chat,
          statuses: {
            delivered: data.recent_chat.statuses.delivered || null,
            failed: data.recent_chat.statuses.failed || null,
            read: data.recent_chat.statuses.read || null,
            sent: data.recent_chat.statuses.sent || null,
          },
        },
        wait_for_answer: {
          ...data.wait_for_answer,
          asked_at: data.wait_for_answer?.asked_at || null,
        },
        exclusive_admin: data.exclusive_admin || null,
        ref: snapshot.ref,
      });
    },
  };
}

export default ChatRoom;
