import { BigQueryTimestamp } from '@google-cloud/bigquery';

export interface ISpkToLeadsTable {
  uuid: string;
  firstName: string;
  lastName: string | null;
  phoneNumber: string;
  domicile: {
    provinceName: string;
    provinceCode: string;
    cityName: string;
    cityCode: string;
  };
  vehicles: {
    brand: {
      code: string;
    };
    model: {
      name: string;
    };
    variant: {
      name: string;
      code: string;
    };
    color: {
      name: string;
      code: string;
    };
  }[];
  domicileSpk: {
    provinceName: string;
    provinceCode: string;
    cityName: string;
    cityCode: string;
  };
  vehicleSpk: {
    brand: {
      code: string;
    };
    model: {
      name: string;
    } | null;
    variant: {
      name: string;
      code: string;
    } | null;
    color: {
      name: string;
      code: string;
    } | null;
  } | null;
  spkNumber: string;
  agentName: string;
  agentCode: string;
  source: string;
  organization: string;
  paymentPlan: string;
  createdAt: BigQueryTimestamp;
}
