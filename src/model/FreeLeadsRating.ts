import { firestore } from 'firebase-admin';
import { IFreeLeadsRatingDocumentModel } from '../types/firestore/free_leads_rating.types';

class FreeLeadsRating implements IFreeLeadsRatingDocumentModel {
  REF: firestore.DocumentReference;
  agentName: string;
  agentCode: string;
  organization: string;
  rating: number;
  createdAt: firestore.Timestamp;

  constructor(data: IFreeLeadsRatingDocumentModel) {
    this.REF = data.REF;
    this.agentName = data.agentName;
    this.agentCode = data.agentCode;
    this.organization = data.organization;
    this.rating = data.rating;
    this.createdAt = data.createdAt;
  }

  static converter: firestore.FirestoreDataConverter<FreeLeadsRating> = {
    toFirestore(modelObject: FreeLeadsRating): firestore.DocumentData {
      return {
        agentName: modelObject.agentName,
        agentCode: modelObject.agentCode,
        organization: modelObject.organization,
        rating: modelObject.rating,
        createdAt: modelObject.createdAt,
      };
    },
    fromFirestore(snapshot: firestore.QueryDocumentSnapshot): FreeLeadsRating {
      const data = snapshot.data() as IFreeLeadsRatingDocumentModel;
      return new FreeLeadsRating({
        REF: snapshot.ref,
        agentName: data.agentName,
        agentCode: data.agentCode,
        organization: data.organization,
        rating: data.rating,
        createdAt: data.createdAt,
      });
    },
  };
}

export default FreeLeadsRating;
