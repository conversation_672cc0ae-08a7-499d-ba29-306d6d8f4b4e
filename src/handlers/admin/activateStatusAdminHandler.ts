import { json, RequestHand<PERSON> } from 'express';
import { myAuth, myFirestore } from '../../services/firebaseAdmin';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import authChecker from '../../middlewares/authChecker';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import AdminModel from '../../model/AdminModel';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';

interface ReqBody {
  status: 'enable' | 'disable';
  adminPath: string;
}

const activateStatusAdminHandler: {
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
  middlewares: RequestHandler[];
} = {
  middlewares: [
    json(),
    authChecker,
    body('status').notEmpty().isIn(['enable', 'disable']),
    body('adminPath').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const adminDoc = myFirestore.doc(req.body.adminPath);
    const get = await adminDoc.withConverter(AdminModel.converter).get();

    const adminData = get.data();

    if (!adminData) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Admin tidak terdaftar',
        })
      );
    }

    try {
      await myAuth().updateUser(adminData.doc_admin.id, {
        disabled: req.body.status === 'disable',
      });
    } catch (e) {
      console.log(e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi Error Pada Firebase. Tidak dapat mengaktifkan atau menonaktifkan',
        })
      );
    }

    switch (req.body.status) {
      case 'enable':
        adminData.active = true;
        break;
      case 'disable':
        adminData.active = false;
        break;
    }

    try {
      await adminDoc.withConverter(AdminModel.converter).set(adminData);
    } catch (e) {
      console.log(e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi Error. Tidak dapat mengaktifkan atau menonaktifkan',
        })
      );
    }

    return res.send(
      successResponse({
        data: null,
      })
    );
  },
};

export default activateStatusAdminHandler;
