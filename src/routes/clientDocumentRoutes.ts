import { Router } from 'express';
import SetIdCardHandler from '../handlers/client/document/SetIdCardHandler';
import SetFamilyRegisterHandler from '../handlers/client/document/SetFamilyRegisterHandler';
import SetSelfieHandler from '../handlers/client/document/SetSelfieHandler';
import SetPlaceToStayHandler from '../handlers/client/document/SetPlaceToStayHandler';
import SetPlaceOfBusiness from '../handlers/client/document/SetPlaceOfBusinessHandler';
import SetBusinessDocument from '../handlers/client/document/SetBusinessDocument';
import SetIncomeDocumentHandler from '../handlers/client/document/SetIncomeDocumentHandler';
import SetOtherDocumentHandler from '../handlers/client/document/SetOtherDocumentHandler';
import SetIdCardSpouseGuarantorHandler from '../handlers/client/document/SetIdCardSpouseGuarantorHandler';
import SetIdCardV2Handler from '../handlers/client/document/SetIdCardHandler.v2';
import SetFamilyRegisterV2Handler from '../handlers/client/document/SetFamilyRegisterHandler.v2';
import SetSelfieV2Handler from '../handlers/client/document/SetSelfieHandler.v2';
import addNewOwnedVehicle from '../handlers/client/addNewOwnedVehicle';

const router = Router();

export default function (parent: Router) {
  parent.use('/client/document', router);
  router.post('/set-id-card', SetIdCardHandler.middlewares, SetIdCardHandler.handler);
  router.post(
    '/set-id-card-spouse-guarantor',
    SetIdCardSpouseGuarantorHandler.middlewares,
    SetIdCardSpouseGuarantorHandler.handler
  );
  router.post(
    '/set-family-register',
    SetFamilyRegisterHandler.middlewares,
    SetFamilyRegisterHandler.handler
  );
  router.post('/set-selfie', SetSelfieHandler.middlewares, SetSelfieHandler.handler);
  router.post(
    '/set-place-to-stay',
    SetPlaceToStayHandler.middlewares,
    SetPlaceToStayHandler.handler
  );
  router.post('/set-place-of-business', SetPlaceOfBusiness.middlewares, SetPlaceOfBusiness.handler);
  router.post(
    '/set-business-document',
    SetBusinessDocument.middlewares,
    SetBusinessDocument.handler
  );
  router.post(
    '/set-income-document',
    SetIncomeDocumentHandler.middlewares,
    SetIncomeDocumentHandler.handler
  );
  router.post(
    '/set-other-document',
    SetOtherDocumentHandler.middlewares,
    SetOtherDocumentHandler.handler
  );

  router.post('/v2/set-id-card', SetIdCardV2Handler.middlewares, SetIdCardV2Handler.handler);
  router.post(
    '/v2/set-family-register',
    SetFamilyRegisterV2Handler.middlewares,
    SetFamilyRegisterV2Handler.handler
  );
  router.post('/v2/set-selfie', SetSelfieV2Handler.middlewares, SetSelfieV2Handler.handler);

  router.post('/add-owned-vehicle', addNewOwnedVehicle.middlewares, addNewOwnedVehicle.handler);
}
