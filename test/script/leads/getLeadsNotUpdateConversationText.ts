import { myFirestore } from '../../../src/services/firebaseAdmin';
import dayjs from 'dayjs';
import LeadsModel from '../../../src/model/LeadsModel';
import { collect } from 'collect.js';
import { ILeadsNotesDocument } from '../../../src/types/firestore/leads_notes.types';
import ColdLeadsModel from '../../../src/model/ColdLeadsModel';
import aggregatesAgent from '../../../src/helpers/leads/aggregatesAgent';

const getLeadsNotUpdateConversationText = async () => {
  const coldLeadsCollection = myFirestore.collection('cold_leads');
  const coldLeadsHistoriesCollection = myFirestore.collection('cold_leads_histories');

  const offset = dayjs().subtract(30, 'days');
  console.log(offset.format('YYYY-MM-DD'));
  const collection = myFirestore.collection('leads');
  const get = await collection
    .where('createdAt', '<', offset.toDate())
    // .where("createdAt", ">", offset.subtract(2, "days").toDate())
    .orderBy('createdAt', 'desc')
    .withConverter(LeadsModel.converter)
    .get();

  const allLeads: LeadsModel[] = [];

  get.forEach(result => {
    allLeads.push(result.data());
  });

  let leadsNotUpdateFileWa: LeadsModel[] = [];
  for (const lead of allLeads) {
    const waConversations = lead.agentWhatsappConversations;
    if (waConversations.length > 0) {
      const lastFile = waConversations[waConversations.length - 1];
      const createdFile = dayjs(lastFile.createdAt);

      const beforeOffset = createdFile.isBefore(offset);
      if (beforeOffset) {
        leadsNotUpdateFileWa.push(lead);
      }
    } else if (waConversations.length === 0) {
      leadsNotUpdateFileWa.push(lead);
    }

    // console.log(dayjs(lead.createdAt).format("YYYY-MM-DD"))
  }

  const collectLeads = collect(leadsNotUpdateFileWa);

  const groupByAgentCode = collectLeads.groupBy('agentCode');

  const agentCodes: string[] = groupByAgentCode.keys().toArray();

  const groupByOrganization = collectLeads.groupBy('organization');

  const organizations: string[] = groupByOrganization.keys().toArray();

  const chunkLeads = collectLeads.chunk(100);

  for (const leads of chunkLeads.all()) {
    const batch = myFirestore.batch();
    for (const lead of leads) {
      let notes: ILeadsNotesDocument<Date> = {
        agentCode: lead.agentCode,
        event: 'moveToCold',
        notes:
          'Move to Cold System - Move to Cold karena tidak ada update percakapan lebih dari 30 Hari',
        organization: lead.organization,
        phoneNumber: lead.phoneNumber,
        statusLevel: lead.statusLevel,
        updatedAt: new Date(),
        updatedByUser: null,
        firstName: lead.firstName,
        lastName: lead.lastName,
        agentName: lead.agentName,
        moveToCold: true,
        reactivate: {
          currentTotal: 0,
        },
        totalUpdateNotes: lead.updateHistories.length + 1,
      };
      lead.updateHistories.push(notes);
      lead.isTracking = false;

      const coldLeads = new ColdLeadsModel({
        ...lead,
        tradeIn: null,
        moveToColdAt: new Date(),
        moveToColdBy: 'System',
        coldNotes: notes.notes,
        moveToColdByUserType: 'system',
      });

      const docName = `${lead.organization}-${lead.phoneNumber}`;

      batch.set(
        coldLeadsCollection.doc(docName).withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.set(
        coldLeadsHistoriesCollection.doc().withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.set(myFirestore.collection('leads_notes').doc(), notes);
      batch.delete(lead.ref);
    }
    await batch.commit();
  }

  for (const agentCode of agentCodes) {
    for (const organization of organizations) {
      await aggregatesAgent(agentCode, organization);
    }
  }
};

getLeadsNotUpdateConversationText();
