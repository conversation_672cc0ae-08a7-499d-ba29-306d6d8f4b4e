import { json, Request, Response } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../middlewares/requestValidator';
import moment from 'moment';
import { myFirestore } from '../../services/firebaseAdmin';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import leadsAuthorizerExternal from '../../middlewares/leadsAuthorizerExternal';
import { ILeadsDocument, LeadsVehicleOptions } from '../../types/firestore/leads_model.types';
import FreeLeadsModel from '../../model/FreeLeadsModel';
import { firestore } from 'firebase-admin';
import { RawLeadsTable } from '../../types/services/bigQuery/rawLeadsTable.types';
import { bigQuery, rawLeadsTable } from '../../services/bigQueryService';
import { v4 as uuidV4 } from 'uuid';
import sendTemplate from '../../services/sendTemplate/sendTemplate';

interface ReqBody {
  externalId: string;
  phoneNumber: string;
  fullName: string;
  city: string;
  vehicleModel: string;
  vehicleVariant: string;

  organization: string;

  externalData: {
    vehicle: {
      variantId: string;
    };
    city: {
      slug: string;
      name: string;
    };
  };
}

const AddRawLeadsOtocom = {
  middlewares: [
    leadsAuthorizerExternal,
    json(),
    body('fullName').notEmpty(),
    body('phoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .customSanitizer(input => {
        return phoneNumberCountryCodeSanitizer(input, '62', '62');
      }),
    body('vehicleModel').notEmpty(),
    body('vehicleVariant').notEmpty(),
    body('city').notEmpty(),
    body('organization').notEmpty(),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response<ResponseSchema>) => {
    const rawLeadsCollection = myFirestore.collection('raw_leads');
    const freeLeadsCollection = myFirestore.collection('free_leads');
    const leadsCollection = myFirestore.collection('leads');

    const otocomCityCollection = myFirestore.collection('/master/otocom/cities');
    const otocomVehicleCollection = myFirestore.collection('/master/otocom/vehicles');

    const now = moment();

    const getVehicle = await otocomVehicleCollection
      .doc(req.body.externalData.vehicle.variantId?.toString())
      .get();
    let vehicle: LeadsVehicleOptions | null = null;
    if (getVehicle.exists) {
      const vehicleMasterData: any = getVehicle.data();
      vehicle = {
        color: {
          code: '',
          name: '',
        },
        brand: {
          name: vehicleMasterData.trimitraBrandName,
        },
        variant: {
          name: vehicleMasterData.trimitraVariantName,
          code: vehicleMasterData.trimitraVariantCode,
        },
        model: {
          name: vehicleMasterData.trimitraModelName,
        },
      };
    } else {
      console.log('OTOCOM_VEHICLE_NOT_MAPPED', JSON.stringify(req.body));
    }

    const getCity = await otocomCityCollection
      .where('otoComName', '==', req.body.externalData.city.name)
      .get();

    let city: ILeadsDocument['domicile'] | null = null;
    let area: string | null = null;
    if (!getCity.empty) {
      getCity.forEach(result => {
        const data = result.data() as any;
        city = {
          provinceName: data.trimitraProvinceName,
          provinceCode: data.trimitraProvinceCode,
          cityName: data.trimitraCityName,
          cityCode: data.trimitraCityCode,
        };

        area = !data.trimitraCityGroup ? null : data.trimitraCityGroup;
      });
    }

    const acquiredLeads = await leadsCollection
      .doc(`${req.body.organization}-${req.body.phoneNumber}`)
      .get();

    const findInRawLeads = await rawLeadsCollection
      .where('phoneNumber', '==', req.body.phoneNumber)
      .get();

    const rawLeadsData: any = {
      ...req.body,
      source: 'otocom',

      inFreeLeads: false,
      shouldPushToFreeLeadsAt: now.clone().add(15, 'minutes').toDate(),
      freeLeadsPath: null,
      freeLeadsCreatedAt: null,

      shouldPushToBigQueryAt: now.clone().add(15, 'minutes').toDate(),
      donePushToBigQuery: false,
      donePushToBigQueryAt: null,

      createdAt: now.toDate(),

      duplicated: !findInRawLeads.empty,
      mappedData: {
        domicile: city,
        vehicle: vehicle,
        area: area,
        paymentPlan: null,
      },

      whatsapp: null,

      alreadyInMyLeads: acquiredLeads.exists,
    };

    const rawLeadsDocRef = rawLeadsCollection.doc();

    try {
      await rawLeadsDocRef.set(rawLeadsData);
    } catch (error: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: error.toString(),
        })
      );
    }

    const freeLeadsDocName = `${req.body.organization}-${req.body.phoneNumber}`;
    let freeLeadsDocRef!: firestore.DocumentReference;
    const findFreeLeads = await freeLeadsCollection
      .doc(freeLeadsDocName)
      .withConverter(FreeLeadsModel.converter)
      .get();

    if (findFreeLeads.exists) {
      freeLeadsDocRef = findFreeLeads.ref;
    } else {
      freeLeadsDocRef = freeLeadsCollection.doc(freeLeadsDocName);
    }

    if (!acquiredLeads.exists) {
      const price = rawLeadsData.organization === 'amartahonda' ? 20000 : 50000;
      const freeLeads = new FreeLeadsModel({
        rawLeadsRef: rawLeadsDocRef,
        area: rawLeadsData.mappedData?.area ?? null,
        externalId: rawLeadsData.externalId ?? null,
        title: null,
        firstName: rawLeadsData.fullName,
        lastName: '',
        phoneNumber: rawLeadsData.phoneNumber,
        paymentPlan: null,
        hasVehicleLoan: false,
        createdAt: now.toDate(),

        domicile: rawLeadsData.mappedData?.domicile ?? null,

        driverLicense_number: null,
        email: null,
        idCard_number: null,
        nextTotalVehicleOwnerShip: null,
        organization: rawLeadsData.organization,
        purchasePlan: null,
        source: 'otocom',
        vehicleOptions: rawLeadsData.mappedData?.vehicle ? [rawLeadsData.mappedData.vehicle] : [],
        vehicleUsage: null,
        ref: freeLeadsDocRef,

        isAcquired: false,
        acquiredAt: null,
        acquiredAgentCode: null,
        acquiredAgentName: null,

        price,
        ideal: null,
      });

      try {
        const batchAddFreLeads = myFirestore.batch();

        batchAddFreLeads.set(freeLeadsDocRef.withConverter(FreeLeadsModel.converter), freeLeads);
        batchAddFreLeads.update(rawLeadsDocRef, {
          inFreeLeads: true,
          freeLeadsPath: freeLeadsDocRef,
          freeLeadsCreatedAt: now.toDate(),
        });

        await batchAddFreLeads.commit();
      } catch {
        console.log('FAILED_INSERT_RAW_LEADS_TO_FREE_LEADS', rawLeadsData.phoneNumber);
      }
    }

    try {
      const uuid = uuidV4();
      const bigQueryRow: RawLeadsTable = {
        city: rawLeadsData.city,
        city_group: rawLeadsData.mappedData.area,
        duplicated: rawLeadsData.duplicated,
        external_id: rawLeadsData.externalId,
        full_name: rawLeadsData.fullName,
        organization: rawLeadsData.organization,
        phone_number: rawLeadsData.phoneNumber,
        source: rawLeadsData.source,

        trimitra_city: rawLeadsData.mappedData.domicile
          ? {
              code: rawLeadsData.mappedData.domicile.cityCode,
              name: rawLeadsData.mappedData.domicile.cityName,
            }
          : null,
        trimitra_province: rawLeadsData.mappedData.domicile
          ? {
              code: rawLeadsData.mappedData.domicile.provinceCode,
              name: rawLeadsData.mappedData.domicile.provinceName,
            }
          : null,

        trimitra_vehicle: rawLeadsData.mappedData.vehicle
          ? {
              model: rawLeadsData.mappedData.vehicle.model,
              variant: rawLeadsData.mappedData.vehicle.variant,
              color: null,
            }
          : null,

        uid: uuid,
        vehicle_model: rawLeadsData.vehicleModel,
        vehicle_variant: rawLeadsData.vehicleVariant,
        whatsapp_delivered: !!rawLeadsData.whatsapp?.statuses.delivered,
        whatsapp_delivered_at_timestamp: rawLeadsData.whatsapp?.statuses.delivered
          ? bigQuery.timestamp(rawLeadsData.whatsapp.statuses.delivered.toDate())
          : null,
        data_leads_created_at_timestamp: bigQuery.timestamp(rawLeadsData.createdAt),
        bq_created_at_datetime: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
        bq_created_at_timestamp: bigQuery.timestamp(now.toDate()),
        is_in_my_leads: rawLeadsData.alreadyInMyLeads ?? false,
      };
      await rawLeadsTable.insert(bigQueryRow);
      await rawLeadsDocRef.update({
        donePushToBigQuery: true,
        donePushToBigQueryAt: now.toDate(),
      });
    } catch {
      console.log('FAILED_INSERT_RAW_LEADS_TO_BIG_QUERY', rawLeadsData.phoneNumber);
    }

    if (!acquiredLeads.exists && req.body.organization === 'amartahonda') {
      try {
        sendTemplate
          .sendTemplateMetaV2({
            projectId: 'WLdKug7hau0MbRzKcnqg',
            bindDocuments: [
              {
                path: rawLeadsDocRef.path,
                context: 'add_raw_leads',
              },
              {
                path: freeLeadsDocRef.path,
                context: 'add_leads',
              },
            ],
            template_name: 'leads1',
            components: [
              {
                type: 'body',
                parameters: [
                  {
                    type: 'text',
                    text: req.body.fullName.toUpperCase(),
                  },
                  {
                    type: 'text',
                    text: 'OTOCOM',
                  },
                  {
                    type: 'text',
                    text: rawLeadsData.mappedData.vehicle.model.name,
                  },
                ],
              },
            ],
            contactName: req.body.fullName,
            target: req.body.phoneNumber,
            area: area || undefined,
            vehicle: {
              model_name: vehicle?.model.name || '',
              variant_name: vehicle?.variant.name || '',
              variant_code: vehicle?.variant.code || '',
              color_code: vehicle?.color.code || '',
              color_name: vehicle?.color.name || '',
              year: '',
            },
          })
          .then()
          .catch(reason => {
            throw reason;
          });
      } catch {
        console.log('FAILED_SEND_TEMPLATE_LEADS', JSON.stringify(req.body));
      }
    }

    res.send(
      successResponse({
        data: {
          ...rawLeadsData,
          id: rawLeadsDocRef.id,
        },
        type: 'CREATED',
      })
    );
  },
};

export default AddRawLeadsOtocom;
