// AES encryption/decryption using Node.js crypto module
import crypto from 'crypto';
import { test } from '@jest/globals';

// Configuration
const algorithm = 'aes-256-cbc';
const secretKey = process.env.ENCRYPTION_KEY || 'vOVH6sdmpNWjRRIqCc7rdxs01lwHzfr3'; // 32 bytes key

/**
 * Encrypts text using AES-256-CBC algorithm
 * @param text - Plain text to encrypt
 * @returns Encrypted text in format: iv:encryptedData (base64 encoded)
 */
const encrypt = (text: string): string => {
  // Generate a new random IV for each encryption
  const iv = crypto.randomBytes(16);

  // Create cipher
  const cipher = crypto.createCipheriv(algorithm, Buffer.from(secretKey), iv);

  // Encrypt the text
  let encrypted = cipher.update(text, 'utf8', 'base64');
  encrypted += cipher.final('base64');

  // Return iv + encrypted data (both base64 encoded)
  return `${iv.toString('base64')}:${encrypted}`;
};

/**
 * Decrypts text using AES-256-CBC algorithm
 * @param text - Encrypted text in format: iv:encryptedData (base64 encoded)
 * @returns Decrypted plain text
 */
const decrypt = (text: string): string => {
  // Split iv and encrypted data
  const [ivPart, encryptedText] = text.split(':');

  // Convert iv from base64 to Buffer
  const ivBuffer = Buffer.from(ivPart, 'base64');

  // Create decipher
  const decipher = crypto.createDecipheriv(algorithm, Buffer.from(secretKey), ivBuffer);

  // Decrypt the text
  let decrypted = decipher.update(encryptedText, 'base64', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
};

export { encrypt, decrypt };
