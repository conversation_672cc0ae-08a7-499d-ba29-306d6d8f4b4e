import { Request, Response } from 'express';
import successResponse from '../../schema/successResponse';
import authChecker from '../../middlewares/authChecker';

export default class AccountHandler {
  static middlewares = [authChecker];

  static async handler(req: Request, res: Response) {
    const data = res.locals.account.data();
    res.send(
      successResponse({
        data: {
          ...res.locals.account.data(),
          doc_project: data.doc_project?.path,
          doc_department: data.doc_department?.path ?? null,
          created_time: data.created_time.toDate(),
          doc_admin: res.locals.account.path,
        },
      })
    );
  }
}
