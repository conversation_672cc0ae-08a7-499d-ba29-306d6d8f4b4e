import { LeadsUpdateEvent } from './leads_model.types';
import { firestore } from 'firebase-admin';

export interface ILeadsNotesDocument<TIMESTAMP = firestore.Timestamp> {
  moveToCold: boolean;

  notes: string;
  updatedAt: TIMESTAMP;
  updatedByUser: string | null;
  statusLevel: number;
  event: LeadsUpdateEvent;

  phoneNumber: string;
  organization: string;

  firstName: string;
  lastName: string;

  agentCode: string;
  agentName: string;

  reactivate?: {
    currentTotal: number;
  };

  followUpScheduledAt?: TIMESTAMP | null;

  totalUpdateNotes: number;
}
