import { json, Request, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import successResponse from '../../schema/successResponse';

interface ReqBody {
  leadsPhoneNumber: string;
  organization: string;
  followUp: boolean;
}

const updateFollowUpStatus = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('leadsPhoneNumber').notEmpty(),
    body('organization').notEmpty(),
    body('followUp').notEmpty().isBoolean().toBoolean(),
    requestValidator,
  ],
  handler: async function (req: Request<any, ResponseSchema, ReqBody>, res: Response) {
    const collection = myFirestore.collection('leads');
    const getLeads = await collection
      .withConverter(LeadsModel.converter)
      .doc(`${req.body.organization}-${req.body.leadsPhoneNumber}`)
      .get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    if (!leads.followUp) {
      if (req.body.followUp) {
        leads.followUp = true;
        leads.followUpRequestedAt = new Date();
      }
    } else {
      if (!req.body.followUp) {
        leads.followUp = false;
        leads.followUpRequestedAt = null;
      }
    }

    try {
      await leads.ref.withConverter(LeadsModel.converter).set(leads);
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: `Error: ${e.toString()}`,
        })
      );
    }
  },
};

export default updateFollowUpStatus;
