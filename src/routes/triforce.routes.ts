import { Router } from 'express';
import AddOrderHistory from '../handlers/tri-froce/AddOrderHistory';
import getAvailableLeadsV2 from '../handlers/tri-froce/v2/getAvailableLeads';
import updateNotes from '../handlers/tri-froce/v2/updateNotes';
import getLeads from '../handlers/tri-froce/v2/getLeads';
import addLeadHandler from '../handlers/tri-froce/v2/AddLeadHandler';
import notifyFollowUpStarted from '../handlers/tri-froce/v2/notifyFollowUpStarted';
import reactivateTracking from '../handlers/tri-froce/v2/ReactivateTracking';
import updateIdCard from '../handlers/tri-froce/v2/updateIdCard';
import MoveLeadsToCold from '../handlers/tri-froce/v2/MoveLeadsToCold';
import GetFreeLeads from '../handlers/tri-froce/v2/GetFreeLeads';
import LeadsAcquisition from '../handlers/tri-froce/v2/leadsAcquisition';
import updateLeads from '../handlers/tri-froce/v2/updateLeads';
import triforceCheckVersionV2 from '../middlewares/triforce/triforceCheckVersionV2';
import checkPhoneNumberStates from '../handlers/leadsApiExternal/checkPhoneNumberStates';
import setLeadsOfferCode from '../handlers/tri-froce/v2/setLeadsOfferCode';
import feedBackPhoneNumber from '../handlers/tri-froce/v2/feedBackPhoneNumber';
import addLeadHandlerV2 from '../handlers/tri-froce/v2/AddLeadHandler.v2';
import updateLeadsConversations from '../handlers/tri-froce/v2/updateLeadsConversations';
import AddLeadsB2bCreateSurveyOrder from '../handlers/tri-froce/v2/AddLeadsB2bCreateSurveyOrder';
import generateAgentRefCodeHandler from '../handlers/tri-froce/v2/generateAgentRefCodeHandler';
import getAgentRefCodeHandler from '../handlers/tri-froce/v2/getAgentRefCodeHandler';
import GetFreeLeadsChat from '../handlers/tri-froce/v2/GetFreeLeadsChat';
import getLeadsTradeIn from '../handlers/tri-froce/v2/GetLeadsTradeIn';
import AddLeadsTradeIn from '../handlers/tri-froce/v2/AddLeadsTradeIn';

const router = Router();

export default function (parent: Router) {
  router.use(triforceCheckVersionV2);

  parent.use('/tri-force', router);

  router.get('/is-up-to-date', (req, res) => {
    res.send({
      minTriforceBuildVersion: process.env.TRIFORCE_MIN_VERSION,
    });
  });

  router.post('/order-history', AddOrderHistory.middlewares as any, AddOrderHistory.handler);

  router.get(
    '/v2/get-leads/:organization/:agentCode',
    getAvailableLeadsV2.middlewares,
    getAvailableLeadsV2.handler
  );

  router.get(
    '/v2/get-specific-leads/:organization/:phoneNumber',
    getLeads.middlewares,
    getLeads.handler
  );

  router.post('/v2/update-notes', updateNotes.middlewares, updateNotes.handler);

  router.post('/v2/add-leads', addLeadHandler.middlewares, addLeadHandler.handler);

  router.post(
    '/v2/update-leads-conversation',
    updateLeadsConversations.middlewares,
    updateLeadsConversations.handler
  );

  router.post('/v2/add-leads-v2', addLeadHandlerV2.middlewares, addLeadHandlerV2.handler);

  router.post(
    '/v2/notify-leads-start-follow-up',
    notifyFollowUpStarted.middlewares,
    notifyFollowUpStarted.handler
  );

  router.post(
    '/v2/request-reactivate-tracking',
    reactivateTracking.middlewares,
    reactivateTracking.handler
  );

  router.post('/v2/update-id-card', updateIdCard.middlewares, updateIdCard.handler);

  router.post('/v2/move-to-cold', MoveLeadsToCold.middlewares, MoveLeadsToCold.handler);

  router.get(
    '/v2/get-free-leads/:organization',
    GetFreeLeads.middlewares,
    GetFreeLeads.handler as any
  );

  router.post(
    '/v2/free-leads-acquisition',
    LeadsAcquisition.middlewares,
    LeadsAcquisition.handler as any
  );

  router.post('/v2/update-leads', updateLeads.middlewares, updateLeads.handler);

  router.post('/v2/set-offer-code', setLeadsOfferCode.middlewares, setLeadsOfferCode.handler);

  router.post('/v2/update-feedback', feedBackPhoneNumber.middlewares, feedBackPhoneNumber.handler);

  router.post(
    '/v2/add-b2b-create-survey-order',
    AddLeadsB2bCreateSurveyOrder.middlewares,
    AddLeadsB2bCreateSurveyOrder.handler
  );

  router.get(
    '/v2/check-phone-number',
    checkPhoneNumberStates.middlewares,
    checkPhoneNumberStates.handler
  );

  router.post(
    '/v2/generate-agent-ref-code',
    generateAgentRefCodeHandler.middlewares as any,
    generateAgentRefCodeHandler.handler
  );

  router.get(
    '/v2/get-agent-ref-code',
    getAgentRefCodeHandler.middlewares,
    getAgentRefCodeHandler.handler
  );

  router.get('/get-free-leads-chat', GetFreeLeadsChat.middlewares, GetFreeLeadsChat.handler);

  router.get(
    '/v2/get-leads-trade-in/:agentCode/:organization/:phoneNumber',
    getLeadsTradeIn.middlewares,
    getLeadsTradeIn.handler
  );

  router.post(
    '/v2/add-leads-trade-in',
    AddLeadsTradeIn.middlewares,
    AddLeadsTradeIn.handler
  );
}
