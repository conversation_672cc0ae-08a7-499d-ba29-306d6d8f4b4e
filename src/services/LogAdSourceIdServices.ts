import axios from 'axios';
import { ILogAdSourceIdServiceParamsTypes } from '../types/LogAdSorceIdService.types';

class LogAdSourceIdServices {
  private key = '5ukoYcllpl6lIeKsbeIPI4hOZGDszFVk1dDBddHi';

  public logDealCodeActivity(params: ILogAdSourceIdServiceParamsTypes) {
    return axios.post(
      'https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1/deal/activity/aggregate',
      params,
      {
        headers: {
          'x-api-key': this.key,
        },
      }
    );
  }
}

const logAdSourceIdServices = new LogAdSourceIdServices();

export default logAdSourceIdServices;
