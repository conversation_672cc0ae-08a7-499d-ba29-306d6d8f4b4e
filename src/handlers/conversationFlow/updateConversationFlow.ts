import { json, RequestHandler } from 'express';
import { IConversationFlow } from '../../types/conversationFlow/conversationFlow.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import moment from 'moment';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import { firestore } from 'firebase-admin';
import getDealCodeData, { ResponseGetDealCode } from '../../helpers/getDealCodeData';

interface RequestBody extends IConversationFlow {
  pathConversationFlow: string;
  projectId: string;
}

const addConversationFlow: {
  handler: RequestHandler<any, ResponseSchema, RequestBody>;
  middlewares: RequestHandler[];
} = {
  middlewares: [
    json(),
    body('pathConversationFlow').notEmpty(),
    body('projectId').notEmpty(),
    body('referralSourceId.sourceId')
      .if((input, meta) => {
        return meta.req.body.startAt === 'referralSourceId';
      })
      .notEmpty(),
    body('mediaTrimitraArt.artCode')
      .if((input, meta) => {
        return meta.req.body.startAt === 'mediaTrimitraArt';
      })
      .trim()
      .notEmpty()
      .withMessage('Ad Art Code diperlukan'),
    body('internalTemplate.templateId')
      .if((input, meta) => {
        return meta.req.body.startAt === 'internalTemplate';
      })
      .trim()
      .notEmpty()
      .withMessage('Ad Template Id diperlukan'),
    body('referralSourceId.dealCode').optional({ values: 'falsy' }),
    body('messages.*.button.buttons.*.text')
      .if((input, meta) => {
        const matches = meta.path.match(/messages\[(\d+)\]/);
        const messageIndex = matches ? parseInt(matches[1]) : 0;
        return meta.req.body.messages[messageIndex].type === 'button';
      })
      .optional({ values: 'falsy' }) // akan mengabaikan string kosong
      .isLength({ min: 1, max: 20 })
      .withMessage((value, { path }) => {
        const matches = path.match(/messages\[(\d+)\].*buttons\[(\d+)\]/);
        const messageIndex = matches ? matches[1] : '0';
        const buttonIndex = matches ? matches[2] : '0';
        return `Button Text Minimal 1 s.d 20 karakter (Pesan ke-${parseInt(messageIndex) + 1}, Button ke-${parseInt(buttonIndex) + 1})`;
      }),
    body('messages.*.button.buttons.*.id')
      .if((input, meta) => {
        const matches = meta.path.match(/messages\[(\d+)\]/);
        const messageIndex = matches ? parseInt(matches[1]) : 0;
        return meta.req.body.messages[messageIndex].type === 'button';
      })
      .optional({ values: 'falsy' }) // akan mengabaikan string kosong
      .isLength({ min: 1, max: 20 })
      .withMessage((value, { path }) => {
        const matches = path.match(/messages\[(\d+)\].*buttons\[(\d+)\]/);
        const messageIndex = matches ? matches[1] : '0';
        const buttonIndex = matches ? matches[2] : '0';
        return `Button Id Minimal 1 s.d 20 karakter (Pesan ke-${parseInt(messageIndex) + 1}, Button ke-${parseInt(buttonIndex) + 1})`;
      }),
    requestValidator,
  ],
  handler: async (req, res) => {
    const projectCollection = myFirestore.collection('projects');
    const projectDocument = projectCollection.doc(req.body.projectId);
    const convFlowCollection = projectDocument.collection('conversation_flow');
    const docRef = myFirestore.doc(req.body.pathConversationFlow);

    const find = await docRef.get();

    if (!find.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Conversation flow tidak ditemukan',
        })
      );
    }

    let dataDealCode: ResponseGetDealCode['data'] | null = null;
    const data = find.data() as IConversationFlow;

    if (data.startAt === 'referralSourceId' && !req.body.referralSourceId.dealCode) {
      const findPriceListMessageType = req.body.messages.find(m => m.type === 'priceList');
      if (findPriceListMessageType) {
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: 'Deal Code dibutuhkan jika ada pesan dengan tipe pricelist',
          })
        );
      }
    }

    if (data.startAt === 'referralSourceId') {
      if (req.body.referralSourceId.dealCode) {
        try {
          const getDealCode = await getDealCodeData(req.body.referralSourceId.dealCode);
          dataDealCode = getDealCode.data.data;
        } catch {
          return res.status(500).send(
            errorResponse({
              type: 'SERVER_ERROR',
              messages: 'Error Ketika Fetch DealCode',
            })
          );
        }
      }

      const findDuplicate = await convFlowCollection
        .where(firestore.FieldPath.documentId(), '!=', find.id)
        .where('startAt', '==', 'referralSourceId')
        .where('referralSourceId.sourceId', '==', req.body.referralSourceId.sourceId)
        .count()
        .get();

      if (findDuplicate.data().count > 0)
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: 'Conversation flow sudah ada',
          })
        );
    }

    const now = moment();

    await docRef.update({
      id: find.id,
      updatedAt: now.toDate(),
      messages: req.body.messages,
      referralSourceId: {
        dealCode: req.body.referralSourceId.dealCode || '',
        sourceId: req.body.referralSourceId.sourceId || '',
      },
      active: req.body.active,
      dealCode: dataDealCode
        ? {
            modelName: dataDealCode.vehicle.model_name.toUpperCase(),
            cityGroup: dataDealCode.area[0].toUpperCase(),
            endPeriod: moment(dataDealCode.end_period).toDate(),
          }
        : null,
    });

    res.send(
      successResponse({
        data: null,
      })
    );
  },
};

export default addConversationFlow;
