import { myFirestore } from '../../../src/services/firebaseAdmin';
import sendTemplate from '../../../src/services/sendTemplate/sendTemplate';

const sendLeadsTemplate = async () => {
  const collection = myFirestore.collection('raw_leads');

  const get = await collection
    .where('phoneNumber', '==', '6285817319676')
    .where('organization', '==', 'amartahonda')
    .get();

  let data: any;

  get.forEach(result => {
    const _data = result.data();
    data = {
      ..._data,
      ref: result.ref,
    };
  });

  try {
    const vehicle = data.mappedData.vehicle;
    const params = {
      bindDocuments: [
        {
          path: data.ref.path,
          context: 'add_raw_leads',
        },
      ],
      template_name: 'leads_welcome_1',
      components: [
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: data.fullName.toUpperCase(),
            },
            {
              type: 'text',
              text: 'OTOCOM',
            },
            {
              type: 'text',
              text: vehicle.model.name,
            },
          ],
        },
      ],
      contactName: data.fullName,
      target: data.phoneNumber,
      area: data.mappedData.area || undefined,
      vehicle: {
        model_name: vehicle?.model.name || '',
        variant_name: vehicle?.variant.name || '',
        variant_code: vehicle?.variant.code || '',
        color_code: vehicle?.color.code || '',
        color_name: vehicle?.color.name || '',
        year: '',
      },
    };

    const send = await sendTemplate.sendTemplateQiscus(params);
    console.log(send);
  } catch (e) {
    console.log(e);
  }
};

sendLeadsTemplate();
