export interface IQiscusSendMessageParams {
  target: string;
  text?: string;
  imageUrl?: string;
  documentUrl?: string;
  documentName?: string;
  imageId?: string;
}

export interface IQiscusSendMessageParamsV2 extends IPostMessageContext {
  to: string;
  type: string;
  preview_url: boolean;
  recipient_type: 'individual' | 'group';
}

export interface IPayloadQiscusPostMessage extends IPostMessageContext {
  to: string;
  recipient_type: 'individual' | 'group';
  type: TAvailablePostMessageContext;
  preview_url: boolean;
}

export interface IPostMessageContext {
  audio?: IFileContextWebhook;
  document?: IFileContextWebhook;
  image?: IFileContextWebhook;
  video?: IFileContextWebhook;
  voice?: IFileContextWebhook;
  system?: ITextContextWebhook;
  text?: ITextContextWebhook;
  context?: IReplyContext;
  sticker?: IStickerContext;
  location?: ILocationContextWebhook;
  interactive?: IInteractiveContextSendMessage;
  button?: any;
}

export type TAvailablePostMessageContext =
  | 'audio'
  | 'document'
  | 'image'
  | 'location'
  | 'system'
  | 'interactive'
  | 'text'
  | 'video'
  | 'voice'
  | 'context'
  | 'button'
  | 'sticker';

export interface IFileContextWebhook {
  id?: string;
  link?: string;
  caption: string;
  filename?: string;
}

export interface ILocationContextWebhook {
  address: string;
  latitude: number;
  longitude: number;
  name: string;
}

export interface ITextContextWebhook {
  body: string;
}

export interface IStickerContext {
  id: string;
  metadata: {
    emojis: string[];
    'is-first-party-sticker': number;
    'sticker-pack-id': string;
  };
  mime_type: string;
  sha256: string;
}

export interface IReplyContext {
  from: string;
  id: string;
  mentions: string[];
}

export interface IInteractiveContextSendMessage {
  type: 'list' | 'button';
  header?: {
    type: 'text' | 'video' | 'image' | 'document';
    text?: string;
    video?: IFileContextWebhook;
    image?: IFileContextWebhook;
    document?: IFileContextWebhook;
  };
  body: {
    text: string;
  };
  footer?: {
    text: string;
  };
  action: {
    button?: string;
    buttons?: {
      type: 'reply';
      reply?: {
        title: string;
        id: string;
      };
    }[];

    sections?: {
      title: string;
      rows: {
        id: string;
        title: string;
        description?: string;
      }[];
    }[];
  };
}

export interface IQiscusResponsePostMessage {
  messages: {
    id: string;
  }[];
  meta: {
    api_status: 'stable';
    version: '2.29.3';
  };
}
