import { myFirestore } from '../../src/services/firebaseAdmin';
import FreeLeadsModel from '../../src/model/FreeLeadsModel';

const countTemplateLogs = async () => {
  const freeLeadsCollection = myFirestore.collection('free_leads');
  const coldLeadsCollection = myFirestore.collection('cold_leads');
  const collectionTemplateLogs = myFirestore.collection('phone_number_crm_template');
  const logOtolmsCollection = myFirestore.collection(
    '/leads_logs/add_free_leads/otocom_upload_file_xls'
  );

  const getOtolmsFreeLeads = await freeLeadsCollection
    .where('source', '==', 'otolms')
    .withConverter(FreeLeadsModel.converter)
    .get();

  const getOtolmsColdLeads = await coldLeadsCollection
    .where('source', '==', 'otolms')
    .withConverter(FreeLeadsModel.converter)
    .get();

  const phoneNumbers: string[] = [];

  getOtolmsFreeLeads.forEach(result => {
    const data = result.data()!;
    phoneNumbers.push(data.phoneNumber);
  });

  getOtolmsColdLeads.forEach(result => {
    const data = result.data()!;
    phoneNumbers.push(data.phoneNumber);
  });

  console.log('SEMUA NOMOR TELEPON DARI OTOLMS', phoneNumbers.length, phoneNumbers);

  const phoneNumbersWhatsappSend: string[] = [];

  for (const phoneNumber of phoneNumbers) {
    const get = await collectionTemplateLogs.where('target_phone_number', '==', phoneNumber).get();

    if (!get.empty) phoneNumbersWhatsappSend.push(phoneNumber);
  }

  console.log('NOMOR YANG DIKIRIM WA', phoneNumbersWhatsappSend.length, phoneNumbersWhatsappSend);

  const duplicatedPhoneNumbers: string[] = [];

  for (const phoneNumber of phoneNumbers) {
    const get = await logOtolmsCollection
      .where('allLeadsPhoneNumbersInFile', 'array-contains', phoneNumber)
      .get();

    if (get.size > 1) {
      duplicatedPhoneNumbers.push(phoneNumber);
    }
  }

  console.log('NOMOR YANG DUPLIKAT', duplicatedPhoneNumbers.length, duplicatedPhoneNumbers);
};

countTemplateLogs();
