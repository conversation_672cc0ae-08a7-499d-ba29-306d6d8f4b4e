import { myAuth, myFirestore } from '../services/firebaseAdmin';
import { NextFunction, Request, Response } from 'express';
import AdminModel from '../model/AdminModel';

export default async function authChecker(req: Request, res: Response, next: NextFunction) {
  let token = req.header('Authorization');

  if (!token) {
    res.status(422).send({
      success: false,
      data: 'NOT_AUTHENTICATED',
      messages: 'Bearer Token tidak ditemukan',
    });
  } else
    try {
      token = token.replace('Bearer ', '');
      const log = await myAuth().verifyIdToken(token.replace('Bearer ', ''));
      const account = await myFirestore
        .collection('admins')
        .doc(log.uid)
        .withConverter(AdminModel.converter)
        .get();
      if (!account.exists) {
        throw new Error('NOT_AUTHENTICATED');
      }

      res.locals.account = account.data()!;
      next();
    } catch (e: any) {
      const errorMessage = e.message ?? 'Bearer token tidak dapat di autentikasi';
      res.status(422).send({
        success: false,
        data: 'NOT_AUTHENTICATED',
        messages: errorMessage,
      });
    }
}
