import axios, { isAxiosError } from 'axios';
import {
  AdsPackageResponse,
  IdealBypassLoginResponse,
  LeadCheckoutResponse,
  PlanCheckoutResponse,
  PlanCheckoutStatusResponse,
} from './amartavip.services.types';

class AmartavipService {
  private email = '';
  private password = '74064f31-377a-4ae0-a369-b61ea5da2472';
  private baseUrl = 'https://3krjkubmjk.execute-api.ap-southeast-3.amazonaws.com/v1';
  private apiKey = 'NqWjTj6qpI7LK8JUKvkQz3isRY9NGU7faeWwv2ff';
  private baseAxios = axios.create({
    baseURL: this.baseUrl,
    headers: {
      'Content-Type': 'application/json',
      'x-api-key': this.apiKey,
    },
  });

  private handleAxiosError(error: unknown, context: string): never {
    if (isAxiosError(error)) {
      console.log(`Error during ${context}:`);
      console.log('HTTP Method:', error.config?.method);
      console.log('Base URL:', error.config?.baseURL);
      console.log('Endpoint:', error.config?.url);
      console.log('Headers:', error.config?.headers);
      console.log('Request Body:', error.config?.data);
      console.log('Response:', error.response?.data);
    }
    throw error;
  }

  public async adsPlanCheckout(params: {
    source: string;
    code: string;
    ads_option_qty: number;
    payment_issuer: string;
    payment_method: string;
    notes: string;
  }): Promise<PlanCheckoutResponse> {
    try {
      const token = await this.getToken();
      const response = await this.baseAxios.post<PlanCheckoutResponse>('/plan-checkout', params, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      this.handleAxiosError(error, 'plan checkout');
    }
  }

  public async getPurchasedPlan() {
    try {
      const token = await this.getToken();
      const response = await this.baseAxios.get<AdsPackageResponse>(`/plan/trimobi`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      this.handleAxiosError(error, 'get purchased plan');
    }
  }

  public async adsPlanUpgradeCheckout(params: {
    source: string;
    code: string;
    ads_option_qty: number;
    payment_issuer: string;
    payment_method: string;
    notes: string;
  }): Promise<PlanCheckoutResponse> {
    try {
      const token = await this.getToken();
      const response = await this.baseAxios.put<PlanCheckoutResponse>('/plan-checkout', params, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      return response.data;
    } catch (error) {
      this.handleAxiosError(error, 'plan upgrade checkout');
    }
  }

  public async getPlanCheckoutStatus(params: { condition: string; transaction_id: string }) {
    try {
      const token = await this.getToken();
      const response = await this.baseAxios.get<PlanCheckoutStatusResponse>(
        `/plan-checkout/trimobi`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
          params,
        }
      );
      return response.data;
    } catch (error) {
      this.handleAxiosError(error, 'get plan checkout status');
    }
  }

  public setEmail(email: string) {
    this.email = email;
  }

  public checkLeadsPayment(transactionId: string, token: string) {
    return this.baseAxios.get<LeadCheckoutResponse>(`/lead-checkout/ideal`, {
      params: {
        transaction_id: transactionId,
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
  }

  private async idealBypassLogin(email: string, password: string) {
    try {
      const response = await this.baseAxios.post<IdealBypassLoginResponse>('/auth/login', {
        type: 'ideal-bypass',
        // email: "<EMAIL>",
        email: email,
        password: password,
      });
      return response.data;
    } catch (error) {
      this.handleAxiosError(error, 'ideal bypass login');
    }
  }

  public async getToken() {
    const response = await this.idealBypassLogin(this.email, this.password);
    return response.data.token;
  }
}

const amartavipService = new AmartavipService();

export default amartavipService;
