import { RequestHandler } from 'express';
import { myFirestore } from '../../services/firebaseAdmin';
import AdminModel from '../../model/AdminModel';
import successResponse from '../../schema/successResponse';

const getAllAdminHandler: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [],
  handler: async (req, res) => {
    const collection = myFirestore.collection('admins');

    const get = await collection
      .orderBy('created_time', 'desc')
      .withConverter(AdminModel.converter)
      .get();

    const admins: AdminModel[] = [];

    get.forEach(result => {
      admins.push(result.data());
    });

    return res.send(
      successResponse({
        data: admins.map(v => v.toJsonResponse()),
      })
    );
  },
};

export default getAllAdminHandler;
