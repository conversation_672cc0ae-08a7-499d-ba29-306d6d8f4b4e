import { json, Request, Response } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import ClientModel from '../../model/ClientModel';
import { profileService } from '../../services/profileService';
import moment from 'moment';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import authChecker from '../../middlewares/authChecker';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';

interface IDreamVehicleHandler {
  _clientPath: string;
  _chatRoomPath: string;

  area: string;
  brand_name: string;
  brand_uuid: string;

  model_name: string;
  model_uuid: string;
  model_category: string;

  variant_code: string;
  variant_name: string;
  variant_uuid: string;

  color_name: string;
  color_code: string;

  license_plate: string;

  variant_free_text: string;

  year: string;
  price: number;

  condition: string;

  mileage: string;
}

class DreamVehicleHandler {
  public static middlewares = [
    auth<PERSON><PERSON><PERSON>,
    json(),
    body('variant_free_text')
      .if((input, meta) => {
        return meta.req.body.condition?.toUpperCase() === 'USED';
      })
      .trim()
      .notEmpty(),
    body('mileage')
      .if((input, meta) => {
        return meta.req.body.condition?.toUpperCase() === 'USED';
      })
      .trim()
      .notEmpty()
      .isNumeric()
      .isLength({ min: 3 }),
    body('year')
      .if((input, meta) => {
        return meta.req.body.condition?.toUpperCase() === 'USED';
      })
      .trim()
      .notEmpty()
      .isNumeric()
      .isLength({ min: 4, max: 4 }),
    body('license_plate')
      .if((input, meta) => {
        return meta.req.body.condition?.toUpperCase() === 'USED';
      })
      .trim()
      .notEmpty()
      .custom(input => {
        const plateRegex = /^[A-Z]{1,2}\s?\d{1,4}\s?[A-Z]{1,3}$/;

        if (!plateRegex.test(input)) {
          throw new Error('Invalid Indonesian license plate format');
        }

        return true;
      })
      .toUpperCase()
      .customSanitizer(input => input.replace(/\s+/g, '')),
    body('_chatRoomPath').notEmpty(),
    body('price').optional().isNumeric(),
    requestValidator,
  ];
  public static handler = async (
    req: Request<any, ResponseSchema, IDreamVehicleHandler>,
    res: Response
  ) => {
    const clientRef = myFirestore.doc(req.body._clientPath);
    const chatRoomRef = myFirestore.doc(req.body._chatRoomPath);
    const getClient = await clientRef.withConverter(ClientModel.converter).get();
    const data = getClient.data();

    if (!data) return;

    const dreamVehicle = {
      updated_at: moment().toDate(),

      condition: req.body.condition || 'new',

      brand_name: req.body.brand_name?.toUpperCase() || '',
      brand_uuid: req.body.brand_uuid || '',

      model_name: req.body.model_name?.toUpperCase(),
      model_uuid: req.body.model_uuid || '',
      model_category: req.body.model_category || '',

      variant_code: req.body.variant_code || '',
      variant_name: req.body.variant_name?.toUpperCase() || '',
      variant_uuid: req.body.variant_uuid || '',

      variant_free_text: req.body.variant_free_text || '',

      color_name: req.body.color_name?.toUpperCase() || null,
      color_code: req.body.color_code?.toUpperCase() || null,

      license_plate: req.body.license_plate?.toUpperCase() || '',

      mileage: req.body.mileage || null,

      year: req.body.year || moment().format('YYYY'),
      price: req.body.price || null,
    };

    if (req.body.variant_free_text) {
      dreamVehicle.variant_code = '';
      dreamVehicle.variant_name = '';
      dreamVehicle.variant_uuid = '';
    }

    const params = {
      'profile.area': {
        text: req.body.area.toUpperCase(),
        value: req.body.area.toUpperCase(),
      },
      dream_vehicle: dreamVehicle,
    };

    const batch = myFirestore.batch();

    // Update chat room
    batch.update(chatRoomRef, {
      dream_vehicle: dreamVehicle,
    });

    try {
      // Update client
      batch.update(clientRef, {
        ...params,
      });
      await batch.commit();
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }

    profileService
      .updateProfile(
        {
          cityGroup: req.body.area,
          phone: data.contacts.whatsapp,
          customerName: data.profile.name,
        },
        data.contacts.whatsapp
      )
      .then()
      .catch();

    res.send(
      successResponse({
        type: 'UPDATED',
        data: params,
      })
    );
  };
}

export default DreamVehicleHandler;
