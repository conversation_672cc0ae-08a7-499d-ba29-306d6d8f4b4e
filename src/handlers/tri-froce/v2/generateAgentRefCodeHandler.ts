import { body } from 'express-validator';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { HandlerTypes } from '../../../types/handler.types';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import { firestore } from 'firebase-admin';
import successResponse from '../../../schema/successResponse';
import { json } from 'express';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import errorResponse from '../../../schema/errorResponse';
import LeadsModel from '../../../model/LeadsModel';

interface RequestBody {
  agentCode: string;
  agentName: string;
  phoneNumber: string;
  fullName: string;
  province: {
    code: string;
    name: string;
  };
  city: {
    code: string;
    name: string;
  };
  vehicle: {
    brand: {
      name: string;
    };
    model: {
      name: string;
    };
    variant: {
      code: string;
      name: string;
    };
    color: {
      code: string;
      name: string;
    };
  };
  notes: string | null;
  organization: string;
  source: string;
}

const generateAgentRefCodeHandler: HandlerTypes<any, ResponseSchema, RequestBody> = {
  middlewares: [
    json(),
    body('agentCode')
      .notEmpty()
      .withMessage('Kode agen wajib diisi')
      .isString()
      .withMessage('Kode agen harus berupa teks'),
    body('agentName')
      .notEmpty()
      .withMessage('Nama agen wajib diisi')
      .isString()
      .withMessage('Nama agen harus berupa teks'),
    body('phoneNumber')
      .notEmpty()
      .withMessage('Nomor telepon wajib diisi')
      .isString()
      .withMessage('Nomor telepon harus berupa teks')
      .customSanitizer(value => phoneNumberCountryCodeSanitizer(value, '62', '62'))
      .custom(value => {
        if (!/^628[1-9][0-9]{7,11}$/.test(value)) {
          throw new Error(
            'Format nomor telepon tidak valid. Harus diawali 628 dan memiliki panjang 11-13 digit'
          );
        }
        return true;
      }),
    body('fullName')
      .notEmpty()
      .withMessage('Nama lengkap wajib diisi')
      .isString()
      .withMessage('Nama lengkap harus berupa teks'),
    body('province.code')
      .notEmpty()
      .withMessage('Kode provinsi wajib diisi')
      .isString()
      .withMessage('Kode provinsi harus berupa teks'),
    body('province.name')
      .notEmpty()
      .withMessage('Nama provinsi wajib diisi')
      .isString()
      .withMessage('Nama provinsi harus berupa teks'),
    body('city.code')
      .notEmpty()
      .withMessage('Kode kota wajib diisi')
      .isString()
      .withMessage('Kode kota harus berupa teks'),
    body('city.name')
      .notEmpty()
      .withMessage('Nama kota wajib diisi')
      .isString()
      .withMessage('Nama kota harus berupa teks'),
    body('vehicle.brand.name')
      .notEmpty()
      .withMessage('Nama merek kendaraan wajib diisi')
      .isString()
      .withMessage('Nama merek kendaraan harus berupa teks'),
    body('vehicle.model.name')
      .notEmpty()
      .withMessage('Nama model kendaraan wajib diisi')
      .isString()
      .withMessage('Nama model kendaraan harus berupa teks'),
    body('vehicle.variant.code')
      .optional({ values: 'falsy' })
      .isString()
      .withMessage('Kode varian kendaraan harus berupa teks'),
    body('vehicle.variant.name')
      .optional({ values: 'falsy' })
      .isString()
      .withMessage('Nama varian kendaraan harus berupa teks'),
    body('vehicle.color.code')
      .optional({ values: 'falsy' })
      .isString()
      .withMessage('Kode warna kendaraan harus berupa teks'),
    body('vehicle.color.name')
      .optional({ values: 'falsy' })
      .isString()
      .withMessage('Nama warna kendaraan harus berupa teks'),
    body('notes').optional().isString().withMessage('Catatan harus berupa teks'),
    body('organization')
      .notEmpty()
      .withMessage('Organisasi wajib diisi')
      .isString()
      .withMessage('Organisasi harus berupa teks'),
    body('source')
      .notEmpty()
      .withMessage('Sumber wajib diisi')
      .isString()
      .withMessage('Sumber harus berupa teks'),
    requestValidator,
  ],
  handler: async (req, res) => {
    const leadsCollection = myFirestore.collection('leads');

    const docName = `${req.body.organization}-${req.body.phoneNumber}`;
    const getLeads = await leadsCollection.withConverter(LeadsModel.converter).doc(docName).get();

    if (getLeads.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_ALREADY_EXISTS',
          messages: 'Leads sudah diakuisisi',
        })
      );
    }

    const agentRefCodeCollections = myFirestore.collection('agent_ref_codes');

    const refCode = await randomRefCode();
    const docRef = await agentRefCodeCollections.doc(refCode).get();
    const data = {
      agentCode: req.body.agentCode,
      agentName: req.body.agentName,
      phoneNumber: req.body.phoneNumber,
      fullName: req.body.fullName,
      province: req.body.province,
      city: req.body.city,
      vehicle: req.body.vehicle,
      notes: req.body.notes,
      refCode: refCode,
      createdAt: firestore.Timestamp.now(),
      updatedAt: firestore.Timestamp.now(),
      organization: req.body.organization,
      source: req.body.source,
      isLeadsSent: false,
    };
    await docRef.ref.set(data);
    res.send(
      successResponse({
        type: 'CREATED',
        data: refCode,
      })
    );
  },
};

const randomRefCode = async (): Promise<string> => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  let isUnique = false;

  while (!isUnique) {
    result = 'TFC#';

    // Generate 6 random characters
    for (let i = 0; i < 6; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters[randomIndex];
    }

    result += '#REF';

    // Check uniqueness
    const docRef = await myFirestore.collection('agent_ref_codes').doc(result).get();
    isUnique = !docRef.exists;
  }

  return result;
};

export default generateAgentRefCodeHandler;
