export interface ISendBotMessage {
  sender_email: string;
  message: string;
  type: string;
  room_id: string;
  payload?: {
    caption: string;
    url: string;
  };
}

export interface IQiscusBaseResponse<T> {
  results: T;
  status: number;
}

export interface IQiscusRoomResponse {
  room_avatar_url: string;
  room_channel_id: string;
  room_id: string;
  room_name: string;
  room_options: string;
  room_type: string;
}
