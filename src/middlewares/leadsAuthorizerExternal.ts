import { NextFunction, Request, Response } from 'express';
import errorResponse from '../schema/errorResponse';

const availableBasicAuth = [
  {
    username: 'leadsExternalApp',
    password: 'pDy<A2T.s(f3`6HZ',
    source: 'otocom',
  },
  {
    username: 'leadsMomotor',
    password: 'pDy<A2T.s(f3`6HZ',
    source: 'momotor',
  },
];

export default function leadsAuthorizerExternal(req: Request, res: Response, next: NextFunction) {
  let basicAuth = req.header('Authorization');

  try {
    const getEncodedFromHead = basicAuth?.replace('Basic ', '');

    if (!getEncodedFromHead) throw new Error('Unauthenticated');
    const decodedUsernamePassword = Buffer.from(getEncodedFromHead, 'base64').toString('ascii');

    const [decodedUsername, decodedPassword] = decodedUsernamePassword.split(':');

    const find = availableBasicAuth.find(b => {
      return decodedUsername === b.username && decodedPassword === b.password;
    });

    if (!find) {
      throw new Error('NOT_AUTHENTICATED');
    }

    next();
  } catch (e: any) {
    console.log(e);
    res.status(422).send(
      errorResponse({
        type: 'NOT_AUTHENTICATED',
        messages: 'Tidak terautentikasi',
      })
    );
  }
}
