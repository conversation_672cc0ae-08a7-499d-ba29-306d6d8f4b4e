import { myFirestore } from '../../../src/services/firebaseAdmin';
import metaServices from '../../../src/services/MetaServices';
import { IProject } from '../../../src/types/firestore/project.types';
import fs from 'fs';
import path from 'path';
import mime from 'mime-types';
import { fail } from 'assert';
const projectId = 'kpsDdEcRtReOQrCYkzq2';
const getProjectData = async () => {
  const projectCollections = myFirestore.collection('projects').doc(projectId);
  const projectData = await projectCollections.get();
  return projectData.data() as IProject;
};

let mediaId = '1305998980605779';

describe('Meta Media', () => {
  it('should upload image', async () => {
    try {
      const projectData = await getProjectData();
      if (!projectData.meta) {
        throw new Error('Project data not found');
      }
      const filePath = 'test/whatsappProviderTest/downloaded_media_1738650063138.mp4';
      const imageBuffer = await fs.promises.readFile(filePath);
      const mimeType = mime.lookup(filePath) || 'application/octet-stream';
      const fileName = path.basename(filePath);
      const imageFile = new File([imageBuffer], fileName, {
        type: mimeType,
      });
      const image = await metaServices.uploadMedia(
        {
          file: imageFile,
          type: mimeType,
          messagingProduct: 'whatsapp',
        },
        projectData.meta
      );
      mediaId = image.data.id;
      console.log('mediaId', mediaId);
      expect(image.data.id).toBeDefined();
      expect(typeof image.data.id).toBe('string');
    } catch (error) {
      console.log('ERROR:');
      console.log(error);
      fail('Failed to upload image - Test should not reach catch block');
    }
  });

  it('should download media by id', async () => {
    try {
      const projectData = await getProjectData();
      if (!projectData.meta) {
        throw new Error('Project data not found');
      }
      const media = await metaServices.getFile(mediaId, projectData.meta);

      // Buat folder download jika belum ada
      const downloadDir = path.join(__dirname, 'download');
      if (!fs.existsSync(downloadDir)) {
        fs.mkdirSync(downloadDir);
      }

      // Ambil content type dari header
      const contentType = media.headers['content-type'];
      const extension = mime.extension(contentType) || 'bin';

      // Simpan file
      const filePath = path.join(downloadDir, `downloaded_media_${Date.now()}.${extension}`);
      fs.writeFileSync(filePath, media.data);

      console.log('File saved to:', filePath);
      expect(media.data).toBeDefined();
      expect(media.data).toBeInstanceOf(Buffer);
      expect(Buffer.isBuffer(media.data)).toBe(true);
      expect(fs.existsSync(filePath)).toBe(true);
    } catch (error) {
      console.log('ERROR:');
      console.log(error);
      fail('Failed to download media by URL - Test should not reach catch block');
    }
  });
});
