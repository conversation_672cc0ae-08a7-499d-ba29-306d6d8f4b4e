import { json, Request, Response } from 'express';
import triFor<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '../../middlewares/triforce<PERSON>uth<PERSON><PERSON><PERSON>';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import errorResponse from '../../schema/errorResponse';
import { IClientDocument } from '../../types/firestore/client_document_types';
import { firestore } from 'firebase-admin';
import moment from 'moment';
import successResponse from '../../schema/successResponse';
import updateLeadsService from '../../services/updateLeadsServices';

interface IAddOrderHistoryReqBody {
  clientId: string;
  offerCode: string;
  createdAt: Date;
  paymentScheme: 'CASH' | 'CREDIT';
  source: 'IDEAL' | 'TRIFORCE';
}

export default class AddOrderHistory {
  public static middlewares = [
    triForce<PERSON><PERSON><PERSON><PERSON><PERSON>,
    json(),
    body('clientId').notEmpty(),
    body('offerCode').notEmpty(),
    body('createdAt')
      .notEmpty()
      .custom(input => {
        const momentCreatedAt = moment(input);
        if (!momentCreatedAt.isValid()) {
          throw new Error('Date is not valid!');
        }

        return true;
      })
      .customSanitizer(input => {
        const momentCreatedAt = moment(input);
        return momentCreatedAt.toDate();
      }),
    body('paymentScheme').notEmpty().isIn(['CASH', 'CREDIT']),
    body('source').notEmpty(),
    requestValidator,
  ];
  public static handler = async (
    req: Request<{}, ResponseSchema, IAddOrderHistoryReqBody>,
    res: Response<ResponseSchema>
  ) => {
    const clientRef = myFirestore.doc('clients/' + req.body.clientId);
    const getClient = await clientRef.get();

    if (getClient.exists) {
      const data = getClient.data() as IClientDocument;

      const orderHistories = [...(data.order_histories ?? [])];
      orderHistories.push({
        source: req.body.source,
        payment_scheme: req.body.paymentScheme,
        created_at: firestore.Timestamp.fromDate(req.body.createdAt),
        offer_code: req.body.offerCode,
      });

      await clientRef.update({
        order_histories: orderHistories,
      });

      await updateLeadsService
        .updateLeadsOfferCode({
          phone_number: data.contacts.whatsapp,
          offer_code: req.body.offerCode,
        })
        .then(() => {})
        .catch(reason => console.log(reason));

      res.send(
        successResponse({
          type: 'UPDATED',
          data: orderHistories,
        })
      );
    } else {
      res.status(400).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          data: null,
        })
      );
    }
  };
}
