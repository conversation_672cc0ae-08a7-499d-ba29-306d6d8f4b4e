import { IParamsUpdateLeads, IParamsUpdateLeadsOffer } from '../types/services/updateLeads_types';
import axios from 'axios';

class UpdateLeadsServices {
  private baseUrl = axios.create({
    baseURL: `https://order.amartahonda.com/rest/index.php/api/amarta`,
  });
  public async updateLeadsOfferCode(params: IParamsUpdateLeadsOffer) {
    return await this.baseUrl.put('/leads_offer', {
      ...params,
    });
  }
}

const updateLeadsService = new UpdateLeadsServices();

export default updateLeadsService;
