import { HandlerTypes } from '../../../types/handler.types';
import dayjs from 'dayjs';
import successResponse from '../../../schema/successResponse';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import { collect, Collection } from 'collect.js';
import axios, { AxiosError } from 'axios';
import errorResponse from '../../../schema/errorResponse';

interface Bill {
  company: string;
  data: AgentBill[];
}

interface AgentBill {
  agent_code: string;
  total_bill: number;
  total_leads_not_updated: number;
  phone_numbers: string[];
}

/*
Untuk CRON di jalankan setiap jam 00:10.

Jadi jika cron dijalankan tanggal 2024-08-22 00:10.
Maka yang dilakukan adalah get semua Leads yang diakuisisi di tanggal 2024-08-20 antara jam 00:00 sampai jam 23:59.
Lalu penentuan deadline harus upload filechat whatsappnya adalah sebelum tanggal 2024-08-21 23:59
*/

const leadsFromFreeLeadsDontHaveFileChatHandler: HandlerTypes = {
  middlewares: [],
  handler: async (req, res) => {
    const today = dayjs();

    const leadsStartDate = today.subtract(2, 'days').startOf('day');
    const leadsEndDate = leadsStartDate.endOf('day');
    const deadLineUpdateFileChat = leadsEndDate.add(1).endOf('day');

    const leadsCollection = myFirestore.collection('leads');

    let leadsByRangeDate: LeadsModel[] = [];

    const getLeads = await leadsCollection
      .where('createdAt', '>=', leadsStartDate.toDate())
      .where('createdAt', '<', leadsEndDate.toDate())
      .orderBy('createdAt', 'desc')
      .where('fromFreeLeads', '==', true)
      .withConverter(LeadsModel.converter)
      .get();

    getLeads.forEach(result => {
      const data = result.data();
      leadsByRangeDate.push(data);
    });

    let leadsUpdated = leadsByRangeDate.filter(l => {
      if (l.agentWhatsappConversations.length === 0) return false;

      const agentConversationWithLeads = l.agentWhatsappConversations[0];

      return dayjs(agentConversationWithLeads.createdAt).isBefore(deadLineUpdateFileChat);
    });
    const collectUpdated = collect(leadsUpdated);
    const groupByUpdatedByAgentCode = collectUpdated.groupBy('agentCode');
    const agentWithLeadsUpdated = groupByUpdatedByAgentCode
      .map((item: any, index) => {
        return item.count();
      })
      .all();

    let leadsNotUpdated = leadsByRangeDate.filter(l => {
      if (l.agentWhatsappConversations.length === 0) return true;
      const agentConversationWithLeads = l.agentWhatsappConversations[0];
      return dayjs(agentConversationWithLeads.createdAt).isAfter(deadLineUpdateFileChat);
    });

    const collectNotUpdated = collect(leadsNotUpdated);
    const notUpdatedLeadsGroupByCompany = collectNotUpdated.groupBy('organization');

    const bills: Bill[] = [];

    for (const company of notUpdatedLeadsGroupByCompany.keys()) {
      const bill: Bill = {
        company: company,
        data: [],
      };
      const groupByAgentCode = (
        notUpdatedLeadsGroupByCompany.get(company) as Collection<LeadsModel>
      ).groupBy('agentCode');

      for (const agentCode of groupByAgentCode.keys()) {
        const leads = groupByAgentCode.get(agentCode) as Collection<LeadsModel>;
        const dataBill: AgentBill = {
          phone_numbers: [],
          total_bill: 0,
          total_leads_not_updated: leads.count(),
          agent_code: agentCode,
        };
        leads.each(currentItem => {
          if (currentItem.organization === 'amartachery') dataBill.total_bill += 50000;
          if (currentItem.organization === 'amartaneta') dataBill.total_bill += 50000;
          if (currentItem.organization === 'amartavinfast') dataBill.total_bill += 50000;
          if (currentItem.organization === 'amartahonda') dataBill.total_bill += 20000;
          dataBill.phone_numbers.push(currentItem.phoneNumber);
        });
        bill.data.push(dataBill);
      }

      bills.push(bill);
    }

    const dataSendToBill = {
      executed_at: today.format('YYYY-MM-DD HH:mm'),
      leads: {
        start_date: leadsStartDate.format('YYYY-MM-DD HH:mm'),
        end_date: leadsEndDate.format('YYYY-MM-DD HH:mm'),
        deadline_updated_file_chat: deadLineUpdateFileChat.format('YYYY-MM-DD HH:mm:ss'),
        total_leads: getLeads.size,
        total_updated: leadsUpdated.length,
        total_not_updated: leadsNotUpdated.length,
      },
      bills,
    };

    try {
      const send = await sendBill(dataSendToBill);
      console.log('CREATE_BILL_SUCCESS', JSON.stringify(send), JSON.stringify(dataSendToBill));
    } catch (e) {
      const error = e as AxiosError<any>;

      console.log(
        'CREATE_BILL_ERROR',
        JSON.stringify(error.response?.data),
        JSON.stringify(dataSendToBill),
        JSON.stringify(error.code)
      );

      return res.send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e?.toString(),
        })
      );
    }

    res.send(
      successResponse({
        data: dataSendToBill,
      })
    );
  },
};

interface ICreateBillParams {
  executed_at: string;
  leads: {
    start_date: string;
    end_date: string;
    deadline_updated_file_chat: string;
    total_leads: number;
    total_updated: number;
    total_not_updated: number;
  };
  bills: Bill[];
}

const sendBill = async (params: ICreateBillParams) => {
  const fullUrl =
    'https://asia-southeast1-autotrimitra.cloudfunctions.net/amartavip__schedule/daily-lead-bill';
  try {
    const send = await axios.post(fullUrl, {
      ...params,
    });
    return send.data;
  } catch (e) {
    throw e;
  }
};

export default leadsFromFreeLeadsDontHaveFileChatHandler;
