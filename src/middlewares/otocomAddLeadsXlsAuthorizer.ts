import { RequestHand<PERSON> } from 'express';
import errorResponse from '../schema/errorResponse';
import { myAuth } from '../services/firebaseAdmin';
import { DecodedIdToken } from 'firebase-admin/lib/auth';

const otocomAddLeadsXlsAuthorizer: RequestHandler = async (req, res, next) => {
  const token = req.headers.authorization?.split('Bearer ')[1];

  if (!token) {
    return res.status(422).send(
      errorResponse({
        type: 'NOT_AUTHENTICATED',
        messages: 'Tidak terautentikasi',
      })
    );
  }

  try {
    const user: DecodedIdToken = await myAuth().verifyIdToken(token);
    res.locals.decodedToken = user;

    next();
  } catch (error) {
    return res.status(422).send(
      errorResponse({
        type: 'NOT_AUTHENTICATED',
        messages: 'Tidak terautentikasi',
      })
    );
  }
};

export default otocomAddLeadsXlsAuthorizer;
