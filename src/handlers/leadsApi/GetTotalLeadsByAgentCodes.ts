import { HandlerTypes } from '../../types/handler.types';
import { json } from 'express';
import { body } from 'express-validator';
import moment, { Moment } from 'moment';
import requestValidator from '../../middlewares/requestValidator';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import successResponse from '../../schema/successResponse';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';

interface ReqBody {
  agentCodes: {
    agentCode: string;
    organization: string;
  }[];
  startDate: string;
  endDate: string;
}

const getTotalLeadsByAgentCodes: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('startDate')
      .notEmpty()
      .custom(input => {
        const validDate = moment(input, 'YYYY-MM-DD').isValid();
        if (!validDate) {
          throw new Error('Invalid date, tanggal harus format YYYY-MM-DD');
        }
        return true;
      }),
    body('endDate')
      .notEmpty()
      .custom(input => {
        const validDate = moment(input, 'YYYY-MM-DD').isValid();
        if (!validDate) {
          throw new Error('Invalid date, tanggal harus format YYYY-MM-DD');
        }
        return true;
      }),
    body('agentCodes').notEmpty().isArray({
      min: 0,
    }),
    body('agentCodes.*.agentCode').notEmpty(),
    body('agentCodes.*.organization').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const varGlobal: {
      startDate: Moment;
      endDate: Moment;
      count: {
        agentCode: string;
        organization: string;
        allLeads: number;
        trackedLeads: number;
      }[];
    } = {
      startDate: moment(req.body.startDate, 'YYYY-MM-DD').startOf('day'),
      endDate: moment(req.body.endDate, 'YYYY-MM-DD').endOf('day'),
      count: [],
    };

    const collection = myFirestore.collection('leads');
    for (const agentCode of req.body.agentCodes) {
      const get = await collection
        .where('agentCode', '==', agentCode.agentCode)
        .where('organization', '==', agentCode.organization)
        .where('createdAt', '>', varGlobal.startDate.toDate())
        .where('createdAt', '<', varGlobal.endDate.toDate())
        .withConverter(LeadsModel.converter)
        .get();

      const leads: LeadsModel[] = [];

      get.forEach(r => {
        const data = r.data();
        leads.push(data);
      });

      const allLeads = leads.length;
      const trackedLeads = leads.filter(l => l.isTracking).length;

      varGlobal.count.push({
        allLeads,
        trackedLeads,
        organization: agentCode.organization,
        agentCode: agentCode.agentCode,
      });
    }

    res.send(
      successResponse({
        data: {
          count: varGlobal.count,
        },
        type: 'FETCHED',
      })
    );
  },
};

export default getTotalLeadsByAgentCodes;
