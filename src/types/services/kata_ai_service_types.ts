export interface IKataAiServiceReturn<SUCCESS = any, FAILED = any> {
  success: boolean;
  data?: SUCCESS;
  failed?: FAILED;
}

export interface IKataAiServiceDataError {
  code: string;
  message: string;

  [key: string]: string;
}

export interface IUploadMediaSuccess {
  media: {
    id: string;
  }[];
}

export interface ISendMessage {
  to: string;
  recipient_type: 'individual' | 'group';
  type: EMessageContext;
  preview_url: boolean;
  text?: TextContext;
  image?: Pick<IFileContext, 'caption' | 'id' | 'mime_type' | 'link'>;
}

export interface TextContext {
  body: string;
}

export interface IFileContext {
  file?: string;
  id?: string;
  link?: string;
  mime_type?: string;
  sha256?: string;
  caption?: string;
}

export type EMessageContext =
  | 'audio'
  | 'document'
  | 'image'
  | 'location'
  | 'system'
  | 'text'
  | 'video'
  | 'voice'
  | 'context'
  | 'sticker';

export interface ISendMessageSuccess {
  messages: {
    id: string;
  }[];
  meta: {
    api_status: 'stable';
    version: '2.29.3';
  };
}
