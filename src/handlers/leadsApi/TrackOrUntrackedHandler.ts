import { json, Request, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { body, param } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';
import successResponse from '../../schema/successResponse';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import { LeadsOrganization } from '../../types/firestore/leads_model.types';
import fetchAgent from '../../services/agent/fetchAgent';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import { leadsNotesCounter } from '../../helpers/leads/leadsNotes';
import moment from 'moment';
interface ReqBody {
  leadsPhoneNumber: string;
  notes: string;
  updatedBy: string;
  organization: LeadsOrganization;
}

const trackOrUntrackedHandler = {
  middlewares: [
    leadsAuthorizer,
    json(),
    param('type').isIn(['track', 'untrack']),
    body('leadsPhoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('notes')
      .optional()
      .isLength({ min: 30, max: 600 })
      .withMessage('Catatan Minimal 30 s.d 600 karakter.'),
    body('updatedBy').notEmpty(),
    body('organization').notEmpty(),
    requestValidator,
  ],
  handler: async function (
    req: Request<
      {
        type: 'track' | 'untrack';
      },
      ResponseSchema,
      ReqBody
    >,
    res: Response<ResponseSchema>
  ) {
    let type = req.params.type;

    const collection = myFirestore.collection('leads');
    const getLeads = await collection
      .withConverter(LeadsModel.converter)
      .doc(`${req.body.organization}-${req.body.leadsPhoneNumber}`)
      .get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    let agentName: string | null = null;
    if (!leads.agentName) {
      try {
        const fetch = await fetchAgent(leads.agentCode);
        agentName = fetch.name;
      } catch (e: any) {
        agentName = 'NO_NAME';
      }
    } else {
      agentName = leads.agentName;
    }

    const notesCounter = await leadsNotesCounter(leads);
    let notes: ILeadsNotesDocument<Date> = {
      agentCode: leads.agentCode,
      event: req.params.type,
      notes: req.body.notes,
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: req.body.updatedBy,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.notes = req.body.notes;
    leads.isTracking = type !== 'untrack';
    if (req.params.type === 'untrack') {
      leads.needToMoveToColdAt = moment().add(14, 'days').toDate();
    }
    leads.updatedAt = new Date();
    leads.updateHistories.push({
      ...notes,
    });

    try {
      const batch = myFirestore.batch();

      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      if (notes) {
        batch.set(myFirestore.collection('leads_notes').doc(), notes);
      }

      await batch.commit();

      await aggregatesAgent(leads.agentCode, req.body.organization).then();
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: `Error: ${e.toString()}`,
        })
      );
    }
  },
};

export default trackOrUntrackedHandler;
