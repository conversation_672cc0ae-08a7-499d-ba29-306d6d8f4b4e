import vehicles from './otocom_vehicle.json';
import { myFirestore } from '../../../src/services/firebaseAdmin';

async function importOtoMasterVehicle() {
  const collection = myFirestore.collection('master').doc('otocom').collection('vehicles');

  const batch = myFirestore.batch();

  for (const vehicle of vehicles) {
    const docName = vehicle.otoComVariantId;
    const docRef = collection.doc(docName);
    batch.set(docRef, { ...vehicle });
  }

  try {
    await batch.commit();
    return;
  } catch (e) {
    console.log(e);
    return;
  }
}

importOtoMasterVehicle().then().catch();
