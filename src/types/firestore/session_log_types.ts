import { firestore } from 'firebase-admin';
import { IFirestoreMessageEntity } from './messages/message_types';

export interface ISessionLog<T> {
  session_uuid: string;
  attempted: boolean;
  attempted_at: boolean;
  messages: Pick<IFirestoreMessageEntity, 'message' | 'origin'>[];
  data: T;
}

export interface ISessionLogDataClient {
  session_uuid: string;
  client_id: string;
  session_start: firestore.Timestamp | Date | string;
  session_end: firestore.Timestamp | Date | string;
  inbound_text: number;
  outbound_text: number;
  ref_link: string;
  inbound_img: number;
  outbound_img: number;
  avg_inbound_txt_chars: number;
  first_time_resp: number;
  min_time_resp: number;
  max_time_resp: number;
  avg_time_resp: number;
  inbound_link: number;
  outbound_link: number;
}
