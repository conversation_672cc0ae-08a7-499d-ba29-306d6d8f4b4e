export function isMessage(param: any): param is IWebhookMessageEntity {
  return 'messages' in param;
}

export interface IWebhookMessageEntity {
  contacts: IWebhookContact[];
  messages: Messages[];
}

export interface Messages {
  from: string;
  id: string;
  timestamp: string;
  type: MessageContext;
  audio?: FileContext;
  document?: FileContext;
  image?: FileContext;
  voice?: FileContext;
  system?: TextContext;
  text?: TextContext;
  context?: IReplyContext;
  sticker?: IStickerContext;
  locations?: LocationContext;
}

export type MessageContext =
  | 'audio'
  | 'document'
  | 'image'
  | 'location'
  | 'system'
  | 'text'
  | 'video'
  | 'voice'
  | 'context'
  | 'sticker';

export interface FileContext {
  filename?: string;
  id?: string;
  link?: string;
  mime_type?: string;
  sha256?: string;
  caption?: string;
}

export interface LocationContext {
  address: string;
  latitude: number;
  longitude: number;
  name: string;
}

export interface TextContext {
  body: string;
}

export interface IWebhookContact {
  profile: {
    name: string;
  };
  wa_id: string;
}

export interface IStickerContext {
  id: string;
  metadata: {
    emojis: string[];
    'is-first-party-sticker': number;
    'sticker-pack-id': string;
  };
  mime_type: string;
  sha256: string;
}

export interface IReplyContext {
  from: string;
  id: string;
}
