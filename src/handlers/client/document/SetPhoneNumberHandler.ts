import { json, Request, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import { profileService } from '../../../services/profileService';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';

export interface SetPhoneNumberFields {
  type: 'owner_phone_number' | 'guarantor_phone_number' | 'order_maker_phone_number';
  phoneNumber: string;
  fullName: string;
}

export interface SetPhoneNumberReqBody extends SetPhoneNumberFields {
  clientRef: string;
}

export default class SetPhoneNumberHandler {
  public static middlewares = [
    json(),
    body('type').notEmpty(),
    body('fullName').notEmpty(),
    body('phoneNumber')
      .notEmpty()
      .customSanitizer(input => {
        return phoneNumberCountryCodeSanitizer(input, '62', '62');
      }),

    body('clientRef').notEmpty(),
    requestValidator,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, SetPhoneNumberReqBody>,
    res: Response
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);

    try {
      await clientDocRef.update({
        [`details.${req.body.type}`]: req.body.phoneNumber,
      });

      const updateProfile = await profileService.updateProfile(
        {
          phone: req.body.phoneNumber,
          customerName: req.body.fullName.toUpperCase(),
        },
        req.body.phoneNumber
      );

      res.send(
        successResponse({
          type: 'UPDATED',
          data: updateProfile.data,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
