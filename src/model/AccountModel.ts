import { myFirestore } from '../services/firebaseAdmin';

export default class AccountModel {
  private static _instance: AccountModel;

  static async getAdmin(param: { filter: any }) {
    // need check reference first

    let isAdmin, docAdmin, dataAdmin;
    const projectReference = myFirestore.collection('projects').doc(param.filter.project_doc);
    const adminRef = myFirestore.collection('admins');
    const snapshot = await adminRef
      .where('doc_project', '==', projectReference)
      .where('email', '==', param.filter.email)
      .where('phone_number', '==', param.filter.phone_number)
      .get();
    if (snapshot.empty) {
      isAdmin = false;
      docAdmin = null;
      dataAdmin = {};
    } else {
      snapshot.forEach(doc => {
        isAdmin = true;
        docAdmin = doc.id;
        dataAdmin = doc.data();
      });
    }

    return { isAdmin, docAdmin, dataAdmin };
  }
}
