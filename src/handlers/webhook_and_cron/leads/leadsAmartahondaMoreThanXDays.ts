import { RequestHand<PERSON> } from 'express';
import LeadsModel from '../../../model/LeadsModel';
import { myFirestore } from '../../../services/firebaseAdmin';
import moment from 'moment';
import collect, { Collection } from 'collect.js';
import telegramServices from '../../../services/telegramServices';
import successResponse from '../../../schema/successResponse';

const leadsAmartahondaMoreThanXDays: {
  handler: RequestHandler;
} = {
  handler: async (req, res) => {
    const leadsCollection = myFirestore.collection('leads');
    const nowAdd7Days = moment().add(7, 'days');

    const get = await leadsCollection
      // .where("isTracking", "==", true)
      .where('organization', '==', 'amartahonda')
      .where('createdAt', '<', nowAdd7Days.toDate())
      .withConverter(LeadsModel.converter)
      .get();

    const leads: LeadsModel[] = [];

    get.forEach(result => {
      const data = result.data();
      leads.push(data);
    });

    const summaries: { agentName: string; agentCode: string; totalLeads: number }[] = [];

    const convertLeadsToCollection = collect(leads);
    const groupByAgentCode = convertLeadsToCollection.groupBy('agentCode');
    for (const key of Object.keys(groupByAgentCode.all())) {
      const lastElementItems = (
        groupByAgentCode.all()[key as any] as Collection<LeadsModel>
      ).last();
      const collectionElementItems = groupByAgentCode.all()[key as any] as Collection<LeadsModel>;

      summaries.push({
        totalLeads: collectionElementItems.count(),
        agentCode: lastElementItems.agentCode,
        agentName: lastElementItems.agentName,
      });
    }

    const now = moment();
    let messageTelegram =
      `=== LEADS LEBIH DARI 7 HARI ===` +
      `\nSemua yang ada di My Leads baik yang masih di track atau tidak di track lebih dari 7 hari.` +
      `\nTanggal: ${now.format('YYYY-MM-DD HH:mm')}.` +
      `\n`;
    for (const item of summaries) {
      messageTelegram += `\n- ${item.agentName?.trim()} (${item.agentCode}): ${item.totalLeads} leads`;
    }

    telegramServices.sendMessage('-1001957142922', messageTelegram).then().catch();

    res.send(
      successResponse({
        data: summaries,
      })
    );
  },
};

export default leadsAmartahondaMoreThanXDays;
