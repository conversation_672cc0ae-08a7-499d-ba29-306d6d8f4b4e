import axios, { AxiosError } from 'axios';
import {
  AutotrimitraBaseResponse,
  AutotrimitrayBaseError,
  IGeo,
  IProvince,
  IVariant,
  IVehicleModel,
} from '../types/services/autotrimitra_service_types';

class AutotrimitraServices {
  private axios = axios.create({
    baseURL: 'https://au-api-trimitra-get-65q45htc.ts.gateway.dev',
  });
  private key = 'AIzaSyAFTemJSnp4h16lYQfITqLD8Ryp9fGNsVg';

  public async getProvince() {
    try {
      return await this.axios.get<AutotrimitraBaseResponse<IProvince[]>>('/geo-area/province', {
        params: {
          key: this.key,
        },
      });
    } catch (e: any) {
      throw e as AxiosError<AutotrimitrayBaseError>;
    }
  }

  public async getCities(provinceCode: string) {
    try {
      return await this.axios.get<AutotrimitraBaseResponse<IGeo[]>>('/geo-area/city', {
        params: {
          province_code: provinceCode,
          key: this.key,
        },
      });
    } catch (e: any) {
      throw e as AxiosError<AutotrimitrayBaseError>;
    }
  }

  public async postalCode(postal_code: string) {
    try {
      return await this.axios.get<AutotrimitraBaseResponse<IGeo[]>>('/geo-area/postal-code', {
        params: {
          postal_code: postal_code,
          key: this.key,
        },
      });
    } catch (e: any) {
      throw e as AxiosError<AutotrimitrayBaseError>;
    }
  }

  public async getVehicleModelBrand(params?: {
    brandUuid?: string;
    modelUuid?: string;
    category?: string;
  }) {
    const queries = {
      ...(params?.brandUuid && {
        brand_uuid: params.brandUuid,
      }),
      ...(params?.modelUuid && {
        model_uuid: params.modelUuid,
      }),
      ...(params?.category && {
        category: params.category,
      }),
    };
    try {
      return await this.axios.get<AutotrimitraBaseResponse<IVehicleModel[]>>('/vehicle/model', {
        params: {
          key: this.key,
          ...queries,
        },
      });
    } catch (e: any) {
      throw e as AxiosError<AutotrimitrayBaseError>;
    }
  }

  public async getVehicleVariantModel(params?: {
    variantUuid?: string;
    modelUuid?: string;
    code?: string;
    brandUuid?: string;
  }) {
    const queries = {
      ...(params?.variantUuid && {
        variant_uuid: params.variantUuid,
      }),
      ...(params?.modelUuid && {
        model_uuid: params.modelUuid,
      }),
      ...(params?.code && {
        code: params.code,
      }),
      ...(params?.brandUuid && {
        brand_uuid: params.brandUuid,
      }),
    };
    try {
      return await this.axios.get<AutotrimitraBaseResponse<IVariant[]>>('/vehicle/variant', {
        params: {
          key: this.key,
          ...queries,
        },
      });
    } catch (e: any) {
      throw e as AxiosError<AutotrimitrayBaseError>;
    }
  }
}

export const autotrimitraServices = new AutotrimitraServices();
