import { Router } from 'express';
import insertAgentContactList from '../handlers/onboarding/InsertAgentContactList';
import getAgentContactList from '../handlers/onboarding/getAgentContactList';

const router = Router();
export default function (parent: Router) {
  parent.use('/onboarding', router);

  router.post('/add-contacts', insertAgentContactList.middlewares, insertAgentContactList.handler);
  router.get(
    '/get-contacts/:agentPhoneNumber',
    getAgentContactList.middlewares,
    getAgentContactList.handler
  );
}
