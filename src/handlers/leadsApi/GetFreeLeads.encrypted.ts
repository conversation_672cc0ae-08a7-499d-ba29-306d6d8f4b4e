import { RequestHandler } from 'express';
import { myFirestore } from '../../services/firebaseAdmin';
import FreeLeadsModel from '../../model/FreeLeadsModel';
import successResponse from '../../schema/successResponse';
import { query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { encrypt } from '../../helpers/encryption';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';

interface IParams {
  organization: string;
}

interface IQueries {
  page: number;
  size: number;
  cityGroup: string;
}

const getFreeLeadsEncrypted: {
  middlewares: RequestHandler[];
  handler: RequestHandler<IParams, any, any, IQueries>;
} = {
  handler: async function (req, res) {
    const collection = myFirestore.collection('free_leads');

    let totalPage: number;

    let queryBuilder = collection
      .where('organization', '==', req.params.organization)
      .where('isAcquired', '==', false);

    if (req.query.cityGroup) {
      queryBuilder = queryBuilder.where('area', '==', req.query.cityGroup);
    }

    const getCount = await queryBuilder.count().get();
    const totalAllFreeLeads = getCount.data().count;

    totalPage = Math.ceil(totalAllFreeLeads / req.query.size);

    let startAt = 0;

    if (req.query.page > 1) {
      startAt = req.query.size * (req.query.page - 1);
    }

    let getQuery = queryBuilder.orderBy('createdAt', 'desc');

    // Pagination
    if (req.query.page && req.query.size) {
      getQuery = getQuery.offset(startAt).limit(req.query.size);
    }
    const get = await getQuery.withConverter(FreeLeadsModel.converter).get();

    const freeLeads: FreeLeadsModel[] = [];

    get.forEach(result => {
      const data = result.data();
      freeLeads.push(data);
    });

    res.send(
      successResponse({
        meta: {
          totalLeadsCurrentPage: freeLeads.length,
          totalLeadsAllPages: totalPage,
          totalAvailablePages: totalAllFreeLeads,
        },
        data: freeLeads
          .map(value => value.toJsonResponse())
          .map(value => {
            const encryptedPhoneNumber = encrypt(value.phoneNumber);
            return {
              ...value,
              phoneNumber: encryptedPhoneNumber,
              ref: undefined,
              ideal: undefined,
              documentId: undefined,
            };
          }),
      })
    );
  },
  middlewares: [
    leadsAuthorizer,
    query('page').optional().toInt(),
    query('size').optional().toInt(),
    query('cityGroup').optional().toUpperCase().trim(),
    requestValidator,
  ],
};

export default getFreeLeadsEncrypted;
