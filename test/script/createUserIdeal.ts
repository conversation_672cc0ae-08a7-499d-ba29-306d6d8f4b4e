import { myAuth, myFirestore } from '../../src/services/firebaseAdmin';
import { firestore } from 'firebase-admin';
import randomString from '../../src/helpers/randomString';

const newUsers = [
  {
    email: '<EMAIL>',
    projects: myFirestore.collection('projects').doc('v8GGopG1v89nd46aajEX'),
    name: 'reggina<PERSON><PERSON>',
    password: randomString(6),
  },
];

async function createUserIdeal() {
  const collection = myFirestore.collection('admins');
  for (const newUser of newUsers) {
    const create = await myAuth().createUser({
      email: newUser.email,
      password: newUser.password,
    });

    const userDoc = collection.doc(create.uid);
    await userDoc.set({
      active: true,
      address: '',
      cont_clients: 0,
      created_time: firestore.Timestamp.now(),
      doc_admin: userDoc,
      doc_department: null,
      doc_project: newUser.projects,
      email: newUser.email,
      last_session_active: null,
      level: 'owner',
      name: newUser.name,
      phone_number: null,
    });

    console.log(`email: ${newUser.email} , password: ${newUser.password}`);
  }
}

createUserIdeal();
