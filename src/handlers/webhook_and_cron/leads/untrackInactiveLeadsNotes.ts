import { Request, Response } from 'express';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import checkAgentUpdatingLeadsNotes from '../../../helpers/leads/checkAgentUpdatingLeadsNotes';

const untrackInactiveLeadsNotes = {
  middlewares: [],
  handler: async function (req: Request, res: Response) {
    try {
      await checkAgentUpdatingLeadsNotes();
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      console.log('ERROR_UNTRACK_INACTIVE_LEADS', e?.toString());
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e?.toString() ?? JSON.stringify(e),
        })
      );
    }
  },
};

export default untrackInactiveLeadsNotes;
