import { json, Request, Response } from 'express';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';

interface ILeadFailNotificationReqBody {
  phoneNumber: string[];
}

class LeadRemoveBroadcastNotification {
  public middlewares = [
    json(),
    body('phoneNumber')
      .isArray({
        min: 1,
      })
      .withMessage('Tipe data harus Array string[] minimal mempunyai satu nilai'),
    body('phoneNumber.*')
      .notEmpty()
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    requestValidator,
  ];

  public async handler(
    req: Request<null, ResponseSchema, ILeadFailNotificationReqBody>,
    res: Response
  ) {
    let batch = myFirestore.batch();

    await Promise.all([
      ...req.body.phoneNumber.map(phoneNumber => {
        return new Promise(async function (res) {
          const find = await myFirestore
            .collection('clients')
            .where('contacts.whatsapp', '==', phoneNumber)
            .get();

          if (!find.empty && find.size === 1) {
            find.forEach(result => {
              const clientRef = result.ref;

              batch.update(clientRef, {
                broadcast: null,
              });
            });

            res(true);
          } else {
            console.error('ERROR_LEAD_FAIL_NOTIFICATION', 'PHONE_NUMBER_NOT_FOUND', phoneNumber);
            res(false);
          }
        });
      }),
    ]);

    try {
      await batch.commit();

      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      console.error('ERROR_LEAD_FAIL_NOTIFICATION', e);

      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: e.toString(),
        })
      );
    }
  }
}

const leadFailNotification = new LeadRemoveBroadcastNotification();
export default leadFailNotification;
