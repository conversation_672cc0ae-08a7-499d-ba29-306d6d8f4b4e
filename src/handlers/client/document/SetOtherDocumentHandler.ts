import { Request, Response } from 'express';
import multer from 'multer';
import { body } from 'express-validator';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import requestValidator from '../../../middlewares/requestValidator';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import { File } from '@google-cloud/storage';
import { IOtherDocumentFields } from '../../../types/services/profile_service_types';
import { profileService } from '../../../services/profileService';

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export interface SetOtherDocumentFields {
  documentName: string;
  expiredDate: Date | null;
  notes: string;
}

export interface SetOtherDocumentReqBody extends SetOtherDocumentFields {
  clientRef: string;
  imageUrl: string;
}

export default class SetOtherDocumentHandler {
  public static middlewares = [
    formDataParse.single('image'),
    body('documentName').notEmpty(),
    body('expiredDate').optional().toDate(),
    body('notes').optional(),
    body('clientRef').notEmpty(),
    requestValidator,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, SetOtherDocumentReqBody>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.get();

    const payload: IOtherDocumentFields = {
      DocumentName: req.body.documentName,
      ExpiredDate: req.body.expiredDate ?? null,
      Notes: req.body.notes ?? null,
    };

    let file: File | null = null;
    if (req.file?.buffer) {
      file = bucketTrimitraIdeal.file(`client-documents/${clientDocRef.id}/other-document.jpg`);
    }

    try {
      if (file) {
        if (req.file)
          await file.save(req.file.buffer, {
            public: true,
          });

        payload.ImageOtherDocument = file.publicUrl();
      } else {
        payload.ImageOtherDocument = req.body.imageUrl;
      }

      const update = await profileService.updateOtherDocument(
        payload,
        getClient.data()!.details.owner_phone_number
      );

      res.send(
        successResponse({
          type: 'CREATED',
          data: update?.data,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
