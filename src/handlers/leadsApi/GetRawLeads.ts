import { Request, RequestHandler, Response } from 'express';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { myFirestore } from '../../services/firebaseAdmin';
import successResponse from '../../schema/successResponse';
import { ResponseSchema } from '../../schema/types/ResponseSchema';

interface IParams {
  organization: string;
}

const getRawLeads: {
  middlewares: RequestHandler[];
  handler: RequestHandler<IParams, ResponseSchema>;
} = {
  middlewares: [leadsAuthorizer],
  handler: async function (req, res) {
    const organization = req.params.organization;
    const rawLeadsCollection = myFirestore.collection('raw_leads');

    const get = await rawLeadsCollection
      .where('organization', '==', organization)
      .orderBy('createdAt', 'desc')
      .get();

    const rawLeads: any[] = [];

    get.forEach(result => {
      const data = result.data();

      rawLeads.push({
        ...data,
        createdAt: data.createdAt.toDate(),
      });
    });

    res.send(
      successResponse({
        type: 'FETCHED',
        data: rawLeads,
      })
    );
  },
};

export default getRawLeads;
