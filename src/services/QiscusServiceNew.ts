import axios, { AxiosError } from 'axios';
import moment from 'moment';
import {
  IPayloadQiscusPostMessage,
  IQiscusResponsePostMessage,
  IQiscusSendMessageParams,
  IQiscusSendMessageParamsV2,
} from '../types/services/qiscus/qiscuss_service_new_types';

class QiscusServiceNew {
  private activeToken?: string;
  private lastLogin?: Date;

  private email: string = '<EMAIL>';
  private password: string = '4martahondaQ123';

  private appId = 'novi-r1k2gj0hgdkessf4';
  private secretKey = 'ca9bd4078b15a0905dbfdfd2eee75e4a';
  private whatsappChannelId = '856';

  private readonly qiscusBaseUrl = axios.create({
    baseURL: `https://multichannel.qiscus.com/whatsapp/v1/${this.appId}/${this.whatsappChannelId}`,
    headers: {
      'Qiscus-App-Id': this.appId,
      'Qiscus-Secret-Key': this.secretKey,
    },
  });

  private readonly qiscusAuthBaseUrl = axios.create({
    baseURL: 'https://multichannel.qiscus.com/api/v1/auth',
  });

  public async loginFirst(): Promise<void> {
    if (!this.activeToken) {
      await this.login();
    } else {
      const expiredTokenDate = moment(this.lastLogin).add(29, 'days');
      if (moment().isAfter(expiredTokenDate)) {
        await this.login();
      }
    }
  }

  public async sendMessageNative(params: IPayloadQiscusPostMessage) {
    await this.loginFirst();

    const body: IPayloadQiscusPostMessage = JSON.parse(JSON.stringify(params));

    if (body.text) {
      body.text.body = body.text.body.replace(/(<br>|<\/br>|<br \/>)/gim, '\n');
    }

    try {
      return await this.qiscusBaseUrl.post<IQiscusResponsePostMessage>('/messages', body);
    } catch (e: any) {
      throw e as AxiosError;
    }
  }

  public async sendMessage(params: IQiscusSendMessageParams) {
    // await this.loginFirst();

    const sendParam: IPayloadQiscusPostMessage = {
      to: params.target,
      recipient_type: 'individual',
      preview_url: false,
      type: 'text',
    };

    if (params.imageUrl) {
      sendParam.image = {
        link: params.imageUrl,
        caption: params.text ?? '',
      };
      sendParam.type = 'image';
    } else if (params.documentUrl) {
      sendParam.document = {
        link: params.documentUrl,
        caption: params.text ?? '',
        filename: params.documentName || 'File Dokumen',
      };
      sendParam.type = 'document';
    } else {
      sendParam.text = {
        body: params.text || '',
      };
    }

    try {
      const send = await this.qiscusBaseUrl.post<IQiscusResponsePostMessage>(
        '/messages',
        sendParam
      );

      return send;
    } catch (e: any) {
      const error = e as AxiosError<any>;
      const errorData = error.response?.data;
      if (errorData.error) {
        throw errorData.error.message;
      } else if (errorData.errors) {
        throw errorData.errors.message;
      } else {
        throw 'Error ketika mengirim pesan';
      }
    }
  }

  public sendMessageV2(params: IQiscusSendMessageParamsV2) {
    return this.qiscusBaseUrl.post('/messages', params);
  }

  public async getMedia(mediaId: string) {
    // await this.loginFirst();
    return await this.qiscusBaseUrl.get('/media/' + mediaId, {
      responseType: 'arraybuffer',
    });
  }

  public async uploadMedia(params: {
    file: File;
    type: string; // MIME type
  }) {
    // await this.loginFirst();
    const buffer = await params.file.arrayBuffer();
    return await this.qiscusBaseUrl.post<{ media: { id: string }[] }>('/media', buffer, {
      headers: {
        'Content-Type': params.type,
      },
    });
  }

  private async login() {
    try {
      const login = await this.qiscusAuthBaseUrl.post('', {
        email: this.email,
        password: this.password,
      });
      this.activeToken = login.data.data.user.authentication_token;
      this.lastLogin = new Date();
      this.qiscusBaseUrl.defaults.headers.common = {
        Authorization: this.activeToken,
        app_id: this.appId,
      };
    } catch (e: any) {
      const error = e as AxiosError;
      throw error;
    }
  }
}

export const qiscusServiceNew = new QiscusServiceNew();
