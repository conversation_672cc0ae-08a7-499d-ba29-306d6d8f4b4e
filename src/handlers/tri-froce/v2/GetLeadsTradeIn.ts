import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import { param } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import LeadsTradeInDocumentModel from '../../../model/LeadsTradeInDocumentModel';
import { HandlerTypes } from '../../../types/handler.types';
import LeadsModel from '../../../model/LeadsModel';

const getLeadsTradeIn: HandlerTypes<
  { organization: string; phoneNumber: string; agentCode: string },
  ResponseSchema
> = {
  middlewares: [
    tri<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    param('agentCode').notEmpty(),
    param('organization').notEmpty(),
    param('phoneNumber').notEmpty(),
    requestValidator,
  ],
  handler: async function (req, res) {
    try {
      const leadsCollection = myFirestore.collection('leads');
      const docName = `${req.params.organization}-${req.params.phoneNumber}`;
      const leadsDocRef = leadsCollection.doc(docName);

      // Check if the lead exists
      const leadsDoc = await leadsDocRef.withConverter(LeadsModel.converter).get();

      if (!leadsDoc.exists) {
        return res.status(404).send(
          errorResponse({
            type: 'ENTITY_NOT_FOUND',
            messages: 'Leads tidak ditemukan',
          })
        );
      }

      const leads = leadsDoc.data()!;

      if (leads.agentCode !== req.params.agentCode) {
        return res.status(404).send(
          errorResponse({
            type: 'ENTITY_NOT_FOUND',
            messages: 'Leads tidak ditemukan',
          })
        );
      }

      const leadsTradeInCollection = leadsDocRef.collection('trade_in');

      // Get all trade-in documents for this lead
      const tradeInDocs = await leadsTradeInCollection
        .withConverter(LeadsTradeInDocumentModel.converter)
        .get();

      if (tradeInDocs.empty) {
        return res.send(
          successResponse({
            type: 'FETCHED',
            data: [],
          })
        );
      }

      const tradeInData: LeadsTradeInDocumentModel[] = [];
      tradeInDocs.forEach(doc => {
        tradeInData.push(doc.data());
      });

      return res.send(
        successResponse({
          type: 'FETCHED',
          data: tradeInData.map(item => item.toJsonResponse()),
        })
      );
    } catch (error) {
      console.error('Error getting trade-in leads:', error);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Failed to retrieve trade-in data',
        })
      );
    }
  },
};

export default getLeadsTradeIn;
