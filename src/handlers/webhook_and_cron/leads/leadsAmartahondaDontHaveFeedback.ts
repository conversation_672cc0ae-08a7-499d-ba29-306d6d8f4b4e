import { RequestHand<PERSON> } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import collect, { Collection } from 'collect.js';
import successResponse from '../../../schema/successResponse';
import moment from 'moment';
import telegramServices from '../../../services/telegramServices';

const leadsAmartahondaDontHaveFeedback: {
  handler: RequestHandler;
} = {
  handler: async (req, res) => {
    const leadsCollection = myFirestore.collection('leads');
    const get = await leadsCollection
      .where('isTracking', '==', true)
      .where('organization', '==', 'amartahonda')
      .withConverter(LeadsModel.converter)
      .get();

    const leads: LeadsModel[] = [];

    get.forEach(result => {
      const data = result.data();
      if (!data.feedBackText && !data.feedBackVoice) {
        leads.push(data);
      }
    });

    const summaries: { agentName: string; agentCode: string; totalLeads: number }[] = [];

    const convertLeadsToCollection = collect(leads);
    const groupByAgentCode = convertLeadsToCollection.groupBy('agentCode');
    for (const key of Object.keys(groupByAgentCode.all())) {
      const firstElementItems = (
        groupByAgentCode.all()[key as any] as Collection<LeadsModel>
      ).first();
      const collectionElementItems = groupByAgentCode.all()[key as any] as Collection<LeadsModel>;

      summaries.push({
        totalLeads: collectionElementItems.count(),
        agentCode: firstElementItems.agentCode,
        agentName: firstElementItems.agentName,
      });
    }

    const now = moment();
    let messageTelegram =
      `=== LEADS BELUM ADA FEEDBACK ===` +
      `\nSemua yang ada di My Leads masih di track tetapi belum ada feedback.` +
      `\nTanggal: ${now.format('YYYY-MM-DD HH:mm')}.` +
      `\n`;
    for (const item of summaries) {
      messageTelegram += `\n- ${item.agentName.trim()} (${item.agentCode}): ${item.totalLeads} leads`;
    }

    telegramServices.sendMessage('-1001957142922', messageTelegram).then().catch();

    res.send(
      successResponse({
        data: summaries,
      })
    );
  },
};

export default leadsAmartahondaDontHaveFeedback;
