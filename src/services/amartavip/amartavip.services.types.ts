export interface IdealBypassLoginResponse {
  success: boolean;
  type: string;
  data: {
    token: string;
    uid: string;
    name: string;
    user_type: string;
    user_code: string;
    email: string;
    phone: string;
    affiliate_data: {
      affiliate_company_code: string;
      affiliate_company_name: string;
      affiliate_finco_id: string;
      affiliate_type: string;
    };
    coverage_area: {
      city_code: string | null;
      city_name: string | null;
      province_code: string;
      province_name: string;
    }[];
    default_organization_code: string;
    default_organization_id: string;
  };
}

export interface PlanCheckoutResponse {
  success: boolean;
  type: string;
  data: {
    transaction_id: string;
    bill_id: string;
    bill_reference_id: string;
    bill_amount: number;
    bill_data: string;
    user_uid: string;
    type: string;
    payload: {
      source: string;
      code: string;
      ads_option_qty: number;
      payment_issuer: string;
      payment_method: string;
      notes: string;
    };
    created_time: string;
    status: string;
  };
}

export interface PlanCheckoutStatusItem {
  transaction_id: string;
  bill_id: string;
  bill_reference_id: string;
  bill_amount: number;
  bill_data: string;
  user_uid: string;
  type: string;
  payload: {
    source: string;
    code: string;
    notes: string;
    ads_option_qty: number;
    payment_issuer: string;
    payment_method: string;
  };
  created_time: string;
  status: string;
}

export interface PlanCheckoutStatusResponse {
  success: boolean;
  type: string;
  data: PlanCheckoutStatusItem[];
}

export interface AdsPackage {
  source: string;
  code: string;
  ads_quantity: number;
  ads_used: number;
  created_time: string;
  days: number;
  price_type: string;
  condition: string[];
  category: string[];
  name: string;
  caption: string;
}

export interface AdsPackageResponse {
  success: boolean;
  type: string;
  data: AdsPackage[];
}

export interface LeadCheckoutResponse {
  success: boolean;
  type: string;
  data: LeadCheckoutItem[];
}

export interface LeadCheckoutItem {
  transaction_id: string;
  bill_id: string;
  bill_reference_id: string;
  bill_amount: number;
  bill_data: string;
  user_uid: string;
  type: string;
  payload: {
    lead_code: string;
    notes: string;
    payment_issuer: string;
    price: number;
    organization: string;
    source: string;
    payment_method: string;
  };
  created_time: string;
  status: string;
}
