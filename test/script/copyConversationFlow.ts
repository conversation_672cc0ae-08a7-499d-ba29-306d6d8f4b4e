import { myFirestore } from '../../src/services/firebaseAdmin';

async function copyConversationFlow() {
  const docSource = myFirestore.doc(
    '/projects/v8GGopG1v89nd46aajEX/conversation_flow/6cd2cdbb-2547-4a7a-a3c3-0c08d619c3b8'
  );
  const collectionTarget = myFirestore.doc(
    '/projects/kpsDdEcRtReOQrCYkzq2/conversation_flow/6cd2cdbb-2547-4a7a-a3c3-0c08d619c3b8'
  );

  const getSourceData = await docSource.get();
  const sourceData = getSourceData.data()!;

  try {
    await collectionTarget.set(sourceData);
  } catch (e) {
    console.log(e);
  }
}

copyConversationFlow();
