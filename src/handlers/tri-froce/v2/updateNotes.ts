import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import { body } from 'express-validator';
import { json, Request, Response } from 'express';
import requestValidator from '../../../middlewares/requestValidator';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import fetchAgent from '../../../services/agent/fetchAgent';
import telegramServices from '../../../services/telegramServices';
import hidePhoneNumber from '../../../helpers/hidePhoneNumber';
import { leadsNotesCounter } from '../../../helpers/leads/leadsNotes';
import moment, { Moment } from 'moment';

interface ReqBody {
  agentCode: string;
  organization: string;
  notes: string;
  phoneNumber: string;
  updatedBy: string;
  followUpScheduledAt?: number;
}

const updateNotes = {
  middlewares: [
    json(),
    triForceAuthChecker,
    body('agentCode').notEmpty(),
    body('organization').notEmpty(),
    body('notes').notEmpty(),
    body('phoneNumber').notEmpty(),
    body('updatedBy').notEmpty(),
    body('followUpScheduledAt').optional().isInt().toInt(),
    requestValidator,
  ],

  handler: async function (req: Request<any, ResponseSchema, ReqBody>, res: Response) {
    const leadsNotesCollection = myFirestore.collection('leads_notes');
    const countLeadsNotes = await leadsNotesCollection
      .where('phoneNumber', '==', req.body.phoneNumber)
      .get();

    const limit = 50;
    if (countLeadsNotes.size > limit) {
      return res.status(404).send(
        errorResponse({
          type: 'REACHED_MAX_LIMIT',
          messages: `Leads ini sudah di update notes lebih dari ${limit} kali.`,
        })
      );
    }

    const collections = myFirestore.collection('leads');
    const find = await collections
      .where('agentCode', '==', req.body.agentCode)
      .where('phoneNumber', '==', req.body.phoneNumber)
      .where('organization', '==', req.body.organization)
      .withConverter(LeadsModel.converter)
      .get();

    if (find.size == 0) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
        })
      );
    }

    let leads!: LeadsModel;

    find.forEach(result => {
      leads = result.data();
    });

    let agentName: string | null;
    if (!leads.agentName) {
      try {
        const fetch = await fetchAgent(leads.agentCode);
        agentName = fetch.name;
      } catch (e: any) {
        agentName = 'NO_NAME';
      }
    } else {
      agentName = leads.agentName;
    }

    const notesCounter = await leadsNotesCounter(leads);
    let followUpScheduledAt: Moment | null = null;
    if (req.body.followUpScheduledAt) {
      followUpScheduledAt = moment().add(req.body.followUpScheduledAt, 'days');
    }

    let notes: ILeadsNotesDocument<Date> = {
      agentCode: req.body.agentCode,
      event: 'triforceNotes',
      notes: req.body.notes,
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: null,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
      followUpScheduledAt: followUpScheduledAt?.toDate() ?? null,
    };

    leads.agentName = agentName;
    leads.notes = req.body.notes;
    leads.updateHistories.push({
      ...notes,
    });

    leads.followUpScheduledAt = followUpScheduledAt?.toDate() ?? null;

    try {
      const batch = myFirestore.batch();
      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      if (notes) {
        batch.set(myFirestore.collection('leads_notes').doc(), notes);
      }
      await batch.commit();

      // if(leads.organization === "amartachery") {
      // 	let messageTelegram = `=== LEADS UPDATE NOTES ===` +
      // 		`\n` +
      // 		`\nNama Depan: ${leads.firstName}` +
      // 		`\nNama Akhir: ${leads.lastName}` +
      // 		`\nNomor Telepon: ${hidePhoneNumber(leads.phoneNumber)}` +
      // 		`\nNama Agen: ${leads.agentName}` +
      // 		`\nKode Agen: ${leads.agentCode}` +
      // 		`\nNotes: ${leads.notes}` +
      // 		`\nModel: ${leads.vehicleOptions.map(value => value.model.name).join(", ")}`
      // 	;
      //
      // 	telegramServices.sendMessage ("-1002118167928", messageTelegram)
      // 		.then()
      // 		.catch()
      // }

      res.send(
        successResponse({
          type: 'UPDATED',
          data: req.body,
        })
      );
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }
  },
};

export default updateNotes;
