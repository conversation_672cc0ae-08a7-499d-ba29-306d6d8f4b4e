import { ValidationError, validationResult } from 'express-validator';
import { NextFunction, Request, Response } from 'express';
import errorResponse from '../schema/errorResponse';

const requestValidatorWithCb =
  (params?: { notValidCb?: (req: Request, e: Record<string, ValidationError>) => void }) =>
  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      try {
        params?.notValidCb?.(req, errors.mapped());
      } catch (e) {}
      res.status(422).send(
        errorResponse({
          type: 'UNPROCESSABLE_ENTITY',
          messages: errors.mapped(),
        })
      );
    } else {
      next();
    }
  };

export default function (req: Request, res: Response, next: NextFunction) {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    res.status(422).send(
      errorResponse({
        type: 'UNPROCESSABLE_ENTITY',
        messages: errors.mapped(),
      })
    );
  } else {
    next();
  }
}

export { requestValidatorWithCb };
