import { json, Request, Response } from 'express';
import moment, { Moment } from 'moment';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import fetchAgent from '../../../services/agent/fetchAgent';
import successResponse from '../../../schema/successResponse';
import ColdLeadsModel from '../../../model/ColdLeadsModel';
import aggregatesAgent from '../../../helpers/leads/aggregatesAgent';
import { leadsNotesCounter } from '../../../helpers/leads/leadsNotes';

interface ReqBody {
  path: string;
  messageId: string;
  createdAt: Moment;
  text: string;
}

const leadsAnswerNotificationHandler = {
  middlewares: [
    json(),
    body('path').notEmpty(),
    body('messageId').notEmpty(),
    body('text').notEmpty().trim(),
    body('createdAt')
      .notEmpty()
      .customSanitizer(input => moment(input)),
    requestValidator,
  ],
  handler: async function (req: Request<any, ResponseSchema, ReqBody>, res: Response) {
    const coldLeadsCollection = myFirestore.collection('cold_leads');
    const coldLeadsHistoriesCollection = myFirestore.collection('cold_leads_histories');
    const get = await myFirestore.doc(req.body.path).withConverter(LeadsModel.converter).get();

    if (!get.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
        })
      );
    }

    let leads: LeadsModel = get.data()!;

    if (leads.whatsapp) {
      leads.whatsapp.response = {
        createdAt: req.body.createdAt.toDate(),
        text: req.body.text,
      };
    }

    let agentName: string | null = null;
    if (!leads.agentName) {
      try {
        const fetch = await fetchAgent(leads.agentCode);
        agentName = fetch.name;
      } catch (e: any) {
        agentName = 'NO_NAME';
      }
    } else {
      agentName = leads.agentName;
    }

    const notesCounter = await leadsNotesCounter(leads);
    let notes: ILeadsNotesDocument<Date> = {
      agentCode: leads.agentCode,
      event: 'whatsappAnswer',
      notes: `Leads membalas pesan Whatsapp: ${req.body.text}`,
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: null,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.agentName = agentName;
    leads.notes = notes.notes;
    leads.updateHistories.push({
      ...notes,
    });

    let coldLeads: ColdLeadsModel | undefined;
    if (req.body.text.toUpperCase() === 'Hentikan promosi'.toUpperCase()) {
      leads.isTracking = false;
      coldLeads = new ColdLeadsModel({
        ...leads,
        tradeIn: null,
        moveToColdAt: new Date(),
        moveToColdBy: 'SYSTEM',
        coldNotes: notes.notes,
        moveToColdByUserType: 'admin',
      });
    } else if (
      req.body.text.toUpperCase() === 'Ya, Terimakasih'.toUpperCase() &&
      ['amartachery', 'amartaneta', 'amartavinfast'].indexOf(leads.organization) > -1
    ) {
      leads.statusLevel = 1;
    }

    try {
      const batch = myFirestore.batch();

      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      batch.set(myFirestore.collection('leads_notes').doc(), notes);

      if (coldLeads) {
        batch.set(
          coldLeadsCollection.doc(leads.ref.id).withConverter(ColdLeadsModel.converter),
          coldLeads
        );
        batch.set(
          coldLeadsHistoriesCollection.doc().withConverter(ColdLeadsModel.converter),
          coldLeads
        );
        batch.delete(leads.ref);
      }

      await batch.commit();
      await aggregatesAgent(leads.agentCode, leads.organization).then();

      res.send(
        successResponse({
          type: 'UPDATED',
          data: req.body,
        })
      );
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }
  },
};

export default leadsAnswerNotificationHandler;
