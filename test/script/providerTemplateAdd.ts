import { firestore } from 'firebase-admin';
import { myFirestore } from '../../src/services/firebaseAdmin';

const providerTemplateAdd = () => {
  const templateName = 'finco_order_notif_ver3';
  const data = {
    body: 'NEW ORDER {{1}}, atas nama {{2}} dengan kode order {{3}} dan kode order leasing {{4}} no telp pemohon {{5}} janji survey {{6}} lokasi survey {{7}}',
    createdAt: firestore.Timestamp.now(),
    idealInitMessage: false,
    variables: [
      {
        name: '<PERSON>a Admin CMO',
      },
      {
        name: '<PERSON><PERSON>',
      },
      {
        name: '<PERSON><PERSON> Offer',
      },
      {
        name: 'Kode Order Leasing',
      },
      {
        name: 'No Telepon Pemohon',
      },
      {
        name: 'Tanggal Survey',
      },
      {
        name: 'Link Google Map lokasi survey',
      },
    ],
  };

  const doc = myFirestore.doc('/projects/v8GGopG1v89nd46aajEX/provider_templates/' + templateName);
  doc.set(data).then(value => {
    console.log('SUCCESS', value);
  });
};

providerTemplateAdd();
