import { myFirestore } from '../../../src/services/firebaseAdmin';
import dayjs from 'dayjs';
import ChatRoom from '../../../src/model/ChatRoom';
import { IProject } from '../../../src/types/firestore/project.types';
import { firestore } from 'firebase-admin';
import ClientModel from '../../../src/model/ClientModel';

const fillCityGroupScript = async () => {
  const today = dayjs();
  const startDate = today.subtract(4).startOf('day');
  const endDate = today.endOf('day');

  const projectsCollection = myFirestore.collection('projects');
  const clientsCollection = myFirestore.collection('clients');

  const getAllProjects = await projectsCollection.where('active', '==', true).get();
  const projects: (IProject & { ref: firestore.DocumentReference })[] = [];

  getAllProjects.forEach(result => {
    projects.push({
      ...(result.data() as any),
      ref: result.ref,
    });
  });

  const chatRooms: ChatRoom[] = [];

  for (const project of projects) {
    const chatRoomsCollection = project.ref.collection('chat_rooms');
    const getAllChatRooms = await chatRoomsCollection.withConverter(ChatRoom.converter)
      .where('created_at', '>=', startDate.toDate())
      .where('created_at', '<=', endDate.toDate())
      .get();

    getAllChatRooms.forEach(result => {
      const data = result.data()!;
      if (data.organization) {
        chatRooms.push(data);
      }
    });
  }

  console.log(chatRooms.length);

  const batch = myFirestore.batch();
  for (const chatRoom of chatRooms) {
    const getClients = await clientsCollection
      .where('contacts.whatsapp', '==', chatRoom.contacts[0])
      .withConverter(ClientModel.converter)
      .get();

    if (getClients.empty) continue;

    const client = getClients.docs[0].data()!;
    if (!client.profile.area?.text) continue;

    batch.update(chatRoom.ref, {
      cityGroup: client.profile.area.text,
    });
  }
  await batch.commit();
};

fillCityGroupScript();
