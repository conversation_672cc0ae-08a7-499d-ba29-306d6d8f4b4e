import { json, Request, Response } from 'express';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import errorResponse from '../../schema/errorResponse';
import LeadsModel from '../../model/LeadsModel';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';
import successResponse from '../../schema/successResponse';
import moment, { Moment } from 'moment';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import ColdLeadsModel from '../../model/ColdLeadsModel';
import { leadsNotesCounter } from '../../helpers/leads/leadsNotes';
import { ISpkToLeadsTable } from '../../types/services/bigQuery/spkToLeadsTable.types';
import { bigQuery, leadsToSpkTable } from '../../services/bigQueryService';
import { v4 } from 'uuid';

interface ReqBody {
  phoneNumber: string;
  source2: string;

  spkCreatedAt: Moment;
  spkNumber: string;

  paymentPlan: string;

  notes: string;

  provinceName: string;
  provinceCode: string;
  cityName: string;
  cityCode: string;

  vehicleOptions: {
    brand: {
      name: string;
    };
    model: {
      name: string;
    };
    variant: {
      code: string;
      name: string;
    };
    color: {
      code: string;
      name: string;
    };
  }[];
}

const addLeadsFromOffer = {
  middlewares: [
    // leadsAuthorizer,
    json(),
    body('notes').optional(),
    body('spkNumber').notEmpty(),
    body('spkCreatedAt')
      .notEmpty()
      .custom(input => {
        const check = moment(input, 'YYYY-MM-DD HH:mm');
        if (!check.isValid()) {
          throw new Error('Format tanggal harus YYYY-MM-DD HH:mm');
        }

        return true;
      })
      .withMessage('Format tanggal harus YYYY-MM-DD HH:mm')
      .customSanitizer(input => {
        return moment(input, 'YYYY-MM-DD HH:mm');
      }),
    body('phoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('source2').notEmpty().trim().withMessage('Source2 is required'),

    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response) => {
    const coldLeadsCollection = myFirestore.collection('cold_leads');
    const coldLeadsHistoriesCollection = myFirestore.collection('cold_leads_histories');
    const collection = myFirestore.collection('leads');
    const docName = `${req.body.source2}-${req.body.phoneNumber}`;
    const getLeads = await collection.withConverter(LeadsModel.converter).doc(docName).get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const now = moment();

    const leads = getLeads.data()!;

    const notesCounter = await leadsNotesCounter(leads);
    const notesSpk: ILeadsNotesDocument<Date> = {
      notes: !req.body.notes ? 'SPK Berhasil dibuat' : req.body.notes,
      updatedAt: new Date(),
      updatedByUser: null,
      statusLevel: 5,
      agentCode: leads.agentCode,
      event: 'updateSpk',
      phoneNumber: req.body.phoneNumber,
      organization: req.body.source2,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: leads.agentName,
      moveToCold: true,

      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.notes = notesSpk.notes;
    leads.updatedAt = now.toDate();
    leads.isTracking = false;
    leads.spk = {
      spkNumber: req.body.spkNumber,
      createdAt: req.body.spkCreatedAt.toDate(),
    };
    leads.updateHistories.push({
      ...notesSpk,
    });

    const coldLeads = new ColdLeadsModel({
      ...leads,
      tradeIn: null,
      moveToColdAt: now.toDate(),
      moveToColdBy: 'SYSTEM',
      coldNotes: notesSpk.notes,
      moveToColdByUserType: 'admin',
    });

    try {
      const batch = myFirestore.batch();
      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      batch.set(myFirestore.collection('leads_notes').doc(), notesSpk);
      batch.set(
        coldLeadsCollection.doc(docName).withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.set(
        coldLeadsHistoriesCollection.doc().withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.delete(leads.ref);
      await batch.commit();

      await aggregatesAgent(leads.agentCode, leads.organization).then();
    } catch (e: any) {
      console.log(e);
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }

    try {
      const uuidV4 = v4();
      const insertToBq: ISpkToLeadsTable = {
        agentCode: leads.agentCode,
        agentName: leads.agentCode,
        createdAt: bigQuery.timestamp(now.toDate()),
        domicile: {
          cityCode: leads.domicile?.cityCode ?? '',
          cityName: leads.domicile?.cityName ?? '',
          provinceCode: leads.domicile?.provinceCode ?? '',
          provinceName: leads.domicile?.provinceName ?? '',
        },
        domicileSpk: {
          cityCode: req.body.cityCode ?? '',
          cityName: req.body.cityName ?? '',
          provinceCode: req.body.provinceCode ?? '',
          provinceName: req.body.provinceName ?? '',
        },
        firstName: leads.firstName,
        lastName: leads.lastName,
        organization: leads.organization,
        paymentPlan: req.body.paymentPlan,
        phoneNumber: leads.phoneNumber,
        source: leads.source,
        spkNumber: req.body.spkNumber,
        uuid: uuidV4,
        vehicles: leads.vehicleOptions.map(value => {
          return {
            brand: {
              code: value.brand.name,
            },
            model: {
              name: value.model.name,
            },
            variant: {
              name: value.variant.name,
              code: value.variant.code,
            },
            color: {
              name: value.color.name || '',
              code: value.color.code || '',
            },
          };
        }),
        vehicleSpk: null,
      };

      if (req.body.vehicleOptions && req.body.vehicleOptions.length > 0) {
        insertToBq.vehicleSpk = {
          brand: {
            code: req.body.vehicleOptions[0].brand.name,
          },
          model: {
            name: req.body.vehicleOptions[0].model.name,
          },
          variant: {
            name: req.body.vehicleOptions[0].variant.name,
            code: req.body.vehicleOptions[0].variant.code,
          },
          color: {
            name: req.body.vehicleOptions[0].color.name,
            code: req.body.vehicleOptions[0].color.code,
          },
        };
      }

      await leadsToSpkTable.insert(insertToBq);
    } catch (e) {
      console.log('ERROR_INSERT_BQ_SPK_LEADS', JSON.stringify(req.body), JSON.stringify(e), e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
        })
      );
    }

    res.send(
      successResponse({
        data: null,
        type: 'UPDATED',
      })
    );
  },
};

export default addLeadsFromOffer;
