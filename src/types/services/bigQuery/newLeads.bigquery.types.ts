import { BigQueryTime } from '@google-cloud/bigquery';

export interface NewLeadsBigqueryTypes {
  source: string;
  organization: string;
  city_group: string | null;
  lead_phone_number: string;
  lead_name: string;
  lead_email: string | null;
  agent_code: string;
  agent_name: string;
  agent_phone_number: string | null;
  lead_conversation_count_inbound: number;
  lead_conversation_count_outbound: number;
  lead_conversation_resume: string | null;
  lead_conversation_file_url: string;
  create_date_time: BigQueryTime;

  lead_conversation_start_time: BigQueryTime;
  lead_conversation_end_time: BigQueryTime;
  lead_conversation_inbound_start_time: BigQueryTime;
  lead_conversation_outbound_start_time: BigQueryTime;
  lead_conversation_inbound_end_time: BigQueryTime;
  lead_conversation_outbound_end_time: BigQueryTime;

  province_code: string | null;
  province_name: string | null;
  city_code: string | null;
  city_name: string | null;
  vehicle_usage: string | null;
  payment_plan: string | null;
  has_vehicle_loan: boolean | null;
  notes: string | null;
  type: string;
  event_source: string;
}
