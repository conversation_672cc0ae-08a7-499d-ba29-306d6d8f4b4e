import { json, Request, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';
import moment from 'moment';

interface RequestBody {
  agentCode: string;
  phoneNumber: string;
  type: 'in' | 'out';
  date: Date;
}

const inboundOutboundWWJSCounterHandler = {
  middlewares: [
    json(),
    body('agentCode').notEmpty(),
    body('phoneNumber').notEmpty(),
    body('type').notEmpty(),
    body('date')
      .notEmpty()
      .customSanitizer(input => {
        return moment(input).toDate();
      }),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, RequestBody>, res: Response) => {
    const collection = myFirestore.collection('leads');
    const get = await collection
      .where('phoneNumber', '==', req.body.phoneNumber)
      .where('agentCode', '==', req.body.agentCode)
      .orderBy('updatedAt', 'desc')
      .withConverter(LeadsModel.converter)
      .get();

    let leads!: LeadsModel;
    if (get.empty) {
      return res.send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    } else {
      get.forEach(result => {
        leads = result.data();
      });
    }

    const inbound = leads.webWhatsapp?.inbound.total ?? 0;
    const outbound = leads.webWhatsapp?.outbound.total ?? 0;

    let finalIn = inbound;
    let finalOut = outbound;
    switch (req.body.type) {
      case 'in':
        finalIn++;
        break;
      case 'out':
        finalOut++;
        break;
    }

    if (leads.webWhatsapp) {
      switch (req.body.type) {
        case 'in':
          leads.webWhatsapp.inbound = {
            total: finalIn,
            updatedAt: req.body.date,
          };
          break;
        case 'out':
          leads.webWhatsapp.outbound = {
            total: finalOut,
            updatedAt: req.body.date,
          };
          break;
      }
    } else {
      leads.webWhatsapp = {
        inbound: {
          total: finalIn,
          updatedAt: new Date(),
        },
        outbound: {
          total: finalOut,
          updatedAt: new Date(),
        },
      };
    }

    try {
      await leads.ref.withConverter(LeadsModel.converter).set(leads);
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: `Error: ${e.toString()}`,
        })
      );
    }
  },
};

export default inboundOutboundWWJSCounterHandler;
