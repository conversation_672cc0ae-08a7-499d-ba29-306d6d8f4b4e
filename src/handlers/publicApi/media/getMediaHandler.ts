import { query } from 'express-validator';
import { HandlerTypes } from '../../../types/handler.types';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import errorResponse from '../../../schema/errorResponse';
import { qiscusServiceNew } from '../../../services/QiscusServiceNew';
import metaServices from '../../../services/MetaServices';
import axios from 'axios';
import mime from 'mime-types';

/**
 * Query parameters interface for getting media
 */
interface ReqQuery {
  /** Project identifier */
  projectId: string;
  /** Media identifier */
  mediaId: string;
}

/**
 * Handler for getting media files from different providers (Qiscus or Meta)
 */
const getMediaHandler: HandlerTypes<any, any, any, ReqQuery> = {
  middlewares: [query('projectId').notEmpty(), query('mediaId').notEmpty(), requestValidator],
  handler: async (req, res) => {
    try {
      // Get project data
      const projectRef = myFirestore.doc(`projects/${req.query.projectId}`);
      const projectSnapshot = await projectRef.get();

      if (!projectSnapshot.exists) {
        return res.status(404).send(
          errorResponse({
            type: 'NOT_FOUND',
            messages: 'Project not found',
          })
        );
      }

      const projectData = projectSnapshot.data()!;
      let response;

      // Get media based on provider
      if (projectData.provider === 'qiscus') {
        response = await qiscusServiceNew.getMedia(req.query.mediaId);
      } else if (projectData.provider === 'meta') {
        if (!projectData.meta) {
          return res.status(400).send(
            errorResponse({
              type: 'INVALID_REQUEST',
              messages: 'Meta configuration not found',
            })
          );
        }
        response = await metaServices.getFile(req.query.mediaId, projectData.meta);
      } else {
        return res.status(400).send(
          errorResponse({
            type: 'INVALID_REQUEST',
            messages: 'Unsupported provider',
          })
        );
      }

      // Determine content type and file name
      const contentType = response.headers['content-type'] || 'application/octet-stream';
      const extension = mime.extension(contentType) || 'bin';
      const fileName = `media_${Date.now()}.${extension}`;

      // Set response headers
      res.setHeader('Content-Disposition', `inline; filename="${fileName}"`);
      res.contentType(contentType);
      return res.send(response.data);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('Get media error:', error.response?.data);
      } else {
        console.error('Get media error:', error);
      }
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Failed to get media',
        })
      );
    }
  },
};

export default getMediaHandler;
