import axios from 'axios';
import {
  IBusinessDocumentFields,
  IdCardFields,
  IdCardSpouseGuarantorFields,
  IFamilyRegisterFields,
  IIncomeDocumentFields,
  IOtherDetailRequestPayload,
  IOtherDocumentFields,
  IPlaceOfBusinessFields,
  IPlaceToStayFields,
  IProfileRequestPayload,
  ISelfieFields,
  TProfileRequestPayload,
  TProfileResponse,
} from '../types/services/profile_service_types';

class ProfileService {
  private key = 'lgDRR2ltg45JOyuvcWxWe8NRdNnJP4YS30xonWNh';
  private readonly base = axios.create({
    baseURL: 'https://xqfrf2v5m0.execute-api.ap-southeast-1.amazonaws.com/v1',
    headers: {
      'X-api-key': this.key,
    },
  });

  public async getProfile(phoneNumber: string) {
    try {
      return this.base.get<TProfileResponse[]>('/customer/' + phoneNumber);
    } catch (e: any) {
      throw e;
    }
  }

  public async updateProfile(params: IProfileRequestPayload, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          ...params,
          phone: phoneNumber,
        },
        phoneNumber
      );
    } catch (e: any) {
      throw e;
    }
  }

  public async updateOtherProfile(
    params: Partial<IOtherDetailRequestPayload>,
    phoneNumber: string
  ) {
    try {
      return this.updatePartial(
        {
          ...params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  public async updateIdCardSpouseGuarantor(
    params: IdCardSpouseGuarantorFields,
    phoneNumber: string
  ) {
    try {
      return this.updatePartial(
        {
          idCardSpouse: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }
  public async updateIdCard(params: IdCardFields, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          idCard: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  public async updateFamilyRegister(params: IFamilyRegisterFields, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          familyRegister: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  public async updateIncomeDocument(params: IIncomeDocumentFields, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          incomeDocument: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  public async updateBusinessDocument(params: IBusinessDocumentFields, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          businessDocument: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  public async updatePlaceToStay(params: IPlaceToStayFields, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          placeToStay: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  public async updatePlaceOfBusiness(params: IPlaceOfBusinessFields, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          placeOfBusiness: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  public async updateSelfie(params: ISelfieFields, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          selfie: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  public async updateOtherDocument(params: IOtherDocumentFields, phoneNumber: string) {
    try {
      return this.updatePartial(
        {
          otherDocument: params,
        },
        phoneNumber
      );
    } catch (e: any) {}
  }

  private async updatePartial(partial: Partial<TProfileRequestPayload>, phoneNumber: string) {
    try {
      const get = await this.getProfile(phoneNumber);

      let profileRequestPayload: TProfileRequestPayload = {
        customerName: '',
        phone: '',
        cityGroup: null,
        idCard: null,
        idCardSpouse: null,
        ownedVehicles: null,
        familyRegister: null,
        incomeDocument: null,
        businessDocument: null,
        placeToStay: null,
        placeOfBusiness: null,
        selfie: null,
        otherDocument: null,
      };

      if (get.data.length === 1) {
        const {
          Phone,
          CustomerName,
          CityGroup,
          IdCard,
          IdCardSpouse,
          OwnedVehicles,
          FamilyRegister,
          IncomeDocument,
          BusinessDocument,
          PlaceToStay,
          PlaceOfBusiness,
          Selfie,
          OtherDocument,
        } = get.data[0];
        profileRequestPayload = {
          customerName: CustomerName,
          phone: Phone,
          cityGroup: CityGroup || null,
          ownedVehicles: OwnedVehicles || null,
          idCard: IdCard || null,
          idCardSpouse: IdCardSpouse || null,
          familyRegister: FamilyRegister || null,
          incomeDocument: IncomeDocument || null,
          businessDocument: BusinessDocument || null,
          placeToStay: PlaceToStay || null,
          placeOfBusiness: PlaceOfBusiness || null,
          selfie: Selfie || null,
          otherDocument: OtherDocument || null,
        };
      }

      profileRequestPayload = { ...profileRequestPayload, ...partial };

      const finalRequestBody = Object.assign({}, profileRequestPayload, partial);

      return this.base.post<TProfileResponse>('/customer', finalRequestBody);
    } catch (e: any) {
      throw e;
    }
  }
}

export const profileService = new ProfileService();
