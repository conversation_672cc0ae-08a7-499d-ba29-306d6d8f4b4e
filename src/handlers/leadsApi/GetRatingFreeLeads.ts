import { HandlerTypes } from '../../types/handler.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { json } from 'express';
import { body, query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import FreeLeadsRating from '../../model/FreeLeadsRating';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { decrypt } from '../../helpers/encryption';
import FreeLeadsModel from '../../model/FreeLeadsModel';

interface ReqBody {
  phoneNumber: string;
  organization: string;
}

interface ReqQuery {
  encrypted?: boolean;
}

const GetRatingFreeLeads: HandlerTypes<any, ResponseSchema, ReqBody, ReqQuery> = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('phoneNumber').notEmpty().trim(),
    body('organization').notEmpty().trim(),
    query('encrypted').optional().isBoolean().toBoolean(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const { organization } = req.body;

    let phoneNumber = req.body.phoneNumber;
    if (req.query.encrypted) {
      phoneNumber = decrypt(phoneNumber);
    }

    const docName = `${organization}-${phoneNumber}`;
    const docRefFreeLeads = myFirestore.collection('free_leads').doc(docName);

    // Check if the free lead exists
    const getFreeLeadsQuery = await docRefFreeLeads.withConverter(FreeLeadsModel.converter).get();

    if (!getFreeLeadsQuery.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Free Leads tidak ditemukan',
        })
      );
    }

    let ratings = [];
    let freeLeadData;

    try {
      // Get the ratings collection
      const ratingsSnapshot = await docRefFreeLeads.collection('ratings')
        .withConverter(FreeLeadsRating.converter)
        .orderBy('createdAt', 'desc')
        .get();

      // Get the free lead data
      freeLeadData = getFreeLeadsQuery.data();

      // Extract ratings
      ratings = ratingsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          agentName: data.agentName,
          agentCode: data.agentCode,
          organization: data.organization,
          rating: data.rating,
          createdAt: data.createdAt.toDate(),
        };
      });
    } catch (error) {
      console.error('Error getting ratings for free lead:', error);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Failed to get ratings',
          data: {
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        })
      );
    }

    // Return the ratings along with the average rating
    res.send(
      successResponse({
        data: {
          phoneNumber: req.body.phoneNumber,
          organization,
          averageRating: freeLeadData?.rating?.average || 0,
          totalRatings: freeLeadData?.rating?.total || 0,
          ratings,
        },
        type: 'FETCHED',
      })
    );
  },
};

export default GetRatingFreeLeads;
