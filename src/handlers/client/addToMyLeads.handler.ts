import { HandlerTypes } from '../../types/handler.types';
import authChecker from '../../middlewares/authChecker';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import ClientModel from '../../model/ClientModel';
import LeadsModel from '../../model/LeadsModel';
import AdminModel from '../../model/AdminModel';
import fetchAgent from '../../services/agent/fetchAgent';
import moment from 'moment';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import { errorLogsAddLeads } from '../../helpers/leads/errorLogs';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import { firestore } from 'firebase-admin';
import { json } from 'express';

interface ReqBody {
  clientRefPath: string;
  projectRefPath: string;
  chatRoomRefPath: string;
  organization: string;
}

const addToMyLeadsHandler: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    authChecker,
    json(),
    body('clientRefPath').notEmpty(),
    body('projectRefPath').notEmpty(),
    body('chatRoomRefPath').notEmpty(),
    body('organization').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const leadsCollection = myFirestore.collection('leads');
    const clientDoc = myFirestore.doc(req.body.clientRefPath);
    const chatRoomDoc = myFirestore.doc(req.body.chatRoomRefPath);
    const getClientData = await clientDoc.withConverter(ClientModel.converter).get();

    const now = moment();

    const dataClient = getClientData.data()!;
    const admin = res.locals.account as AdminModel;
    const agent = await fetchAgent(admin.amartaVip?.mediatorCode || '');
    const leadDocId = `${req.body.organization}-${dataClient.contacts.whatsapp}`;

    const myLeads = await leadsCollection.doc(leadDocId).withConverter(LeadsModel.converter).get();
    if (myLeads.exists) {
      const data = myLeads.data()!;

      if (data.agentCode === admin.amartaVip?.mediatorCode) {
        dataClient.acquiredLeadsStatus = {
          agentCode: admin.amartaVip?.mediatorCode || '',
          agentName: agent.name || '',
          acquired: true,
          acquiredAt: firestore.Timestamp.fromDate(data.createdAt),
          organization: req.body.organization,
        };

        const batch = myFirestore.batch();
        batch.set(clientDoc.withConverter(ClientModel.converter), dataClient);
        batch.update(chatRoomDoc, {
          exclusive_admin: {
            email: admin.email,
            ref: admin.ref,
            name: admin.name,
            updatedAt: now.toDate(),
          },
        });
        await batch.commit();

        return res.send(
          successResponse({
            data: data.toJsonResponse(),
            type: 'CREATED',
          })
        );
      }

      return res.status(500).send(
        errorResponse({
          type: 'UNPROCESSABLE',
          messages: 'Nomor telepon ini sudah jadi Leads yang diakuisisi.',
        })
      );
    }

    const notes: ILeadsNotesDocument<Date> = {
      notes: `Leads bersumber dari Akuisisi IDEAL`,
      updatedAt: now.toDate(),
      updatedByUser: null,
      statusLevel: 0,
      agentCode: admin.amartaVip?.mediatorCode || '',
      event: 'init',
      phoneNumber: dataClient.contacts.whatsapp,
      organization: req.body.organization,
      firstName: dataClient.profile.name,
      lastName: '',
      agentName: agent?.name || '',
      moveToCold: false,
      reactivate: {
        currentTotal: 0,
      },
      totalUpdateNotes: 1,
    };

    const leadsDoc = new LeadsModel({
      createdAt: now.toDate(),
      agentCode: admin.amartaVip?.mediatorCode || '',
      area: dataClient.profile.area?.text || '',
      agentName: agent.name,
      phoneNumberAgent: agent?.phones.map(value => value.phone) ?? [],
      downPaymentPlan: dataClient.survey?.credit_scheme?.down_payment || null,
      domicile: null,
      email: null,
      firstName: dataClient.profile.name,
      lastName: '',
      hasVehicleLoan: false,
      paymentPlan: null,
      phoneNumber: dataClient.contacts.whatsapp,
      ref: leadsCollection.doc(leadDocId),
      source: `ideal`,
      organization: req.body.organization,
      title: 'kak',
      vehicleOptions: [],
      vehicleUsage: 'individual',
      statusLevel: 0,

      purchasePlan: 'firstVehicle',
      nextTotalVehicleOwnerShip: '1',

      isTracking: true,
      notes: notes.notes,

      updateHistories: [
        {
          ...notes,
        },
      ],

      idCard_number: null,
      driverLicense_number: null,
      whatsapp: null,
      fromFreeLeads: false,
      freeLeadsCreatedAt: null,
    });

    dataClient.acquiredLeadsStatus = {
      agentCode: admin.amartaVip?.mediatorCode || '',
      agentName: admin.amartaVip?.mediatorName || '',
      acquired: true,
      acquiredAt: firestore.Timestamp.fromDate(now.toDate()),
      organization: req.body.organization,
    };

    try {
      const batch = myFirestore.batch();
      batch.set(leadsDoc.ref.withConverter(LeadsModel.converter), leadsDoc);
      batch.set(clientDoc.withConverter(ClientModel.converter), dataClient);
      batch.set(myFirestore.collection('leads_notes').doc(), notes);
      batch.update(chatRoomDoc, {
        exclusive_admin: {
          email: admin.email,
          ref: admin.ref,
          name: admin.name,
        },
      });
      await batch.commit();
    } catch (e: any) {
      console.log(e);
      errorLogsAddLeads(req, e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }

    res.send(
      successResponse({
        data: leadsDoc.toJsonResponse(),
        type: 'CREATED',
      })
    );
  },
};

export default addToMyLeadsHandler;
