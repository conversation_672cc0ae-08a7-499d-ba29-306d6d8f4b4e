import authChecker from '../../middlewares/authChecker';
import { HandlerTypes } from '../../types/handler.types';
import busboyMiddleware from '../../middlewares/busboyMiddleware';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import errorResponse from '../../schema/errorResponse';
import { IProject } from '../../types/firestore/project.types';
import { v4 } from 'uuid';
import { IFirestoreMessageEntity } from '../../types/firestore/messages/message_types';
import dayjs from 'dayjs';
import { firestore } from 'firebase-admin';
import { qiscusServiceNew } from '../../services/QiscusServiceNew';
import { IQiscusSendMessageParamsV2 } from '../../types/services/qiscus/qiscuss_service_new_types';
import metaServices from '../../services/MetaServices';
import { IMetaSendMessageParamsV2 } from '../../types/services/meta/metaApi.types';
import { firestoreService } from '../../services/FirestoreService';
import { IAdminSessionLog } from '../../types/firestore/admin_session_log_types';
import { ISessionLog, ISessionLogDataClient } from '../../types/firestore/session_log_types';
import successResponse from '../../schema/successResponse';
import { isAxiosError } from 'axios';
import { IChatRoomModel } from '../../types/firestore/i_chat_room_model';
import ChatRoom from '../../model/ChatRoom';

enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VIDEO = 'video',
  DOCUMENT = 'document',
}

interface ReqBody {
  roomPath: string;
  phoneNumber: string;
  adminSessionPath: string;
  text?: string | null;
  fileUrl?: string;
  fileType?: MessageType;
  filename?: string;
  fileMediaId?: string;
}

interface MediaPayload {
  type: MessageType;
  caption?: string;
  mediaId?: string;
  fileUrl?: string;
  filename?: string;
}

const handleProviderError = (error: any, provider: string) => {
  if (isAxiosError(error)) {
    console.error('Axios error:', error.response?.data);
  } else {
    console.error('Error:', error);
  }
  let errorMessage = `Failed to send message via ${provider}`;
  if (error instanceof Error) {
    errorMessage = error.message;
  }
  return errorResponse({
    type: 'SERVER_ERROR',
    messages: errorMessage,
  });
};

const getMessageTypeFromMime = (mimetype: string): MessageType => {
  if (mimetype.startsWith('image/')) return MessageType.IMAGE;
  if (mimetype.startsWith('video/')) return MessageType.VIDEO;
  if (mimetype === 'application/pdf') return MessageType.DOCUMENT;
  throw new Error('Unsupported file type');
};

const createRecentChatText = (type: MessageType, text?: string): string => {
  const prefix =
    type === MessageType.IMAGE
      ? '(Gambar) '
      : type === MessageType.VIDEO
        ? '(Video) '
        : type === MessageType.DOCUMENT
          ? '(Dokumen) '
          : '';
  return `${prefix}${text || ''}`;
};

const createMessageParams = (
  messageId: string,
  sessionId: string,
  account: any,
  mediaPayload?: MediaPayload
): IFirestoreMessageEntity => {
  const params: IFirestoreMessageEntity = {
    message: {
      id: messageId,
      direction: 'OUT',
      unixtime: new Date().getTime() / 1000,
      timestamp: firestore.Timestamp.now(),
      type: mediaPayload?.type || MessageType.TEXT,
    },
    statuses: {
      delivered: null,
      read: null,
      sent: firestore.Timestamp.now(),
      failed: null,
    },
    origin: {
      display_name: account.name,
      id: account.ref!.id,
      reference: account.ref as firestore.DocumentReference,
    },
    session_id: sessionId,
  };

  if (mediaPayload) {
    switch (mediaPayload.type) {
      case MessageType.IMAGE:
        params.message.image = {
          id: mediaPayload.mediaId || '',
          link: mediaPayload.fileUrl || '',
          caption: mediaPayload.caption,
        };
        break;
      case MessageType.VIDEO:
        params.message.video = {
          id: mediaPayload.mediaId || '',
          link: mediaPayload.fileUrl || '',
          caption: mediaPayload.caption,
        };
        break;
      case MessageType.DOCUMENT:
        params.message.document = {
          id: mediaPayload.mediaId || '',
          link: mediaPayload.fileUrl || '',
          caption: mediaPayload.caption,
          filename: mediaPayload.filename || 'document.pdf',
        };
        break;
      case MessageType.TEXT:
        params.message.text = {
          body: mediaPayload.caption || '',
        };
        break;
    }
  }

  return params;
};

const sendMessageV2Handler: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    authChecker,
    busboyMiddleware,
    body('roomPath').notEmpty(),
    body('phoneNumber').notEmpty(),
    body('adminSessionPath').notEmpty(),
    body('fileUrl').optional(),
    body('fileType')
      .optional()
      .custom((value, { req }) => {
        if (req.body.fileUrl && !value) {
          throw new Error('fileType is required when fileUrl is provided');
        }
        if (value && !Object.values(MessageType).includes(value)) {
          throw new Error('Invalid fileType. Must be one of: image, video, document');
        }
        return true;
      }),
    body('filename')
      .optional()
      .custom((value, { req }) => {
        if (req.body.fileType === MessageType.DOCUMENT && !value) {
          throw new Error('filename is required for document type');
        }
        return true;
      }),
    body('text').custom((value, { req }) => {
      if (!req.file && !req.body.fileUrl && !req.body.fileMediaId && !value) {
        throw new Error('Text is required when no file or file media is provided');
      }
      return true;
    }),
    body('fileMediaId').optional().isNumeric(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const roomDocRef = myFirestore.doc(req.body.roomPath);
    const adminSessionDocRef = myFirestore.doc(req.body.adminSessionPath);
    const projectDocRef = roomDocRef.parent.parent;
    const chatsCollection = roomDocRef.collection('chats');

    if (!projectDocRef) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Project not found',
        })
      );
    }

    let messageId = '';
    const rawText = req.body.text ?? '';

    const sanitizedText = rawText
      .replace(/<br\s*\/?>/gi, '\n') // Ubah tag <br> menjadi newline
      .replace(/<\/?[^>]+(>|$)/g, ''); // Hapus tag HTML lainnya
    req.body.text = sanitizedText;

    const [roomGet, lastInboundMessage, adminSessionGet, projectGet] = await Promise.all([
      new Promise<ChatRoom | null>(async resolve => {
        const get = await roomDocRef.withConverter(ChatRoom.converter).get();
        if (get.exists) {
          resolve(get.data()!);
        } else {
          resolve(null);
        }
      }),
      chatsCollection
        .where('message.direction', '==', 'IN')
        .orderBy('message.timestamp', 'desc')
        .limit(1)
        .get(),
      adminSessionDocRef.get(),
      projectDocRef.get(),
    ]);

    if (!roomGet) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Room not found',
        })
      );
    }

    const projectData = projectGet.data()! as IProject;
    const roomData = roomGet;

    let sessionId = v4();
    if (lastInboundMessage.size === 1) {
      lastInboundMessage.forEach(result => {
        const data = result.data() as IFirestoreMessageEntity;
        const timestamp = dayjs((data.message.timestamp as firestore.Timestamp).toDate());
        if (timestamp.add(24, 'hours').isAfter(dayjs())) {
          if (data.session_id) sessionId = data.session_id;
        }
      });
    }

    let mediaId = req.body.fileMediaId || '';
    let messageType = MessageType.TEXT;

    try {
      if (req.file) {
        messageType = getMessageTypeFromMime(req.file.mimetype);
        switch (projectData.provider) {
          case 'qiscus': {
            const upload = await qiscusServiceNew.uploadMedia({
              file: new File([req.file.buffer], req.file.originalname),
              type: req.file.mimetype,
            });
            mediaId = upload.data.media[0].id;
            break;
          }
          case 'meta': {
            const upload = await metaServices.uploadMedia(
              {
                file: new File([req.file.buffer], req.file.originalname),
                type: req.file.mimetype,
                messagingProduct: 'whatsapp',
              },
              projectData.meta!
            );
            mediaId = upload.data.id;
            break;
          }
        }
      } else if (req.body.fileType) {
        messageType = req.body.fileType;
      }

      const mediaPayload: MediaPayload = {
        type: messageType,
        caption: sanitizedText,
        mediaId: mediaId || undefined,
        fileUrl: req.body.fileUrl,
        filename: req.body.filename,
      };

      switch (projectData.provider) {
        case 'qiscus': {
          const qiscusPayload: IQiscusSendMessageParamsV2 = {
            to: req.body.phoneNumber,
            type: messageType,
            preview_url: false,
            recipient_type: 'individual',
          };

          if (messageType !== MessageType.TEXT) {
            switch (messageType) {
              case MessageType.IMAGE:
                qiscusPayload.image = {
                  id: mediaId || undefined,
                  link: req.body.fileUrl || undefined,
                  caption: sanitizedText,
                };
                break;
              case MessageType.VIDEO:
                qiscusPayload.video = {
                  id: mediaId || undefined,
                  link: req.body.fileUrl || undefined,
                  caption: sanitizedText,
                };
                break;
              case MessageType.DOCUMENT:
                qiscusPayload.document = {
                  id: mediaId || undefined,
                  link: req.body.fileUrl || undefined,
                  caption: sanitizedText,
                  filename: req.body.filename || 'document.pdf',
                };
                break;
            }
          } else {
            qiscusPayload.text = { body: sanitizedText };
          }

          const sendMessage = await qiscusServiceNew.sendMessageV2(qiscusPayload);
          messageId = sendMessage.data.messages[0].id;
          break;
        }
        case 'meta': {
          const metaPayload: IMetaSendMessageParamsV2 = {
            messaging_product: 'whatsapp',
            recipient_type: 'individual',
            to: roomData.contacts[0],
            type: messageType,
          };

          if (messageType !== MessageType.TEXT) {
            switch (messageType) {
              case MessageType.IMAGE:
                metaPayload.image = {
                  id: mediaId || '',
                  link: req.body.fileUrl || '',
                  caption: sanitizedText,
                };
                break;
              case MessageType.VIDEO:
                metaPayload.video = {
                  id: mediaId || '',
                  link: req.body.fileUrl || '',
                  caption: sanitizedText,
                };
                break;
              case MessageType.DOCUMENT:
                metaPayload.document = {
                  id: mediaId || '',
                  link: req.body.fileUrl || '',
                  caption: sanitizedText,
                  filename: req.body.filename || 'document.pdf',
                };
                break;
            }
          } else {
            metaPayload.text = {
              body: sanitizedText,
              preview_url: false,
            };
          }

          const sendMessage = await metaServices.sendMessageV2(metaPayload, projectData.meta!);
          messageId = sendMessage.data.messages[0].id;
          break;
        }
        default: {
          throw new Error('Unsupported provider');
        }
      }

      const messageParams = createMessageParams(
        messageId,
        sessionId,
        res.locals.account,
        mediaPayload
      );

      const recentChat: IChatRoomModel['recent_chat'] = {
        contact: res.locals.account.email ?? 'Admin',
        direction: 'OUT',
        display_name: 'Admin',
        read: false,
        statuses: {
          failed: null,
          sent: firestore.Timestamp.now(),
          read: null,
          delivered: null,
        },
        text: createRecentChatText(messageType, sanitizedText),
        timestamp: firestore.Timestamp.now(),
        type: messageType,
        unixtime: dayjs().unix(),
      };

      // Check 24-hour window
      if (lastInboundMessage.size === 1) {
        lastInboundMessage.forEach(result => {
          const data = result.data() as IFirestoreMessageEntity;
          if (
            !dayjs((messageParams.message.timestamp as firestore.Timestamp).toDate()).isBefore(
              dayjs((data.message.timestamp as firestore.Timestamp).toDate()).add(24, 'hours')
            )
          ) {
            messageParams.error = {
              href: '',
              title:
                'Message failed to send because more than 24 hours have passed since the customer last replied to this number',
              code: 402,
            };
          }
        });
      }

      const clientSessionDoc = myFirestore.collection('client_session_logs').doc(sessionId);
      const getClientSessionDoc = await clientSessionDoc.get();

      const batch = myFirestore.batch();

      const messageRef = await firestoreService.addNewMessage(
        messageParams,
        roomDocRef,
        messageId,
        batch
      );

      await firestoreService.updateRoomRecentChat(recentChat, roomDocRef, batch);

      if (!messageParams.error && adminSessionGet.exists) {
        const dataAdminSession = adminSessionGet.data() as IAdminSessionLog;
        batch.update(adminSessionDocRef, {
          messages: [
            ...dataAdminSession.messages,
            {
              origin: messageParams.origin,
              message: messageParams.message,
              ref: messageRef,
            },
          ],
        });
      }

      if (getClientSessionDoc.exists) {
        const sessionLog = getClientSessionDoc.data() as ISessionLog<ISessionLogDataClient>;
        sessionLog.messages.push({
          message: messageParams.message,
          origin: messageParams.origin,
        });

        batch.update(clientSessionDoc, {
          messages: sessionLog.messages,
        });
      }

      await batch.commit();

      return res.send(
        successResponse({
          type: 'CREATED',
          data: {
            messageId,
          },
        })
      );
    } catch (error) {
      return res.status(500).send(handleProviderError(error, projectData.provider));
    }
  },
};

export default sendMessageV2Handler;
