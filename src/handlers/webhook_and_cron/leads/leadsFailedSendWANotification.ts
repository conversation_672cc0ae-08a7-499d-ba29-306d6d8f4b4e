import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import { myFirestore } from '../../../services/firebaseAdmin';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import { leadsNotesCounter } from '../../../helpers/leads/leadsNotes';
import ColdLeadsModel from '../../../model/ColdLeadsModel';
import aggregatesAgent from '../../../helpers/leads/aggregatesAgent';
import successResponse from '../../../schema/successResponse';

interface ReqBody {
  path: string;
}

const leadsFailedSendWANotification: {
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
} = {
  handler: async (req, res) => {
    const coldLeadsCollection = myFirestore.collection('cold_leads');
    const coldLeadsHistoriesCollection = myFirestore.collection('cold_leads_histories');

    const leadsDoc = myFirestore.doc(req.body.path);
    const getLeads = await leadsDoc.withConverter(LeadsModel.converter).get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;
    const notesCounter = await leadsNotesCounter(leads);
    let notes: ILeadsNotesDocument<Date> = {
      agentCode: leads.agentCode,
      event: 'moveToCold',
      notes:
        'Move to Cold - Gagal mengirim template Whatsapp sehingga dianggap nomor telepon tidak memiliki akun Whatasapp',
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: null,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: leads.agentName,
      moveToCold: true,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.isTracking = false;
    leads.notes = notes.notes;
    leads.updateHistories.push({
      ...notes,
    });

    const coldLeads = new ColdLeadsModel({
      ...leads,
      moveToColdAt: new Date(),
      moveToColdBy: 'SYSTEM',
      coldNotes: notes.notes,
      moveToColdByUserType: 'admin',
    });

    try {
      const batch = myFirestore.batch();
      batch.set(
        coldLeadsCollection.doc(leads.ref.id).withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.set(
        coldLeadsHistoriesCollection.doc().withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.set(myFirestore.collection('leads_notes').doc(), notes);

      batch.delete(leads.ref);

      await batch.commit();

      await aggregatesAgent(leads.agentCode, leads.organization).then();

      // if (coldLeads.organization === "amartachery") {
      //     let messageTelegram = `=== LEADS UPDATE NOTES - COLD ===` +
      //         `\n` +
      //         `\nNama Depan: ${leads.firstName}` +
      //         `\nNama Akhir: ${leads.lastName}` +
      //         `\nNomor Telepon: ${hidePhoneNumber(leads.phoneNumber)}` +
      //         `\nNama Agen: ${leads.agentName}` +
      //         `\nKode Agen: ${leads.agentCode}` +
      //         `\nNotes: ${notes.notes}` +
      //         `\nModel: ${leads.vehicleOptions.map(value => value.model.name).join(", ")}`
      //     ;
      //     telegramServices.sendMessage("-1002118167928", messageTelegram)
      //         .then()
      //         .catch()
      // }
    } catch (e: any) {
      console.log('ERROR_MOVE_TO_COLD', JSON.stringify(leads.toJsonResponse()), e);
      return res.send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }

    res.send(
      successResponse({
        data: coldLeads.toJsonResponse(),
        type: 'CREATED',
      })
    );
  },
};

export default leadsFailedSendWANotification;
