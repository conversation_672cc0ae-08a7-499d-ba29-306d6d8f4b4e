import { j<PERSON>, Request, RequestHand<PERSON>, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../../middlewares/requestValidator';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import moment from 'moment';
import { myFirestore } from '../../../services/firebaseAdmin';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import { firestore } from 'firebase-admin';
import { bigQuery, rawLeadsTable } from '../../../services/bigQueryService';
import { RawLeadsTable } from '../../../types/services/bigQuery/rawLeadsTable.types';
import { v4 as uuidV4 } from 'uuid';
import ClientModel from '../../../model/ClientModel';
import leadsAuthorizer from '../../../middlewares/leadsAuthorizer';
import randomString from '../../../helpers/randomString';

interface ReqBody {
  externalId: string;
  area: string;
  title: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  email: string;
  provinceName: string;
  provinceCode: string;
  cityName: string;
  cityCode: string;
  vehicleUsage: 'individual' | 'shared' | 'corporate';
  paymentPlan: 'cash' | 'credit';
  hasVehicleLoan: boolean;
  vehicleOptions:
    | {
        brand: {
          name: string;
        };
        model: {
          name: string;
        };
        variant: {
          code: string;
          name: string;
        };
        color: {
          code: string;
          name: string;
        };
      }[]
    | null;
  organization: string;
  source: string;
  purchasePlan: 'firstVehicle' | 'vehicleReplacement' | 'vehicleAddition';
  nextTotalVehicleOwnerShip: string;

  price: number;
  notes: string;

  idCard_number: string | null;
  driverLicense_number: string | null;

  disableWhatsapp: boolean | null;

  ideal: {
    chat_room_ref: string | null;
  };
}

const addFreeLeads: {
  middlewares: RequestHandler[];
  handler: RequestHandler;
} = {
  middlewares: [
    leadsAuthorizer,
    json(),
    // (req, res, next) => {
    //     console.log("AddFreeLeads.ts", JSON.stringify(req.body))
    //     next()
    // },
    body('externalId').optional(),
    body('disableWhatsapp')
      .optional()
      .isBoolean()
      .withMessage('disableWhatsapp Harus true atau false')
      .toBoolean(),
    body('title').optional({ values: 'falsy' }),
    body('area').optional({ values: 'falsy' }).toUpperCase(),
    body('firstName').notEmpty(),
    body('lastName')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? '' : input)),
    body('phoneNumber')
      .notEmpty()
      .isMobilePhone('id-ID')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('email').optional({ values: 'falsy' }).isEmail(),
    body('provinceName').notEmpty().withMessage('Province Name dibutuhkan'),
    body('provinceCode').notEmpty().withMessage('Province Code dibutuhkan'),
    body('cityName').notEmpty().withMessage('City Name dibutuhkan'),
    body('cityCode').notEmpty().withMessage('City Code dibutuhkan'),
    body('vehicleUsage').optional({ checkFalsy: true }).isIn(['individual', 'shared', 'corporate']),
    body('paymentPlan').optional({ values: 'falsy' }).isIn(['cash', 'credit']),
    body('hasVehicleLoan')
      .customSanitizer(input => (!input ? false : input))
      .isBoolean()
      .toBoolean(),
    body('vehicleOptions')
      .optional({ values: 'falsy' })
      .isArray({ max: 3 })
      .custom((value: ReqBody['vehicleOptions']) => {
        if (value) {
          for (const option of value) {
            if (!option.brand?.name || !option.model?.name) {
              throw new Error('Data kendaraan yang dikirim kurang lengkap');
            }
          }
        }
        return true;
      }),
    body('organization')
      .notEmpty()
      .isIn(['amartahonda', 'amartachery', 'amartaneta', 'amartavinfast'])
      .withMessage(
        'Organization hanya boleh diisi amartahonda, amartachery, amartaneta, dan amartavinfast '
      )
      .customSanitizer(input => input.replace(/ /g, '')),
    body('purchasePlan')
      .optional({ values: 'falsy' })
      .isIn(['firstVehicle', 'vehicleReplacement', 'vehicleAddition']),
    body('nextTotalVehicleOwnerShip').optional({ values: 'falsy' }),
    body('idCard_number')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? null : input)),
    body('driverLicense_number')
      .optional({ values: 'falsy' })
      .customSanitizer(input => (!input ? null : input)),

    body('price').optional({ values: 'falsy' }).isNumeric().toInt(),

    body('source').notEmpty().withMessage('Source dibutuhkan'),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response<ResponseSchema>) => {
    const now = moment();
    const freeLeadsCollection = myFirestore.collection('free_leads');
    const leadsCollection = myFirestore.collection('leads');
    const rawLeadsCollection = myFirestore.collection('raw_leads');

    const bigQueryRows: RawLeadsTable[] = [];
    const rawLeadsDocs: firestore.DocumentReference[] = [];

    const body = req.body;

    const freeLeadsDocName = `${body.organization}-${body.phoneNumber}`;

    const acquiredLeads = await leadsCollection.doc(freeLeadsDocName).get();
    const findInRawLeads = await rawLeadsCollection
      .where('phoneNumber', '==', body.phoneNumber)
      .get();
    const findFreeLeads = await freeLeadsCollection
      .doc(freeLeadsDocName)
      .withConverter(FreeLeadsModel.converter)
      .get();

    if (acquiredLeads.exists) {
      return res.status(409).send(
        errorResponse({
          type: 'UNPROCESSABLE',
          messages: 'Sudah ada My Leads dan sudah diakusisi',
        })
      );
    } else if (findFreeLeads.exists) {
      return res.status(409).send(
        errorResponse({
          type: 'UNPROCESSABLE',
          messages: 'Sudah ada di Free Leads',
        })
      );
    }

    const batchAddFreLeads = myFirestore.batch();

    const rawLeadsDoc = rawLeadsCollection.doc();
    const rawLeads: any = {
      externalId: req.body.externalId || randomString(20),
      phoneNumber: body.phoneNumber,
      fullName: `${body.firstName} ${body.lastName}`,
      city: body.cityName,
      vehicleModel: body.vehicleOptions?.[0]?.model.name ?? null,
      vehicleVariant: body.vehicleOptions?.[0]?.variant.name ?? null,
      organization: body.organization,

      externalData: {
        ...req.body,
      },
      source: body.source || 'form-free-leads',

      inFreeLeads: findFreeLeads.exists,
      shouldPushToFreeLeadsAt: now.clone().add(15, 'minutes').toDate(),
      freeLeadsPath: findFreeLeads.exists ? findFreeLeads.ref : null,
      freeLeadsCreatedAt: findFreeLeads.exists ? findFreeLeads.get('createdAt') : null,

      shouldPushToBigQueryAt: now.clone().add(15, 'minutes').toDate(),
      donePushToBigQuery: false,
      donePushToBigQueryAt: null,

      createdAt: now.toDate(),

      duplicated: !findInRawLeads.empty,
      mappedData: {
        domicile: {
          provinceName: body.provinceName,
          provinceCode: body.provinceCode,
          cityName: body.cityName,
          cityCode: body.cityCode,
        },
        vehicle: body.vehicleOptions?.[0] ?? null,
        area: req.body.area,
        paymentPlan: body.paymentPlan || null,
      },
      whatsapp: null,
      alreadyInMyLeads: acquiredLeads.exists,
      codingFile: 'AddFreeLeads.ts',
    };
    batchAddFreLeads.create(rawLeadsDoc, rawLeads);
    rawLeadsDocs.push(rawLeadsDoc);

    const freeLeadsDocRef: firestore.DocumentReference = freeLeadsCollection.doc(freeLeadsDocName);

    let price = body.organization === 'amartahonda' ? 20000 : 50000;
    let isCustomPrice = false;

    if (body.price) {
      if (
        ['amartachery', 'amartaneta', 'amartavinfast'].indexOf(body.organization) > 0 &&
        body.price > 50000
      ) {
        price = body.price;
        isCustomPrice = true;
      } else if (body.organization === 'amartahonda' && body.price > 20000) {
        price = body.price;
        isCustomPrice = true;
      }
    }

    const freeLeads = new FreeLeadsModel({
      rawLeadsRef: rawLeadsDoc,
      area: req.body.area,
      externalId: req.body.externalId,
      title: body.title,
      firstName: body.firstName,
      lastName: body.lastName,
      phoneNumber: body.phoneNumber,
      paymentPlan: body.paymentPlan,
      hasVehicleLoan: body.hasVehicleLoan,
      createdAt: now.toDate(),

      domicile: {
        cityCode: body.cityCode,
        cityName: body.cityName,
        provinceCode: body.provinceCode,
        provinceName: body.provinceName,
      },

      driverLicense_number: body.driverLicense_number,
      idCard_number: body.idCard_number,
      email: body.email,
      nextTotalVehicleOwnerShip: body.nextTotalVehicleOwnerShip,
      organization: body.organization as any,
      purchasePlan: body.purchasePlan,
      source: body.source || 'form-free-leads',
      vehicleOptions: body.vehicleOptions ?? [],
      vehicleUsage: body.vehicleUsage,
      ref: freeLeadsDocRef,

      isAcquired: false,
      acquiredAt: null,
      acquiredAgentCode: null,
      acquiredAgentName: null,
      price,
      isCustomPrice: isCustomPrice,
      notes: body.notes,
      disableWhatsapp: req.body.disableWhatsapp || false,
      ideal: req.body.ideal
        ? {
            chat_room_ref: req.body.ideal.chat_room_ref
              ? myFirestore.doc(req.body.ideal.chat_room_ref)
              : null,
          }
        : null,
    });

    batchAddFreLeads.set(freeLeads.ref.withConverter(FreeLeadsModel.converter), freeLeads);

    const uuid = uuidV4();
    const bigQueryRow: RawLeadsTable = {
      city: body.cityName,
      city_group: req.body.area,
      duplicated: !findInRawLeads.empty,
      external_id: freeLeads.externalId,
      full_name: `${freeLeads.firstName} ${freeLeads.lastName}`,
      organization: freeLeads.organization,
      phone_number: freeLeads.phoneNumber,
      source: freeLeads.source,

      trimitra_city: freeLeads.domicile?.cityCode
        ? {
            code: freeLeads.domicile?.cityCode,
            name: freeLeads.domicile?.cityName,
          }
        : null,
      trimitra_province: freeLeads.domicile?.provinceCode
        ? {
            code: freeLeads.domicile?.provinceCode,
            name: freeLeads.domicile?.provinceName,
          }
        : null,

      trimitra_vehicle: {
        variant: {
          code: freeLeads.vehicleOptions?.[0]?.variant.code,
          name: freeLeads.vehicleOptions?.[0]?.variant.name,
        },
        model: {
          name: freeLeads.vehicleOptions?.[0]?.model.name,
        },
        color: {
          code: freeLeads.vehicleOptions?.[0]?.color?.code,
          name: freeLeads.vehicleOptions?.[0]?.color?.name,
        },
      },

      uid: uuid,
      vehicle_model: freeLeads.vehicleOptions?.[0]?.model.name,
      vehicle_variant: freeLeads.vehicleOptions?.[0]?.variant.name,
      whatsapp_delivered: null,
      whatsapp_delivered_at_timestamp: null,
      data_leads_created_at_timestamp: bigQuery.timestamp(now.toDate()),
      bq_created_at_datetime: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
      bq_created_at_timestamp: bigQuery.timestamp(now.toDate()),
      is_in_my_leads: acquiredLeads.exists,
    };

    bigQueryRows.push(bigQueryRow);

    const getClientIdeal = await myFirestore
      .collection('clients')
      .where('contacts.whatsapp', '==', req.body.phoneNumber)
      .withConverter(ClientModel.converter)
      .get();

    if (!getClientIdeal.empty) {
      let client!: ClientModel;
      getClientIdeal.forEach(result => {
        client = result.data();
      });

      client.freeLeadsStatus = {
        createdAt: firestore.Timestamp.fromDate(now.toDate()),
        submitted: true,
        organization: freeLeads.organization,
      };

      batchAddFreLeads.set(client.ref.withConverter(ClientModel.converter), client);
    }

    try {
      await batchAddFreLeads.commit();
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Terjadi kesalahan pada server',
          data: {
            messages: 'Terjadi kesalahan pada server',
            error: e?.toString(),
          },
        })
      );
    }

    try {
      const batchUpdateStatusBigQuery = myFirestore.batch();
      await rawLeadsTable.insert(bigQueryRows);
      for (const doc of rawLeadsDocs) {
        batchUpdateStatusBigQuery.update(doc, {
          donePushToBigQuery: true,
          donePushToBigQueryAt: now.toDate(),
        });
      }
      await batchUpdateStatusBigQuery.commit();
    } catch (e) {
      console.log('FAILED_TO_INSERT_RAW_LEADS_FORM', JSON.stringify(e), e);
    }

    // Disable Dulu
    // if (!acquiredLeads.exists && req.body.organization === "amartahonda" && !req.body.disableWhatsapp) {
    //     sendTemplate.sendTemplateMetaV2({
    //         projectId: "WLdKug7hau0MbRzKcnqg",
    //         template_name: "leads1",
    //         bindDocuments: [
    //             {
    //                 path: rawLeadsDoc.path,
    //                 context: "add_raw_leads",
    //             },
    //             {
    //                 path: freeLeads.ref.path,
    //                 context: "add_leads",
    //             }
    //         ],
    //         components: [
    //             {
    //                 "type": "body",
    //                 "parameters": [
    //                     {
    //                         "type": "text",
    //                         "text": `${req.body.firstName}`,
    //                     },
    //                     {
    //                         "type": "text",
    //                         "text": "amartahonda.com"
    //                     },
    //                     {
    //                         "type": "text",
    //                         "text": rawLeads.mappedData.vehicle.model.name,
    //                     }
    //                 ]
    //             }
    //         ],
    //         contactName: req.body.firstName,
    //         target: req.body.phoneNumber,
    //         area:  req.body.area || undefined,
    //         vehicle: {
    //             model_name: req.body.vehicleOptions?.[0].model.name || "",
    //             variant_name: req.body.vehicleOptions?.[0].variant.name || "",
    //             variant_code: req.body.vehicleOptions?.[0].variant.code || "",
    //             color_code: req.body.vehicleOptions?.[0].color?.code || "",
    //             color_name: req.body.vehicleOptions?.[0].color?.name || "",
    //             year: "",
    //         }
    //
    //     })
    //         .then()
    //         .catch()
    //
    // }

    res.send(
      successResponse({
        data: null,
        type: 'CREATED',
      })
    );
  },
};

export default addFreeLeads;
