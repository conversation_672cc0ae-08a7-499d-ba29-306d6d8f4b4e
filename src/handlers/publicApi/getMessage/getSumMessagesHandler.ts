import { query } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { Request, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import { myFirestore } from '../../../services/firebaseAdmin';
import { firestore } from 'firebase-admin';
import successResponse from '../../../schema/successResponse';

interface IRequestQuery {
  phoneNumber: string[];
}

interface ISumMessages {
  phone_number: string;
  count_messages: number;
}

export default class GetSumMessagesHandler {
  public static middlewares = [
    query('phoneNumber').isArray({
      min: 1,
    }),
    requestValidator,
  ];

  public static async handler(
    req: Request<null, ResponseSchema, null, IRequestQuery>,
    res: Response<ResponseSchema>
  ) {
    const sanitizedPhoneNumbers = req.query.phoneNumber.map(value => {
      return phoneNumberCountryCodeSanitizer(value, '62', '62');
    });

    const projectRef = myFirestore.doc('/projects/v8GGopG1v89nd46aajEX');

    const sumMessages: ISumMessages[] = [];

    for (const phoneNumber of sanitizedPhoneNumbers) {
      const findChatRoom = await projectRef
        .collection('chat_rooms')
        .where('contacts', 'array-contains', phoneNumber)
        .get();

      if (findChatRoom.empty) {
        continue;
      }

      let chatRoomRef!: firestore.DocumentReference<firestore.DocumentData>;
      findChatRoom.forEach(result => {
        chatRoomRef = result.ref;
      });

      const getInboundMessage = await chatRoomRef.collection('chats').get();

      let count = getInboundMessage.size;

      sumMessages.push({
        count_messages: count,
        phone_number: phoneNumber,
      });
    }

    let sumAllMessages = 0;

    sumMessages.forEach(value => {
      sumAllMessages += value.count_messages;
    });

    res.send(
      successResponse({
        type: 'FETCHED',
        data: sumMessages,
        meta: {
          verified: sumAllMessages > 0,
          sum_count: sumAllMessages,
        },
      })
    );
  }
}
