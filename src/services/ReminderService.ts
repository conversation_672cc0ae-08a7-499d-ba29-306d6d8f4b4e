import axios from 'axios';
import {
  ICreateReminderParams,
  ICreateReminderSuccessResponse,
} from '../types/services/reminderServices.types';

class ReminderService {
  private base = axios.create({
    baseURL: 'https://dlh1y5kkh6.execute-api.ap-southeast-3.amazonaws.com/v1',
    headers: {
      'x-api-key': 'YoNCgaeOHN83vYfSM1QLc1rujF3BQyvO4cSHK5mz',
    },
  });

  async createReminder(params: ICreateReminderParams) {
    return this.base.post<ICreateReminderSuccessResponse>('/notification', params);
  }
}

const reminderService = new ReminderService();

export default reminderService;
