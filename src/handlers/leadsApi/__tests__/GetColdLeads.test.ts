import request from 'supertest';
import express from 'express';
import getColdLeads from '../GetColdLeads';

// Mock dependencies
jest.mock('../../../services/firebaseAdmin', () => ({
  myFirestore: {
    collection: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      withConverter: jest.fn().mockReturnThis(),
      get: jest.fn().mockResolvedValue({
        forEach: jest.fn((callback) => {
          // Mock some test data
          const mockData = {
            data: () => ({
              agentCode: 'TEST001',
              moveToColdAt: new Date('2024-01-01'),
              toJsonResponse: () => ({ id: 'test1', agentCode: 'TEST001' })
            })
          };
          callback(mockData);
        })
      })
    }))
  }
}));

jest.mock('../../../middlewares/leadsAuthorizer', () => (req: any, res: any, next: any) => next());

const app = express();
app.use(express.json());
app.get('/test-cold-leads', getColdLeads.middlewares, getColdLeads.handler);

describe('GetColdLeads Handler', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Parameter Validation', () => {
    it('should require startDate parameter', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          endDate: '2024-01-31 23:59'
        });

      expect(response.status).toBe(422);
      expect(response.body.error.messages.startDate).toBeDefined();
    });

    it('should require endDate parameter', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-01 00:00'
        });

      expect(response.status).toBe(422);
      expect(response.body.error.messages.endDate).toBeDefined();
    });

    it('should validate date format', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: 'invalid-date',
          endDate: '2024-01-31 23:59'
        });

      expect(response.status).toBe(422);
      expect(response.body.error.messages.startDate).toBeDefined();
    });

    it('should validate pagination parameters', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-01 00:00',
          endDate: '2024-01-31 23:59',
          page: '0', // Invalid: should be >= 1
          limit: '10'
        });

      expect(response.status).toBe(422);
      expect(response.body.error.messages.page).toBeDefined();
    });

    it('should validate limit parameter range', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-01 00:00',
          endDate: '2024-01-31 23:59',
          page: '1',
          limit: '1001' // Invalid: should be <= 1000
        });

      expect(response.status).toBe(422);
      expect(response.body.error.messages.limit).toBeDefined();
    });
  });

  describe('Successful Requests', () => {
    it('should handle basic request with camelCase parameters', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-01 00:00',
          endDate: '2024-01-31 23:59'
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBeDefined();
      expect(response.body.success.data).toBeInstanceOf(Array);
      expect(response.body.success.meta.startDate).toBe('2024-01-01 00:00');
      expect(response.body.success.meta.endDate).toBe('2024-01-31 23:59');
    });

    it('should handle agentCodes parameter', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-01 00:00',
          endDate: '2024-01-31 23:59',
          agentCodes: ['TEST001', 'TEST002']
        });

      expect(response.status).toBe(200);
      expect(response.body.success.meta.agentCodes).toEqual(['TEST001', 'TEST002']);
    });

    it('should handle pagination when both page and limit are provided', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-01 00:00',
          endDate: '2024-01-31 23:59',
          page: '1',
          limit: '10'
        });

      expect(response.status).toBe(200);
      expect(response.body.success.meta.pagination).toBeDefined();
      expect(response.body.success.meta.pagination.page).toBe(1);
      expect(response.body.success.meta.pagination.limit).toBe(10);
    });

    it('should not paginate when only page is provided', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-01 00:00',
          endDate: '2024-01-31 23:59',
          page: '1'
        });

      expect(response.status).toBe(200);
      expect(response.body.success.meta.pagination).toBeUndefined();
    });

    it('should not paginate when only limit is provided', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-01 00:00',
          endDate: '2024-01-31 23:59',
          limit: '10'
        });

      expect(response.status).toBe(200);
      expect(response.body.success.meta.pagination).toBeUndefined();
    });
  });

  describe('Date Range Validation', () => {
    it('should reject when endDate is before startDate', async () => {
      const response = await request(app)
        .get('/test-cold-leads')
        .query({
          startDate: '2024-01-31 23:59',
          endDate: '2024-01-01 00:00'
        });

      expect(response.status).toBe(422);
      expect(response.body.error.messages).toBe('endDate must be after startDate');
    });
  });
});
