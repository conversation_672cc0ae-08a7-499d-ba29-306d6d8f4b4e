import { firestore } from 'firebase-admin';

export interface IAdminDocument<TIME = Date, REF = firestore.DocumentReference> {
  ref: REF;
  active: boolean;
  address: string;
  created_time: TIME;
  doc_admin: REF;
  doc_department: REF | null;
  doc_project: REF;
  project: {
    name: string;
    provider: string;
  } | null;
  department: {
    name: string;
  } | null;
  email: string;
  last_session_active: null | number;
  level: 'owner' | 'admin';
  name: string;
  phone_number: string;
  createdBy: string | null;
  createdSource: string | null;
  amartaVip: {
    mediatorName: string;
    mediatorCode: string;
  } | null;
}
