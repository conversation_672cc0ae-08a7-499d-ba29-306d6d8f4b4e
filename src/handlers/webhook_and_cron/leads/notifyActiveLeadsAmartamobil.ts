import { HandlerTypes } from '../../../types/handler.types';
import { myFirestore } from '../../../services/firebaseAdmin';
import successResponse from '../../../schema/successResponse';
import LeadsModel from '../../../model/LeadsModel';
import telegramServices from '../../../services/telegramServices';

const telegram: { [key: string]: string } = {
  amartachery: '-4559632746',
  amartaneta: '-4576842832',
};

const notifyActiveLeadsAmartamobil: HandlerTypes = {
  middlewares: [],
  handler: async (req, res) => {
    const organizations = ['amartachery', 'amartaneta', 'amartavinfast'];

    const collectionLeads = myFirestore.collection('leads');

    for (const organization of organizations) {
      const get = await collectionLeads
        .where('organization', '==', organization)
        .orderBy('createdAt', 'desc')
        .withConverter(LeadsModel.converter)
        .limit(100)
        .get();

      let activeLeads: LeadsModel[] = [];

      get.forEach(result => {
        activeLeads.push(result.data());
      });

      let messageBody = `== ACTIVE LEADS - ${organization.toUpperCase()} ==`;
      let count = 0;
      for (const activeLead of activeLeads) {
        count++;
        messageBody += `\n${sanitizeName(activeLead.firstName, activeLead.lastName)}-${activeLead.agentCode}`;
      }

      await telegramServices
        .sendMessage(telegram[organization], messageBody)
        .then(value => {
          // console.log("SUKSES", organization, value);
        })
        .catch(reason => {
          // console.log("ERROR", organization, reason);
        });
    }

    res.send(
      successResponse({
        data: null,
        type: 'FETCHED',
      })
    );
  },
};

function sanitizeName(firstName: string, lastName: string) {
  let _firstName = firstName.toLowerCase();
  _firstName = removeGreetings(_firstName);

  let _lastName = lastName.toLowerCase();
  _lastName = removeGreetings(_lastName);

  return limitStringLength(`${_firstName} ${_lastName}`, 12).trim().toUpperCase();
}

function limitStringLength(str: string, maxLength: number): string {
  if (str.length <= maxLength) {
    return str;
  }
  return str.slice(0, maxLength);
}

function removeGreetings(str: string): string {
  const greetings = [
    'pa',
    'pak',
    'kak',
    'bapak',
    'bu',
    'ibu',
    'mas',
    'mbak',
    'abang',
    'adik',
    'saudara',
    'saudari',
    'tuan',
    'nyonya',
  ];
  const regexGreetings = new RegExp(`\\b(${greetings.join('|')})\\b`, 'gi');

  // Menghapus kata sapaan
  let result = str.replace(regexGreetings, '').trim();

  // Menghapus semua karakter non-alfabet
  result = result.replace(/[^a-zA-Z\s]/g, '').trim();

  return result;
}

export default notifyActiveLeadsAmartamobil;
