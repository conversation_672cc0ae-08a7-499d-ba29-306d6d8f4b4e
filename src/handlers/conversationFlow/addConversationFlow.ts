import { json, RequestHandler } from 'express';
import {
  IConversationFlow,
  IConversationFlowFirestoreDoc,
} from '../../types/conversationFlow/conversationFlow.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import { v4 } from 'uuid';
import moment from 'moment';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import authChecker from '../../middlewares/authChecker';
import { IFirestoreAdminEntity } from '../../types/firestore/messages/message_types';
import getDealCodeData, { ResponseGetDealCode } from '../../helpers/getDealCodeData';

interface RequestBody extends IConversationFlow {
  projectId: string;
}

const addConversationFlow: {
  handler: RequestHandler<
    any,
    ResponseSchema,
    RequestBody,
    any,
    { account: IFirestoreAdminEntity }
  >;
  middlewares: RequestHandler[];
} = {
  middlewares: [
    authChecker,
    json(),
    body('projectId').notEmpty().withMessage('Field "Project ID" dibutuhkan. '),
    body('startAt').notEmpty().withMessage('Field "Dijalankan Ketika" dibutuhkan. '),
    body('referralSourceId.sourceId')
      .if((input, meta) => {
        return meta.req.body.startAt === 'referralSourceId';
      })
      .trim()
      .notEmpty()
      .withMessage('Ad Source Id diperlukan'),
    body('messages.*.internalTemplate.templateId')
      .if((input, meta) => {
        const messageIndex = parseInt(meta.path.split('.')[1]); // Get array index from path
        return meta.req.body.messages[messageIndex].type === 'internalTemplate';
      })
      .trim()
      .notEmpty()
      .withMessage('Template Id diperlukan untuk pesan tipe internalTemplate'),
    body('messages.*.mediaTrimitraArt.artCode')
      .if((input, meta) => {
        const messageIndex = parseInt(meta.path.split('.')[1]); // Get array index from path
        return meta.req.body.messages[messageIndex].type === 'mediaTrimitraArt';
      })
      .trim()
      .notEmpty()
      .withMessage('Ad Art Code diperlukan'),
    body('referralSourceId.dealCode').optional({ values: 'falsy' }),

    body('messages.*.button.buttons.*.text')
      .if((input, meta) => {
        const matches = meta.path.match(/messages\[(\d+)\]/);
        const messageIndex = matches ? parseInt(matches[1]) : 0;
        return meta.req.body.messages[messageIndex].type === 'button';
      })
      .optional({ values: 'falsy' }) // akan mengabaikan string kosong
      .isLength({ min: 1, max: 20 })
      .withMessage((value, { path }) => {
        const matches = path.match(/messages\[(\d+)\].*buttons\[(\d+)\]/);
        const messageIndex = matches ? matches[1] : '0';
        const buttonIndex = matches ? matches[2] : '0';
        return `Button Text Minimal 1 s.d 20 karakter (Pesan ke-${
          parseInt(messageIndex) + 1
        }, Button ke-${parseInt(buttonIndex) + 1})`;
      }),
    body('messages.*.button.buttons.*.id')
      .if((input, meta) => {
        const matches = meta.path.match(/messages\[(\d+)\]/);
        const messageIndex = matches ? parseInt(matches[1]) : 0;
        return meta.req.body.messages[messageIndex].type === 'button';
      })
      .optional({ values: 'falsy' }) // akan mengabaikan string kosong
      .isLength({ min: 1, max: 20 })
      .withMessage((value, { path }) => {
        const matches = path.match(/messages\[(\d+)\].*buttons\[(\d+)\]/);
        const messageIndex = matches ? matches[1] : '0';
        const buttonIndex = matches ? matches[2] : '0';
        return `Button Id Minimal 1 s.d 20 karakter (Pesan ke-${
          parseInt(messageIndex) + 1
        }, Button ke-${parseInt(buttonIndex) + 1})`;
      }),
    requestValidator,
  ],
  handler: async (req, res) => {
    if (req.body.startAt === 'referralSourceId' && !req.body.referralSourceId.dealCode) {
      const findPriceListMessageType = req.body.messages.find(m => m.type === 'priceList');
      if (findPriceListMessageType) {
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: 'Deal Code dibutuhkan jika ada pesan dengan tipe pricelist',
          })
        );
      }
    }

    const projectCollection = myFirestore.collection('projects');
    const projectDocument = projectCollection.doc(req.body.projectId);
    const convFlowCollection = projectDocument.collection('conversation_flow');

    let dataDealCode: ResponseGetDealCode['data'] | null = null;
    if (req.body.startAt === 'referralSourceId') {
      if (req.body.referralSourceId.dealCode) {
        try {
          const getDealCode = await getDealCodeData(req.body.referralSourceId.dealCode);
          dataDealCode = getDealCode.data.data;
        } catch (e: any) {
          console.log(e);
          return res.status(500).send(
            errorResponse({
              type: 'SERVER_ERROR',
              messages: 'Error Ketika Fetch DealCode',
            })
          );
        }
      }

      const find = await convFlowCollection
        .where('startAt', '==', 'referralSourceId')
        .where('referralSourceId.sourceId', '==', req.body.referralSourceId.sourceId)
        .count()
        .get();

      if (find.data().count >= 1) {
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: 'Conversation flow dengan source id ini sudah ada.',
          })
        );
      }
    } else {
      const find = await convFlowCollection.where('startAt', '==', req.body.startAt).count().get();

      if (find.data().count >= 1) {
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            messages: 'Sudah ada conversation flow yang sama.',
          })
        );
      }
    }

    const docName = v4();
    const docRef = convFlowCollection.doc(docName);

    const now = moment();
    const data: IConversationFlowFirestoreDoc<Date> = {
      id: docName,

      createdAt: now.toDate(),
      createdBy: res.locals.account.email,
      updatedAt: now.toDate(),
      startAt: req.body.startAt,
      messages: req.body.messages,
      active: true,

      referralSourceId: {
        dealCode: req.body.referralSourceId.dealCode || '',
        sourceId: req.body.referralSourceId.sourceId || '',
      },

      dealCode: dataDealCode
        ? {
            modelName: dataDealCode.vehicle.model_name.toUpperCase(),
            cityGroup: dataDealCode.area[0].toUpperCase(),
            endPeriod: moment(dataDealCode.end_period).toDate(),
          }
        : null,
    };

    await docRef.set(data);

    res.send(
      successResponse({
        data: {
          id: docName,
        },
      })
    );
  },
};

export default addConversationFlow;
