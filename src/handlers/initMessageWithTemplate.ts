import { json, RequestHandler } from 'express';
import { ResponseSchema } from '../schema/types/ResponseSchema';
import sendTemplate from '../services/sendTemplate/sendTemplate';
import successResponse from '../schema/successResponse';
import errorResponse from '../schema/errorResponse';
import { body } from 'express-validator';
import requestValidator from '../middlewares/requestValidator';
import phoneNumberCountryCodeSanitizer from '../helpers/phoneNumberCountryCodeSanitizer';
import { Axios, AxiosError } from 'axios';
import { myFirestore } from '../services/firebaseAdmin';
import ClientModel from '../model/ClientModel';
import metaServices from '../services/MetaServices';

interface ReqBody {
  templateName: string;
  phoneNumber: string;
  variables: string[];
  projectPath: string;
}

const initMessageWithTemplate: {
  middlewares: RequestHandler[];
  handler: RequestHand<PERSON><any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    json(),
    body('phoneNumber')
      .notEmpty()
      .trim()
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('templateName').notEmpty(),
    body('projectPath').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    let name = req.body.phoneNumber;

    const projectRef = myFirestore.doc(req.body.projectPath);
    const getProjectData = await projectRef.get();

    const clientCollection = myFirestore.collection('clients');
    const getClient = await clientCollection
      .where('contacts.whatsapp', '==', req.body.phoneNumber)
      .withConverter(ClientModel.converter)
      .get();

    if (!getClient.empty) {
      getClient.forEach(r => {
        const data = r.data();
        name = data.profile.name;
      });
    }

    try {
      if (getProjectData.get('provider') === 'qiscus') {
        await sendTemplate.sendTemplateQiscus({
          template_name: req.body.templateName,
          target: req.body.phoneNumber,
          contactName: name,
          components: [
            {
              type: 'body',
              parameters: req.body.variables.map(v => {
                return {
                  type: 'text',
                  text: v,
                };
              }),
            },
          ],
        });
      } else if (getProjectData.get('provider') === 'meta') {
        await sendTemplate.sendTemplateMetaV2({
          template_name: req.body.templateName,
          target: req.body.phoneNumber,
          contactName: name,
          components: [
            {
              type: 'body',
              parameters: req.body.variables.map(v => {
                return {
                  type: 'text',
                  text: v,
                };
              }),
            },
          ],
          projectId: projectRef.id,
        });
      }
    } catch (e: any) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: (e as AxiosError<any>).response?.data?.data?.error ?? 'SERVER_ERROR',
        })
      );
    }

    return res.send(
      successResponse({
        type: 'CREATED',
        data: null,
      })
    );
  },
};

export default initMessageWithTemplate;
