import { json, Request, Response } from 'express';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import ColdLeadsModel from '../../model/ColdLeadsModel';
import successResponse from '../../schema/successResponse';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import { leadsNotesCounter } from '../../helpers/leads/leadsNotes';

interface ReqBody {
  leadsPhoneNumber: string;
  organization: string;
  movedBy: string;
  notes: string;
}

const moveLeadsToCold = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('leadsPhoneNumber').notEmpty(),
    body('organization').notEmpty(),
    body('movedBy').notEmpty(),
    body('notes').notEmpty(),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response) => {
    const leadsCollection = myFirestore.collection('leads');
    const coldLeadsCollection = myFirestore.collection('cold_leads');
    const coldLeadsHistoriesCollection = myFirestore.collection('cold_leads_histories');
    const docName = `${req.body.organization}-${req.body.leadsPhoneNumber}`;

    const getLeads = await leadsCollection.withConverter(LeadsModel.converter).doc(docName).get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    const notesCounter = await leadsNotesCounter(leads);
    const notes: ILeadsNotesDocument<Date> = {
      agentCode: leads.agentCode,
      event: 'moveToCold',
      notes: 'Move to Cold - ' + req.body.notes,
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: null,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: leads.agentName,
      moveToCold: true,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.notes = req.body.notes;
    leads.updateHistories.push({
      ...notes,
    });

    const coldLeads = new ColdLeadsModel({
      ...leads,
      tradeIn: null,
      moveToColdAt: new Date(),
      moveToColdBy: req.body.movedBy,
      coldNotes: req.body.notes,
      moveToColdByUserType: 'admin',
    });

    try {
      const batch = myFirestore.batch();

      batch.set(
        coldLeadsCollection.doc(docName).withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.set(
        coldLeadsHistoriesCollection.doc().withConverter(ColdLeadsModel.converter),
        coldLeads
      );

      batch.set(myFirestore.collection('leads_notes').doc(), notes);
      batch.delete(leads.ref);

      await batch.commit();

      await aggregatesAgent(leads.agentCode, leads.organization).then();

      // if(coldLeads.organization === "amartachery") {
      //     let messageTelegram = `=== LEADS UPDATE NOTES - COLD ===` +
      //         `\n` +
      //         `\nNama Depan: ${leads.firstName}` +
      //         `\nNama Akhir: ${leads.lastName}` +
      //         `\nNomor Telepon: ${hidePhoneNumber(leads.phoneNumber)}` +
      //         `\nNama Agen: ${leads.agentName}` +
      //         `\nKode Agen: ${leads.agentCode}` +
      //         `\nNotes: ${notes.notes}` +
      //         `\nModel: ${leads.vehicleOptions.map(value => value.model.name).join(", ")}`
      //     ;
      //     telegramServices.sendMessage("-1002118167928", messageTelegram)
      //         .then()
      //         .catch()
      // }

      res.send(
        successResponse({
          data: coldLeads.toJsonResponse(),
          type: 'CREATED',
        })
      );
    } catch (e: any) {
      return res.send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  },
};

export default moveLeadsToCold;
