import { HandlerTypes } from '../../../types/handler.types';
import { query } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';
import { myFirestore } from '../../../services/firebaseAdmin';
import FreeLeadsModel from '../../../model/FreeLeadsModel';
import { IFirestoreMessageEntity } from '../../../types/firestore/messages/message_types.ver2';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { firestore } from 'firebase-admin';

interface ReqQuery {
  phoneNumber: string;
  organization: string;
  page?: string;
  limit?: string;
  order?: string;
  reverse?: string;
}

const getFreeLeadChat: HandlerTypes<any, ResponseSchema, any, ReqQuery> = {
  middlewares: [
    // triF<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    query('phoneNumber').notEmpty(),
    query('organization').notEmpty(),
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('limit').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('order').optional().isIn(['asc', 'desc', 'ASC', 'DESC']),
    query('reverse').optional().isBoolean().toBoolean(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const collections = myFirestore.collection('free_leads');
    const get = await collections
      .doc(`${req.query.organization}-${req.query.phoneNumber}`)
      .withConverter(FreeLeadsModel.converter)
      .get();

    if (!get.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Tidak ada Leads untuk nomor telepon ini',
        })
      );
    }

    if (!get.data()?.ideal?.chat_room_ref) {
      return res.status(500).send(
        errorResponse({
          type: 'NOT_FOUND',
          messages: 'Tidak ada chat room untuk nomor telepon ini',
        })
      );
    }

    const data = get.data()!;
    const chats: any[] = [];

    if (data.ideal?.chat_room_ref) {
      const chatRoomRef = data.ideal.chat_room_ref;
      const chatRef = chatRoomRef.collection('chats');

      const page = req.query.page;
      const limit = req.query.limit;
      const order = (req.query.order || 'desc').toLowerCase();

      let query = chatRef.orderBy('message.unixtime', order as 'asc' | 'desc');

      // Only apply pagination if both page and limit are provided
      if (page && limit) {
        const offset = (parseInt(page) - 1) * parseInt(limit);
        query = query.offset(offset).limit(parseInt(limit));
      }

      const getChats = await query.get();

      getChats.forEach(result => {
        const data = result.data() as IFirestoreMessageEntity;
        chats.push({
          message: {
            ...data.message,
            unixtime: Math.floor(data.message.unixtime as number),
            timestamp:
              data.message.timestamp instanceof firestore.Timestamp
                ? data.message.timestamp.toDate()
                : data.message.timestamp,
          },
          origin: {
            ...data.origin,
            reference:
              data.origin.reference instanceof firestore.DocumentReference
                ? data.origin.reference.path
                : data.origin.reference,
          },
          statuses: {
            delivered: data.statuses?.delivered?.toDate() || null,
            read: data.statuses?.read?.toDate() || null,
            sent: data.statuses?.sent?.toDate() || null,
            failed: data.statuses?.failed?.toDate() || null,
          },
        });
      });
    }

    if (req.query.reverse) {
      chats.reverse();
    }

    res.send(
      successResponse({
        type: 'FETCHED',
        data: chats,
        meta: {
          page: req.query.page ? parseInt(req.query.page) : null,
          limit: req.query.limit ? parseInt(req.query.limit) : null,
          order: (req.query.order || 'desc').toLowerCase(),
          reverse: req.query.reverse,
        },
      })
    );
  },
};

export default getFreeLeadChat;
