import { Request, Response, urlencoded } from 'express';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import moment from 'moment';

export interface UpdateCreditSchemeHandlerReqBody {
  _clientPath: string;
  tenor: number;
  installment: number;
  downPayment: number;
  downPaymentDiscount: number;
  surveyTime?: Date;
  surveyGmapUrl?: string;
  discountInstallment?: number;
  discountTenor?: number;
  priceListSource: string;
  dealCode?: string;
  selectedLeasingCode?: string;
  leasingAdminId?: string;
}

export default class UpdateCreditSchemeHandler {
  public static middlewares = [
    urlencoded({
      extended: true,
    }),
    body('_clientPath')
      .notEmpty()
      .isString()
      .custom(async input => {
        const client = myFirestore.doc(input);
        if (!(await client.get()).exists) {
          return Promise.reject('Invalid Value');
        }
      }),
    body('priceListSource').notEmpty().withMessage('Sumber Price List harus diisi.'),
    body('dealCode')
      .if((input, meta) => meta.req.body.priceListSource === 'dealCode')
      .notEmpty(),
    body('selectedLeasingCode')
      .if(
        (input, meta) =>
          meta.req.body.priceListSource === 'dealCode' && !meta.req.body.leasingAdminId
      )
      .notEmpty(),

    body('leasingAdminId')
      .if(
        (input, meta) =>
          meta.req.body.priceListSource === 'dealCode' && !meta.req.body.selectedLeasingCode
      )
      .notEmpty(),

    body('tenor').notEmpty().withMessage('Tenor diperlukan').isInt().toInt(),
    body('discountTenor').optional({ values: 'falsy' }).isInt().toInt(),
    body('discountInstallment').optional({ values: 'falsy' }).isInt().toInt(),
    body('installment').notEmpty().withMessage('Angsuran diperlukan').isFloat().toFloat(),
    body('downPayment').notEmpty().withMessage('Uang muka diperlukan').isFloat().toFloat(),
    body('surveyTime')
      .custom(input => moment(input, 'YYYY-MM-DD HH:mm').isValid())
      .customSanitizer(input => moment(input, 'YYYY-MM-DD HH:mm').toDate())
      .withMessage('Tanggal survey dibutuhkan'),
    body('surveyGmapUrl')
      .optional({ values: 'falsy' })
      .trim()
      .isURL()
      .withMessage('URL Google Map untuk lokasi survey dibutuhkan'),
    requestValidator,
  ];

  public static async handler(
    req: Request<
      any,
      {
        success: boolean;
        message?: string;
      },
      UpdateCreditSchemeHandlerReqBody
    >,
    res: Response<{ success: boolean; message?: string }>
  ) {
    const client = myFirestore.doc(req.body._clientPath);

    try {
      await client.update({
        'survey.credit_scheme': {
          tenor: req.body.tenor,
          discountTenor: req.body.discountTenor || 0,
          discountInstallment: req.body.discountInstallment || 0,
          down_payment: req.body.downPayment,
          installment: req.body.installment,
          surveyTime: req.body.surveyTime || null,
          surveyGmapUrl: req.body.surveyGmapUrl,
          priceListSource: req.body.priceListSource,
          dealCode: req.body.dealCode || '',
          lastUpdate: new Date(),
          selectedLeasingCode: req.body.selectedLeasingCode,
          leasingAdminId: req.body.leasingAdminId,
        },
      });
      res.send({
        success: true,
      });
    } catch (e: any) {
      res.status(500).send({
        success: false,
        message: e,
      });
    }
  }
}
