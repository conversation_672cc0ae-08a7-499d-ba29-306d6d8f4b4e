import { BigQuery } from '@google-cloud/bigquery';

export const bigQuery = new BigQuery({
  keyFilename: './keys/ideal-trimitra-26d125bc4a2c.json',
  projectId: 'ideal-trimitra',
});

const rawLeadsTable = bigQuery.dataset('ext_leads').table('raw_ext_leads');

const feedbackLeadsTable = bigQuery.dataset('ext_leads').table('feedback_leads');

export const newLeadsBigQueryTable = bigQuery.dataset('chat_analytics').table('lead_activity');

const leadsToSpkTable = bigQuery.dataset('ext_leads').table('spk_leads');

const priceListTable = bigQuery.dataset('pricelist_log').table('pricelist_activitylog');

const activeLeadsTable = bigQuery.dataset('leads').table('active_leads');

const clientDataCompletions = bigQuery.dataset('chat_analytics').table('client_data_completions');

export { rawLeadsTable, feedbackLeadsTable, leadsToSpkTable, priceListTable, activeLeadsTable, clientDataCompletions };
