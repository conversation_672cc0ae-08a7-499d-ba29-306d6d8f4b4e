import axios from 'axios';
import { ISendTemplateParams } from './sendTemplateParams';
import { SuccessResponseMeta, SuccessResponseQiscus } from './sendTemplateResponse.types';

class SendTemplate {
  private qiscusAxiosBase = axios.create({
    baseURL: 'https://asia-southeast2-ideal-trimitra.cloudfunctions.net/post-message/',
    // baseURL: "http://localhost:8003",
  });

  public async sendTemplateMetaV2(params: ISendTemplateParams) {
    const body = {
      ...params,
      messaging_product: 'whatsapp',
    };
    try {
      const send = await this.qiscusAxiosBase.post<SuccessResponseMeta>('/send/template/meta/v2', {
        ...body,
      });

      return send.data;
    } catch (e: any) {
      throw e;
    }
  }

  public async sendTemplateQiscus(params: ISendTemplateParams) {
    try {
      const send = await this.qiscusAxiosBase.post<SuccessResponseQiscus>('/send/template', {
        ...params,
      });

      return send.data;
    } catch (e: any) {
      console.log(e);
      throw e;
    }
  }
}

const sendTemplate = new SendTemplate();

export default sendTemplate;
