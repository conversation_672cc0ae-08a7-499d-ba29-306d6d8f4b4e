import { qiscusServiceNew } from '../../../src/services/QiscusServiceNew';
import fs from 'fs';
import path from 'path';
import mime from 'mime-types';
import { fail } from 'assert';

let mediaId = '496208149779661';

describe('Qiscus Media', () => {
  it('should upload image', async () => {
    try {
      const filePath = 'test/whatsappProviderTest/pexels-pixabay-147411.jpg';
      const imageBuffer = await fs.promises.readFile(filePath);
      const file = new File([imageBuffer], 'image.jpg', {
        type: 'image/jpeg',
      });
      const mimeType = mime.lookup(filePath) || 'application/octet-stream';

      const response = await qiscusServiceNew.uploadMedia({
        file: file,
        type: mimeType,
      });
      mediaId = response.data.media[0].id;
      console.log(mediaId);

      expect(response.data.media).toBeDefined();
      expect(response.data.media[0].id).toBeDefined();
      expect(typeof response.data.media[0].id).toBe('string');
    } catch (error) {
      console.log('ERROR:');
      console.log(error);
      fail('Test should not reach catch block');
    }
  });

  it('should get media by id', async () => {
    try {
      const media = await qiscusServiceNew.getMedia(mediaId);

      // Buat folder download jika belum ada
      const downloadDir = path.join(__dirname, 'download');
      if (!fs.existsSync(downloadDir)) {
        fs.mkdirSync(downloadDir);
      }

      // Simpan file
      const filePath = path.join(downloadDir, `downloaded_media_${Date.now()}.jpg`);
      fs.writeFileSync(filePath, media.data);

      console.log('File saved to:', filePath);
      expect(media.data).toBeDefined();
      expect(media.data).toBeInstanceOf(Buffer);
      expect(Buffer.isBuffer(media.data)).toBe(true);
      expect(fs.existsSync(filePath)).toBe(true);
    } catch (error) {
      console.log('ERROR:');
      console.log(error);
      fail('Failed to get media by ID - Test should not reach catch block');
    }
  });
});
