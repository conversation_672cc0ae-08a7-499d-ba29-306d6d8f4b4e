import { HandlerTypes } from '../../types/handler.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { json } from 'express';
import { body, query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import FreeLeadsModel from '../../model/FreeLeadsModel';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import { decrypt } from '../../helpers/encryption';

interface ReqBody {
  phoneNumber: string;
  organization: string;
  encrypted: boolean;
}

interface ReqQuery {
  encrypted: boolean;
}

const CheckFreeLeadsAcquisition: HandlerTypes<any, ResponseSchema, ReqBody, ReqQuery> = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('phoneNumber').notEmpty(),
    body('organization').notEmpty(),
    query('encrypted').optional().isBoolean().toBoolean(),
    requestValidator,
  ],
  handler: async (req, res) => {
    let phoneNumber = req.body.phoneNumber;
    if (req.query.encrypted) {
      phoneNumber = decrypt(phoneNumber);
    }

    const freeLeadsCollection = myFirestore.collection('free_leads');
    const docName = `${req.body.organization}-${phoneNumber}`;
    const getFreeLeads = await freeLeadsCollection
      .doc(docName)
      .withConverter(FreeLeadsModel.converter)
      .get();

    if (!getFreeLeads.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Free Leads tidak ditemukan',
        })
      );
    }

    const dataFreeLeads = getFreeLeads.data()!;

    if (dataFreeLeads.isAcquired) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Free Leads sudah diakuisisi',
        })
      );
    }

    const getLeads = await myFirestore.collection('leads').doc(docName).get();

    if (getLeads.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Free Leads sudah diakuisisi',
        })
      );
    }

    res.send(
      successResponse({
        type: 'FETCHED',
        data: {
          firstName: dataFreeLeads.firstName,
          lastName: dataFreeLeads.lastName,
          phoneNumber: req.body.phoneNumber,
          organization: dataFreeLeads.organization,
        },
      })
    );
  },
};

export default CheckFreeLeadsAcquisition;
