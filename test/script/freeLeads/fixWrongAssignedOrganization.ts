import { myFirestore } from '../../../src/services/firebaseAdmin';
import FreeLeadsModel from '../../../src/model/FreeLeadsModel';

const getFreeLeads = async () => {
  const freeLeadsCollection = myFirestore.collection('free_leads');
  const get = await freeLeadsCollection
    .where('organization', '==', 'amartavinfast')
    .where('source', '==', 'ideal')
    .withConverter(FreeLeadsModel.converter)
    .get();

  const freeLeads: FreeLeadsModel[] = [];

  get.forEach(result => {
    const data = result.data();
    if(data.ref.path.includes('amartahonda')) {
      freeLeads.push(data);
    }
  });

  console.log("Total Free Leads", freeLeads.length);

  return freeLeads;
};


const fixWrongAssignedOrganization = async () => {
  const freeLeads = await getFreeLeads();
};

fixWrongAssignedOrganization();
