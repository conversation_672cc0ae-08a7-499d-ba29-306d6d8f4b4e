import { query } from 'express-validator';
import requestChecker from '../middlewares/requestValidator';
import { Request, Response } from 'express';
import axios from 'axios';

export default class GetImageFromS3 {
  public static middlewares = [query('path').notEmpty(), requestChecker];

  public static async handler(req: Request<{}, any, any, { path: string }>, res: Response) {
    try {
      const getImage = (await axios(req.query.path, { responseType: 'arraybuffer' })).data;

      res.contentType('image/jpeg');
      res.send(getImage);
    } catch (e: any) {
      res.status(404).send({
        success: false,
      });
    }
  }
}
