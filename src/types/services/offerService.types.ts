export interface IGetOffer {
  success: boolean;
  data: {
    transaction_code: string;
    transaction_time: string;
    purchase_method: string;
    area: string;
    contacts: {
      phone_number_owner: string;
      phone_number_alternative_owner: string | null;
      phone_number_order_maker: string | null;
      phone_number_alternative_order_maker: string | null;
      phone_number_guarantor: string | null;
    };
    id_card_order_maker: {
      id_card_image: string;
      marital_status: string;
      full_name: string;
      occupation: string;
      birth_date: string;
      occupation_code: string;
      sex: string;
      birth_place: string;
      id_card_number: string;
      full_address: string;
      religion: string;
    };
    id_card_guarantor: {
      id_card_image: string;
      marital_status: string;
      full_name: string;
      occupation: string;
      birth_date: string;
      occupation_code: string;
      sex: string;
      birth_place: string;
      id_card_number: string;
      full_address: string;
      religion: string;
    };
    id_card_owner: {
      id_card_image: string;
      marital_status: string;
      full_name: string;
      occupation: string;
      birth_date: string;
      occupation_code: string;
      sex: string;
      birth_place: string;
      id_card_number: string;
      full_address: string;
      religion: string;
    };
    contact: {
      phone_number_wa_available_guarantor: boolean;
      phone_number_wa_available_order_maker: boolean;
      phone_number_owner: string;
      phone_number_wa_available_owner: boolean;
      phone_number_alternative_owner: string;
      email_owner: string;
      phone_number_alternative_order_maker: string;
      phone_number_guarantor: string;
      phone_number_order_maker: string;
      email_order_maker: string;
    };
    detail_promo: {
      discount: number;
      tenor: number;
      code: string;
      type: string;
      percentage: number;
      target: string;
    }[];
    credit: {
      po_code: string;
      dp_amount: number;
      installment_amount: number;
      finco_name: string;
      finco_code: string;
      po_data: {};
      po_track_number: string;
      po_expire_poilicy: number;
      credit_note: string;
      tenor: number;
      po_phone_number: string;
      po_expired_at: string;
      po_image: string;
      finco_branch: string;
      po_time: string;
    };
    vehicle: {
      model_name: string;
      alternative_color: {
        name: string;
        code: string;
      };
      year: string;
      variant_uuid: string;
      model_uuid: string;
      brand_name: string;
      variant_name: string;
      color_name: string;
      variant_code: string;
      color_code: string;
      brand_uuid: string;
    };
  };
}
