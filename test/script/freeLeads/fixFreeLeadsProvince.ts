import { myFirestore } from '../../../src/services/firebaseAdmin';
import FreeLeadsModel from '../../../src/model/FreeLeadsModel';
import moment from 'moment';
import { autotrimitraServices } from '../../../src/services/autotrimitraServices';

const fixFreeLeadsProvince = async () => {
  const freeLeadsCollection = myFirestore.collection('free_leads');
  const getFreeLeads = await freeLeadsCollection
    .withConverter(FreeLeadsModel.converter)
    .where('source', '==', 'ideal')
    .get();

  const getProvinces = async () => {
    const get = await autotrimitraServices.getProvince();
    return get.data.data;
  };

  const provinces = await getProvinces();

  const freeLeads: FreeLeadsModel[] = [];

  const startAt = moment().hours(6).minutes(0);

  getFreeLeads.forEach(result => {
    const data = result.data();
    if (moment(data.createdAt).isAfter(startAt)) {
      freeLeads.push(result.data());
    }
  });

  const batch = myFirestore.batch();
  for (const leads of freeLeads) {
    const provinceCode = leads.domicile?.cityCode.split('.')[0];
    const province = provinces.find(p => p.code === provinceCode);
    if (!province) continue;
    console.log(province);
    batch.update(leads.ref, {
      'domicile.provinceName': province.name,
      'domicile.provinceCode': province.code,
    });
  }
  await batch.commit();
};

fixFreeLeadsProvince();
