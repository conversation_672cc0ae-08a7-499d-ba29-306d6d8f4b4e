import { myFirestore } from '../../../src/services/firebaseAdmin';
import FreeLeadsModel from '../../../src/model/FreeLeadsModel';
import aggregatesAgent from '../../../src/helpers/leads/aggregatesAgent';

async function deletePhoneNumber() {
  const phoneNumber = '6285763705624';

  const freeLeadsCollection = myFirestore.collection('free_leads');
  const leadsCollection = myFirestore.collection('leads');
  const rawLeadsCollection = myFirestore.collection('raw_leads');

  const freeLeadsList: FreeLeadsModel[] = [];

  const findFreeLeadsCollection = await freeLeadsCollection
    .where('phoneNumber', '==', phoneNumber)
    .withConverter(FreeLeadsModel.converter)
    .get();

  findFreeLeadsCollection.forEach(result => {
    const data = result.data();
    if (data) freeLeadsList.push(data);
  });

  const findRawLeads = await rawLeadsCollection.where('phoneNumber', '==', phoneNumber).get();

  const batch = myFirestore.batch();

  findRawLeads.forEach(r => {
    batch.delete(r.ref);
  });

  for (const freeLeadsModel of freeLeadsList) {
    const docName = freeLeadsModel.ref.id;
    batch.delete(freeLeadsCollection.doc(docName));
    if (freeLeadsModel.isAcquired) {
      batch.delete(leadsCollection.doc(docName));
    }
  }

  await batch.commit();

  for (const freeLeadsModel of freeLeadsList) {
    if (freeLeadsModel.isAcquired && freeLeadsModel.acquiredAgentCode) {
      await aggregatesAgent(freeLeadsModel.acquiredAgentCode, freeLeadsModel.organization);
    }
  }
}

deletePhoneNumber();
