import { query } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { Request, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import { firestore } from 'firebase-admin';
import {
  IPostMessageContext,
  TAvailablePostMessageContext,
} from '../../../types/services/qiscus/qiscuss_service_new_types';
import successResponse from '../../../schema/successResponse';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import errorResponse from '../../../schema/errorResponse';

interface IGetMessageHandlerReqQuery {
  phoneNumber: string;
  organization?: string;
}

interface IChatList extends IPostMessageContext {
  name: string;
  type: TAvailablePostMessageContext;
  direction: 'IN' | 'OUT';
  time: Date;
}

export default class GetMessageHandler {
  public static middlewares = [
    query('phoneNumber')
      .notEmpty()
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    query('organization').optional(),
    requestValidator,
  ];

  public static async handler(
    req: Request<null, ResponseSchema, null, IGetMessageHandlerReqQuery>,
    res: Response<ResponseSchema>
  ) {
    let organization = req.query.organization || 'amartahonda';
    let projectRef = myFirestore.doc('/projects/WLdKug7hau0MbRzKcnqg');

    if (
      organization !== 'amartahonda' &&
      (organization === 'amartachery' ||
        organization === 'amartaneta' ||
        organization === 'amartavinfast')
    ) {
      projectRef = myFirestore.doc('/projects/kpsDdEcRtReOQrCYkzq2');
    }

    const findChatRoom = await projectRef
      .collection('chat_rooms')
      .where('contacts', 'array-contains', req.query.phoneNumber)
      .get();

    if (findChatRoom.empty) {
      res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
        })
      );

      return;
    }

    let chatRoomRef!: firestore.DocumentReference<firestore.DocumentData>;
    findChatRoom.forEach(result => {
      chatRoomRef = result.ref;
    });

    const getLast10Chats = await chatRoomRef
      .collection('chats')
      .orderBy('message.timestamp', 'desc')
      // .limit(20)
      .get();

    const last10Chats: IChatList[] = [];

    getLast10Chats.forEach(result => {
      last10Chats.push({
        direction: result.get('message.direction'),
        type: result.get('message.type'),
        time: (result.get('message.timestamp') as firestore.Timestamp).toDate(),
        name: result.get('origin.display_name'),
        text: result.get('message.text'),
        location: result.get('message.location') || undefined,
        interactive: result.get('message.interactive'),
        button: result.get('message.button'),
        image:
          result.get('message.image') && result.get('message.direction') === 'IN'
            ? {
                ...result.get('message.image'),
                link: `https://asia-southeast2-ideal-trimitra.cloudfunctions.net/ideal-backend/message/image?path=${result.ref.path}`,
              }
            : result.get('message.image'),
      });
    });

    res.send(
      successResponse({
        data: last10Chats.reverse(),
        type: 'FETCHED',
      })
    );
  }
}
