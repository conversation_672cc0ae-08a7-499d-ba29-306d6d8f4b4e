import { Request, Response } from 'express';
import { query } from 'express-validator';
import moment, { Moment } from 'moment';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import requestValidator from '../../middlewares/requestValidator';
import ColdLeadsModel from '../../model/ColdLeadsModel';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';

interface ReqQuery {
  start_date: string;
  end_date: string;
  agent_codes?: string | string[];
  spk_number: string;
}

const getColdLeads = {
  middlewares: [
    leadsAuthorizer,
    query('start_date')
      .notEmpty()
      .withMessage('start_date is required')
      .custom(input => {
        const validDate = moment(input, 'YYYY-MM-DD HH:mm', true).isValid();
        if (!validDate) {
          throw new Error('Invalid start_date format. Expected format: YYYY-MM-DD HH:mm');
        }
        return true;
      }),
    query('end_date')
      .notEmpty()
      .withMessage('end_date is required')
      .custom(input => {
        const validDate = moment(input, 'YYYY-MM-DD HH:mm', true).isValid();
        if (!validDate) {
          throw new Error('Invalid end_date format. Expected format: YYYY-MM-DD HH:mm');
        }
        return true;
      }),
    query('agent_codes')
      .optional()
      .custom(input => {
        // Handle both single string and array of strings
        if (typeof input === 'string') {
          return true;
        }
        if (Array.isArray(input)) {
          if (input.length === 0) {
            throw new Error('agent_codes array cannot be empty');
          }
          for (const code of input) {
            if (typeof code !== 'string' || code.trim() === '') {
              throw new Error('All agent_codes must be non-empty strings');
            }
          }
          return true;
        }
        throw new Error('agent_codes must be a string or array of strings');
      }),
    requestValidator,
  ],
  handler: async function (
    req: Request<{}, ResponseSchema, {}, ReqQuery>,
    res: Response<ResponseSchema>
  ) {
    try {
      // Parse and validate dates
      const startDate: Moment = moment(req.query.start_date, 'YYYY-MM-DD HH:mm', true);
      const endDate: Moment = moment(req.query.end_date, 'YYYY-MM-DD HH:mm', true);

      // Validate date range
      if (endDate.isBefore(startDate)) {
        return res.status(422).send(
          errorResponse({
            type: 'UNPROCESSABLE_ENTITY',
            messages: 'end_date must be after start_date',
          })
        );
      }

      // Parse agent_codes
      let agentCodes: string[] | undefined;
      if (req.query.agent_codes) {
        if (typeof req.query.agent_codes === 'string') {
          agentCodes = [req.query.agent_codes];
        } else if (Array.isArray(req.query.agent_codes)) {
          agentCodes = req.query.agent_codes;
        }
      }

      // Build Firestore query
      const collection = myFirestore.collection('cold_leads');
      let queryBuilder = collection
        .where('moveToColdAt', '>=', startDate.toDate())
        .where('moveToColdAt', '<=', endDate.toDate())
        .orderBy('moveToColdAt', 'desc');

      // Apply spk_number filter if provided
      if (req.query.spk_number) {
        queryBuilder = queryBuilder.where('spk.spkNumber', '==', req.query.spk_number);
      }

      // Apply agent_codes filter if provided
      if (agentCodes && agentCodes.length > 0) {
        // Firestore 'in' operator supports up to 10 values
        if (agentCodes.length > 10) {
          return res.status(422).send(
            errorResponse({
              type: 'UNPROCESSABLE_ENTITY',
              messages: 'agent_codes cannot contain more than 10 values',
            })
          );
        }
        queryBuilder = queryBuilder.where('agentCode', 'in', agentCodes);
      }

      // Execute query
      const querySnapshot = await queryBuilder.withConverter(ColdLeadsModel.converter).get();

      const coldLeads: ColdLeadsModel[] = [];
      querySnapshot.forEach(doc => {
        const data = doc.data();
        coldLeads.push(data);
      });

      // Return response
      res.send(
        successResponse({
          type: 'FETCHED',
          data: coldLeads.map(lead => lead.toJsonResponse()),
          meta: {
            total: coldLeads.length,
            start_date: req.query.start_date,
            end_date: req.query.end_date,
            agent_codes: agentCodes || null,
          },
        })
      );
    } catch (error: any) {
      console.error('Error in GetColdLeads handler:', error);
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Failed to retrieve cold leads',
          data: error.message,
        })
      );
    }
  },
};

export default getColdLeads;
