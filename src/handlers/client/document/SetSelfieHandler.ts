import { Request, Response } from 'express';
import multer from 'multer';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import { File } from '@google-cloud/storage';
import { ISelfieFields } from '../../../types/services/profile_service_types';
import { profileService } from '../../../services/profileService';

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export interface SetSelfieFields {
  fullAddress: string | null;
  latitude: string | null;
  longitude: string | null;
  description: string | null;
}

export interface SetSelfieReqBody extends SetSelfieFields {
  imageUrl?: string;
  clientRef: string;
}

export default class SetSelfieHandler {
  public static middlewares = [
    formDataParse.single('image'),
    body('fullAddress').optional(),
    body('latitude').optional(),
    body('longitude').optional(),
    body('description').optional(),

    body('imageUrl').optional(),

    body('clientRef').notEmpty(),
    requestValidator,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, SetSelfieReqBody>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.get();

    const payload: ISelfieFields = {
      Description: req.body.description ?? null,
      FullAddress: req.body.fullAddress ?? null,
      Latitude: req.body.latitude ?? null,
      Longitude: req.body.longitude ?? null,
    };

    let file: File | null = null;
    if (req.file?.buffer) {
      file = bucketTrimitraIdeal.file(`client-documents/${clientDocRef.id}/selfie.jpg`);
    }

    try {
      if (file) {
        if (req.file)
          await file.save(req.file.buffer, {
            public: true,
          });

        payload.SelfieImage = file.publicUrl();
      } else {
        payload.SelfieImage = req.body.imageUrl;
      }

      const update = await profileService.updateSelfie(
        payload,
        getClient.data()!.details.owner_phone_number
      );

      res.send(
        successResponse({
          type: 'CREATED',
          data: update?.data,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
