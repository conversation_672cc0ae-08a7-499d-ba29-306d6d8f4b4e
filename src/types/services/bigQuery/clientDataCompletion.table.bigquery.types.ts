import { BigQueryTimestamp } from '@google-cloud/bigquery';

export interface IClientDataCompletionTable {
  uuid: string;
  projectRef: string;
  projectName: string;
  projectGroup: string;
  name: string;
  phoneNumber: string;
  organization: string | null;
  organizationGroup: string | null;
  organizationUpdatedBy: string | null;
  cityGroup: string | null;
  cityGroupUpdatedAt: BigQueryTimestamp | null;
  cityGroupUpdatedBy: string | null;
  organizationUpdatedAt: BigQueryTimestamp | null;
  chatRoomCreatedAt: BigQueryTimestamp | null;
  createdAt: BigQueryTimestamp | null;
}
