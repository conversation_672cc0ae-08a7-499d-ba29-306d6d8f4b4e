import { myFirestore } from '../../services/firebaseAdmin';
import { Request } from 'express';

export function errorLogsAddLeads(req: Request, e: any) {
  const collections = myFirestore.collection('leads_logs').doc('add_leads').collection('logs');

  const dataToInsert = {
    headers: req.headers,
    body: req.body,
    meta: {
      createdAt: new Date(),
      messages: [e?.toString(), JSON.stringify(e)],
    },
  };

  collections.doc().set(dataToInsert).then().catch();
}
