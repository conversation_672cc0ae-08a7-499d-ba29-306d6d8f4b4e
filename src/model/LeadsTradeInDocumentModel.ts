import { firestore } from 'firebase-admin';
import { LeadsTradeInDocumentTypes } from '../types/firestore/leads_trade_in_document_types';

class LeadsTradeInDocumentModel implements LeadsTradeInDocumentTypes {
  public static converter: firestore.FirestoreDataConverter<LeadsTradeInDocumentModel> = {
    fromFirestore(snapshot: FirebaseFirestore.QueryDocumentSnapshot): LeadsTradeInDocumentModel {
      const data = snapshot.data() as LeadsTradeInDocumentTypes;
      return new LeadsTradeInDocumentModel({
        brand: data.brand,
        model: data.model,
        year: data.year,
        images: data.images,
        price: data.price,
        createdAt: data.createdAt,
        ref: snapshot.ref,
      });
    },
    toFirestore(modelObject: LeadsTradeInDocumentModel): FirebaseFirestore.DocumentData {
      return {
        brand: modelObject.brand,
        model: modelObject.model,
        year: modelObject.year,
        images: modelObject.images,
        createdAt: modelObject.createdAt,
        price: modelObject.price,
      };
    },
  };
  brand: {
    name: string;
    uuid: string;
  };
  model: {
    name: string;
    uuid: string;
  };
  year: string;
  images: string[];
  price: number;
  createdAt: LeadsTradeInDocumentTypes['createdAt'];
  ref!: firestore.DocumentReference;

  constructor(params: {
    brand: LeadsTradeInDocumentTypes['brand'];
    model: LeadsTradeInDocumentTypes['model'];
    year: LeadsTradeInDocumentTypes['year'];
    images: LeadsTradeInDocumentTypes['images'];
    price: LeadsTradeInDocumentTypes['price'];
    createdAt: LeadsTradeInDocumentTypes['createdAt'];
    ref?: firestore.DocumentReference;
  }) {
    this.brand = params.brand;
    this.model = params.model;
    this.year = params.year;
    this.images = params.images || [];
    this.price = params.price;
    this.createdAt = params.createdAt;

    if (params.ref) {
      this.ref = params.ref;
    }
  }

  toJsonResponse(): LeadsTradeInDocumentTypes<Date> {
    return {
      ...this,
      ref: this.ref?.path || '',
      createdAt: this.createdAt.toDate(),
    };
  }
}

export default LeadsTradeInDocumentModel;
