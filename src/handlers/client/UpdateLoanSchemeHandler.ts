import { HandlerTypes } from '../../types/handler.types';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { json } from 'express';
import authChecker from '../../middlewares/authChecker';
import { body } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { myFirestore } from '../../services/firebaseAdmin';
import errorResponse from '../../schema/errorResponse';
import successResponse from '../../schema/successResponse';
import moment from 'moment';

interface ReqBody {
  clientRefPath: string;
  tenor: number;
  amount: number;
}

const updateLoanSchemeHandler: HandlerTypes<any, ResponseSchema, ReqBody> = {
  middlewares: [
    authChe<PERSON>,
    json(),
    body('tenor').notEmpty().isNumeric(),
    body('amount').notEmpty().isNumeric(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const client = myFirestore.doc(req.body.clientRefPath);

    try {
      await client.update({
        'survey_loan.lastUpdatedAt': moment().toDate(),
        'survey_loan.scheme': {
          tenor: req.body.tenor,
          amount: req.body.amount,
        },
      });
    } catch (e) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Gagal memperbarui skema Loan',
        })
      );
    }

    return res.send(
      successResponse({
        type: 'UPDATED',
        data: null,
      })
    );
  },
};

export default updateLoanSchemeHandler;
