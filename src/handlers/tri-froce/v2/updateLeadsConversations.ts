import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import multer from 'multer';
import { body } from 'express-validator';
import fetchAgent from '../../../services/agent/fetchAgent';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import parseExportedWhatsappChat, {
  ParsedChat,
  ParseExportedWhatsappChatOptions,
} from '../../../helpers/parseExportedWhatsappChat';
import { v4 } from 'uuid';
import { bucketKataAi } from '../../../services/cloudStorage';
import path from 'path';
import moment from 'moment';
import { NewLeadsBigqueryTypes } from '../../../types/services/bigQuery/newLeads.bigquery.types';
import { bigQuery, newLeadsBigQueryTable } from '../../../services/bigQueryService';
import successResponse from '../../../schema/successResponse';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import { leadsNotesCounter } from '../../../helpers/leads/leadsNotes';
import requestValidator from '../../../middlewares/requestValidator';

const fileUpload = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

interface ReqBody {
  agentCode: string;
  phoneNumber: string;
  organization: string;
  type: 'newLeads' | 'update';
}

const updateLeadsConversations: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, ReqBody>;
} = {
  middlewares: [
    fileUpload.single('conversations'),
    body('agentCode').notEmpty().trim().withMessage('Kode Agen dibutuhkan'),
    body('phoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('type').notEmpty().isIn(['newLeads', 'update']).withMessage('type is required'),
    body('organization').notEmpty().withMessage('Organization is required'),
    requestValidator,
  ],
  handler: async (req, res) => {
    const leadsDocName = `${req.body.organization}-${req.body.phoneNumber}`;
    const leadsDocRef = myFirestore.collection('leads').doc(leadsDocName);
    const getLeadsDoc = await leadsDocRef.withConverter(LeadsModel.converter).get();

    if (!getLeadsDoc.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    if (!req.file?.buffer) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
          messages: 'File percakapan diperlukan!',
        })
      );
    }

    const now = moment();
    const leadsDocData = getLeadsDoc.data()!;

    const readUploadedFile = req.file.buffer.toString('utf-8');
    let parseConversations!: ParsedChat;
    let options: ParseExportedWhatsappChatOptions | undefined;
    const totalConversation = leadsDocData.agentWhatsappConversations.length;
    const lastConversationFile = leadsDocData.agentWhatsappConversations[totalConversation - 1];
    if (req.body.type === 'update' && totalConversation > 0 && lastConversationFile?.endChatAt) {
      options = {
        minDate: lastConversationFile.endChatAt,
      };
    }

    parseConversations = parseExportedWhatsappChat(readUploadedFile, options);

    console.log(parseConversations);

    if (parseConversations.summary.inboundPhoneNumber) {
      if (parseConversations.summary.inboundPhoneNumber !== leadsDocData.phoneNumber) {
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            data: null,
            messages:
              'Pesan yang masuk terdeteksi bukan berasal dari nomor telepon leads yang sama.',
          })
        );
      }
    }

    if (parseConversations.summary.inbound === 0) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          data: null,
          messages: 'Tidak ditemukan percakapan masuk pada update file percakapan',
        })
      );
    }

    if (req.body.type === 'newLeads') {
      if (parseConversations.summary.inbound < 8) {
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            data: null,
            messages: 'Minimal harus ada 8 pesan masuk.',
          })
        );
      }
    }

    if (req.body.type === 'update') {
      if (parseConversations.messages.length === 0) {
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            data: null,
            messages:
              'Tidak ditemukan percakapan yang baru sejak terakhir file percakapan diunggah',
          })
        );
      }
    }

    if (totalConversation === 0) {
      leadsDocData.firstMessageDirection = parseConversations.summary.firstMessageDirection;
    }

    let agentName: string = 'NO_NAME';
    try {
      const fetch = await fetchAgent(req.body.agentCode);
      agentName = fetch.name;
    } catch {
      agentName = 'NO_NAME';
    }

    const batch = myFirestore.batch();

    if (req.body.type === 'update') {
      const notesCounter = await leadsNotesCounter(leadsDocData);
      const notes: ILeadsNotesDocument<Date> = {
        notes: 'Update file percakapan leads',
        updatedAt: now.toDate(),
        updatedByUser: null,
        statusLevel: leadsDocData.statusLevel,
        agentCode: req.body.agentCode,
        event: 'updateFileConversation',
        phoneNumber: req.body.phoneNumber,
        organization: leadsDocData.organization,
        firstName: leadsDocData.firstName,
        lastName: leadsDocData.lastName,
        agentName: agentName,
        moveToCold: false,
        reactivate: {
          currentTotal: notesCounter.totalReactivate,
        },
        totalUpdateNotes: notesCounter.totalNotes + 1,
        followUpScheduledAt: null,
      };
      batch.set(myFirestore.collection('leads_notes').doc(), notes);

      leadsDocData.notes = notes.notes;
      leadsDocData.updateHistories.push({
        ...notes,
      });
    }

    const fileName = `wa-conversations_${v4()}_${req.body.phoneNumber}_${now.format('YYYY-MM-DD_HH:mm:ss')}`;
    const file = bucketKataAi.file(
      'leadsConversations/' +
        req.body.phoneNumber +
        '/' +
        fileName +
        path.extname(req.file.originalname)
    );

    const leadsAgentConversationDocRef = myFirestore.collection('leads_agent_conversations').doc();

    let agentConversation = {
      firstMessageDirection: parseConversations.summary.firstMessageDirection,
      agentCode: req.body.agentCode,
      agentName: agentName,
      createdAt: moment().toDate(),
      bucketStorage: file.publicUrl(),
      docRef: leadsAgentConversationDocRef,
      startInboundChatAt: parseConversations.summary.startInboundChatAt
        ? moment(parseConversations.summary.startInboundChatAt, 'DD/MM/YYYY HH:mm').toDate()
        : null,
      endInboundChatAt: parseConversations.summary.endInboundChatAt
        ? moment(parseConversations.summary.endInboundChatAt, 'DD/MM/YYYY HH:mm').toDate()
        : null,
      startOutboundChatAt: moment(
        parseConversations.summary.startOutboundChatAt,
        'DD/MM/YYYY HH:mm'
      ).toDate(),
      endOutboundChatAt: moment(
        parseConversations.summary.endOutboundChatAt,
        'DD/MM/YYYY HH:mm'
      ).toDate(),
      startChatAt: moment(parseConversations.summary.startChatAt, 'DD/MM/YYYY HH:mm').toDate(),
      endChatAt: moment(parseConversations.summary.endChatAt, 'DD/MM/YYYY HH:mm').toDate(),
    };
    leadsDocData.agentWhatsappConversations.push(agentConversation);

    const leadsAgentConversationDocData = {
      ...agentConversation,
      type: req.body.type,
      phoneNumber: req.body.phoneNumber,
      firstName: leadsDocData.firstName,
      lastName: leadsDocData.lastName,
      leadsDocRef: leadsDocData.ref,
      organization: leadsDocData.organization,
      conversations: parseConversations.messages.map(c => {
        return {
          datetime: moment(c.datetime, 'DD/MM/YYYY HH:mm').toDate(),
          name: c.name,
          type: c.type,
          message: c.message,
        };
      }),
    };

    try {
      batch.set(leadsDocData.ref.withConverter(LeadsModel.converter), leadsDocData);
      batch.set(leadsAgentConversationDocRef, leadsAgentConversationDocData);
      await file.save(parseConversations.formattedRaw.join('\n'), {
        public: true,
      });
      await batch.commit();
    } catch (e: any) {
      console.log(e);
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }

    try {
      const row: NewLeadsBigqueryTypes = {
        event_source: 'triforce',
        agent_code: leadsDocData.agentCode,
        agent_name: leadsDocData.agentName,
        agent_phone_number: leadsDocData.phoneNumberAgent?.[0] || null,
        city_group: leadsDocData.area || null,
        city_code: leadsDocData.domicile?.cityCode || null,
        city_name: leadsDocData.domicile?.cityName || null,
        province_code: leadsDocData.domicile?.provinceCode || null,
        province_name: leadsDocData.domicile?.provinceName || null,
        create_date_time: bigQuery.datetime(now.format('YYYY-MM-DD HH:mm:ss')),
        has_vehicle_loan: null,
        lead_conversation_count_inbound: parseConversations.summary.inbound,
        lead_conversation_count_outbound: parseConversations.summary.outbound,
        lead_conversation_file_url: file.publicUrl(),
        lead_conversation_resume: null,
        lead_email: leadsDocData.email,
        lead_name: leadsDocData.firstName + ' ' + leadsDocData.lastName,
        lead_phone_number: leadsDocData.phoneNumber,
        notes: leadsDocData.notes,
        organization: leadsDocData.organization,
        payment_plan: leadsDocData.paymentPlan,
        source: leadsDocData.source,
        vehicle_usage: null,
        lead_conversation_start_time: bigQuery.datetime(
          moment(parseConversations.summary.startChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_end_time: bigQuery.datetime(
          moment(parseConversations.summary.endChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_inbound_start_time: bigQuery.datetime(
          moment(parseConversations.summary.startInboundChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_inbound_end_time: bigQuery.datetime(
          moment(parseConversations.summary.endInboundChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_outbound_start_time: bigQuery.datetime(
          moment(parseConversations.summary.startOutboundChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        lead_conversation_outbound_end_time: bigQuery.datetime(
          moment(parseConversations.summary.endOutboundChatAt, 'DD/MM/YYYY HH:mm').format(
            'YYYY-MM-DD HH:mm:ss'
          )
        ),

        type: req.body.type === 'update' ? 'update_conversation' : req.body.type,
      };
      await newLeadsBigQueryTable.insert(row);
    } catch (e) {
      console.log('FAILED_INSERT_NEW_LEADS_BIG_QUERY', JSON.stringify(e));
    }

    res.send(
      successResponse({
        data: parseConversations.summary,
        type: 'CREATED',
      })
    );
  },
};

export default updateLeadsConversations;
