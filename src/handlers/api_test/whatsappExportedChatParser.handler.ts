import { Request<PERSON>and<PERSON> } from 'express';
import multer from 'multer';
import successResponse from '../../schema/successResponse';
import errorResponse from '../../schema/errorResponse';
import parseExportedWhatsappChat, { ParsedChat } from '../../helpers/parseExportedWhatsappChat';
import { bucketKataAi } from '../../services/cloudStorage';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../middlewares/requestValidator';

const fileUpload = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

const whatsappExportedChatParserHandler: {
  middlewares: RequestHand<PERSON>[];
  handler: Request<PERSON>and<PERSON>;
} = {
  middlewares: [
    fileUpload.single('conversations'),
    body('phoneNumber')
      .optional()
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    requestValidator,
  ],
  handler: async (req, res) => {
    if (!req.file?.buffer) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
        })
      );
    }

    const fileName = req.file.originalname;
    const file = bucketKataAi.file('errorFileLeadsConversations/' + fileName);

    const read = req.file?.buffer.toString('utf-8');
    let parse!: ParsedChat;
    try {
      parse = parseExportedWhatsappChat(read);
      if (parse.summary.outbound === 0 && parse.summary.inbound === 0) {
        throw new Error('ERROR_READ_FILE');
      }
    } catch (e) {
      console.log(e);
      if (process.env.NODE_ENV === 'production') {
        await file.save(req.file.buffer, {
          public: true,
        });
      }
      console.log('ERROR_READ_FILE_LEADS_CONVERSATION', file.publicUrl());
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Error ketika parsing file',
        })
      );
    }

    if (req.body.phoneNumber) {
      if (
        parse.summary.inboundPhoneNumber &&
        parse.summary.inboundPhoneNumber !== req.body.phoneNumber
      ) {
        return res.status(500).send(
          errorResponse({
            type: 'SERVER_ERROR',
            data: parse,
            messages:
              'Pesan yang masuk terdeteksi bukan berasal dari nomor telepon leads yang sama.',
          })
        );
      }
    }

    res.send(
      successResponse({
        data: parse,
      })
    );
  },
};

export default whatsappExportedChatParserHandler;
