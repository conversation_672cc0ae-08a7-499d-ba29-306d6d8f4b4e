import { json, Request, Response } from 'express';
import { LeadsOrganization } from '../../../types/firestore/leads_model.types';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../../middlewares/requestValidator';
import triForceAuthChecker from '../../../middlewares/triforceAuthChecker';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import { leadsNotesCounter } from '../../../helpers/leads/leadsNotes';

interface ReqBody {
  leadsPhoneNumber: string;
  notes: string;
  organization: LeadsOrganization;
  agentCode: string;
}

const reactivateTracking = {
  middlewares: [
    triForceAuthChecker,
    json(),
    body('agentCode').notEmpty(),
    body('leadsPhoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('notes')
      .optional()
      .isLength({ min: 30, max: 600 })
      .withMessage('Catatan Minimal 30 s.d 600 karakter.'),
    body('organization')
      .notEmpty()
      .isIn(['amartahonda', 'amartachery', 'amartaneta', 'amartavinfast']),
    requestValidator,
  ],
  handler: async (req: Request<any, ResponseSchema, ReqBody>, res: Response<ResponseSchema>) => {
    const collection = myFirestore.collection('leads');
    const leadsNotesCollection = myFirestore.collection('leads_notes');
    const getLeads = await collection
      .withConverter(LeadsModel.converter)
      .doc(`${req.body.organization}-${req.body.leadsPhoneNumber}`)
      .get();

    if (!getLeads.exists) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    if (leads.agentCode !== req.body.agentCode) {
      return res.status(404).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages:
            'Leads tidak ditemukan atau Leads ini bukan didaftarkan oleh kode agen yang bersangkutan',
        })
      );
    }

    if (leads.isTracking) {
      return res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: 'Leads masih dalam status tracking',
        })
      );
    }

    // if(leads.pendingRequestReactivateTracking?.isPending) {
    // 	return res.status(500).send(errorResponse({
    // 		type: "SERVER_ERROR",
    // 		messages: "Leads ini masih menunggu persetujuan untuk aktivasi Tracking"
    // 	}))
    // }

    leads.pendingRequestReactivateTracking = {
      isPending: false,
      notes: req.body.notes,
      requestedAt: new Date(),
      response: {
        updatedAt: new Date(),
        positive: true,
      },
    };

    const notesCounter = await leadsNotesCounter(leads);

    let notes: ILeadsNotesDocument<Date> = {
      agentCode: leads.agentCode,
      event: 'reactivateTrack',
      notes: `Leads di aktifkan kembali sebanyak ${notesCounter.totalReactivate + 1} kali: ${leads.pendingRequestReactivateTracking?.notes ?? ''}`,
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: null,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: leads.agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: notesCounter.totalReactivate + 1,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.notes = notes.notes;
    leads.isTracking = true;
    leads.updatedAt = new Date();
    leads.updateHistories.push({
      ...notes,
    });

    try {
      const batch = myFirestore.batch();
      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      if (notes) {
        batch.set(leadsNotesCollection.doc(), notes);
      }
      await batch.commit();

      // if(leads.organization === "amartachery") {
      // 	let messageTelegram = `=== LEADS UPDATE NOTES ===` +
      // 		`\n` +
      // 		`\nNama Depan: ${leads.firstName}` +
      // 		`\nNama Akhir: ${leads.lastName}` +
      // 		`\nNomor Telepon: ${hidePhoneNumber(leads.phoneNumber)}` +
      // 		`\nNama Agen: ${leads.agentName}` +
      // 		`\nKode Agen: ${leads.agentCode}` +
      // 		`\nNotes: ${leads.notes}` +
      // 		`\nModel: ${leads.vehicleOptions.map(value => value.model).join(", ")}`
      // 	;
      //
      // 	telegramServices.sendMessage ("-1002118167928", messageTelegram)
      // 		.then()
      // 		.catch()
      // }

      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: `Error: ${e.toString()}`,
        })
      );
    }
  },
};

export default reactivateTracking;
