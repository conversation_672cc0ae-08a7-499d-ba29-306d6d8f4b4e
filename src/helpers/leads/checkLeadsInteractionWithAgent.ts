import webWhatsappServices from '../../services/WebWhatsappServices';
import LeadsModel from '../../model/LeadsModel';
import { myFirestore } from '../../services/firebaseAdmin';
import moment, { Moment } from 'moment';
import { Message } from '../../types/services/webWhatsapp/SuccessResponseGetLeadsChat.types';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import aggregatesAgent from './aggregatesAgent';
import { leadsNotesCounter } from './leadsNotes';

async function checkLeadsInteractionWithAgent() {
  const agentCodes: string[] = [];
  try {
    const getLoggedInWA = await webWhatsappServices.checkActiveInstance();
    const data = getLoggedInWA.data.data;
    for (const dataKey in data) {
      if (data[dataKey].loggedIn) {
        agentCodes.push(dataKey);
      }
    }
  } catch (e) {}

  const collections = myFirestore.collection('leads');
  const momentObjNow = moment().subtract(1, 'day');
  console.log(
    'CRON_LEADS_CHECK_INTERACTION_WITH_AGENT',
    momentObjNow.format('YYYY-MM-DD HH:mm:ss')
  );
  for (const agentCode of agentCodes) {
    const batch = myFirestore.batch();
    const leadsList: LeadsModel[] = [];
    const getLeads = await collections
      .where('isTracking', '==', true)
      .where('agentCode', '==', agentCode)
      .where('organization', '==', 'amartahonda')
      .withConverter(LeadsModel.converter)
      .get();

    getLeads.forEach(result => {
      leadsList.push(result.data());
    });

    for (const leads of leadsList) {
      try {
        const fetchMessages = await webWhatsappServices.getChatFromLeads({
          agentCode: agentCode,
          leadsPhoneNumber: leads.phoneNumber,
        });
        const messages: Message[] = JSON.parse(JSON.stringify(fetchMessages.data.data.messages));
        let untrackThis = false;
        let _notes = '';

        if (messages.length > 0) {
          messages.reverse();
          const findInboundMessage = messages.find(m => !m.fromMe);
          let endDate: Moment;
          if (findInboundMessage) {
            const momentObj = moment.unix(findInboundMessage.timestamp);
            endDate = momentObj.clone().add(1, 'week');
          } else {
            endDate = moment(leads.createdAt).add(8, 'days');
          }

          if (momentObjNow.isAfter(endDate)) {
            untrackThis = true;
            _notes = 'Tidak ada pesan masuk dari Leads seminggu terakhir.';
          }
        }

        if (untrackThis) {
          const notesCounter = await leadsNotesCounter(leads);
          // console.log("UNTRACK", agentCode, leads.phoneNumber, leads.createdAt);
          let notes: ILeadsNotesDocument<Date> = {
            agentCode: leads.agentCode,
            event: 'untrack',
            notes: 'Leads di Untrack oleh sistem: ' + _notes,
            organization: leads.organization,
            phoneNumber: leads.phoneNumber,
            statusLevel: leads.statusLevel,
            updatedAt: new Date(),
            updatedByUser: null,
            firstName: leads.firstName,
            lastName: leads.lastName,
            agentName: leads.agentName,
            moveToCold: false,
            reactivate: {
              currentTotal: notesCounter.totalReactivate,
            },
            totalUpdateNotes: notesCounter.totalNotes + 1,
          };

          leads.notes = notes.notes;
          leads.isTracking = false;
          leads.updatedAt = new Date();
          leads.updateHistories.push({
            ...notes,
          });

          batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
          batch.set(myFirestore.collection('leads_notes').doc(), notes);
        } else {
          // console.log("TRACK", agentCode, leads.phoneNumber, leads.createdAt);
        }
      } catch (e) {}
    }

    try {
      if (process.env.NODE_ENV === 'production') {
        await batch.commit();
        await aggregatesAgent(agentCode, 'amartahonda').then();
      }
    } catch (e) {
      console.log('ERROR_COMMIT', agentCode, e);
    }
  }
}

export default checkLeadsInteractionWithAgent;
