interface PriceListRow {
  dp_percentage: number | null;
  dp_nominal: number;
  tenor: number;
  installment: number;
  promo_dp_percentage: number | null;
  promo_dp_nominal: number | null;
  promo_tenor: number | null;
  promo_installment: number | null;
}

interface PricelistData {
  otr: number;
  normal: PriceListRow[];
}

export interface IPriceListFile {
  source: string;
  phoneNumber: string;
  company: string;
  city_group: string;
  variant_code: string;
  title: string;
  caption: string;
  created_at: string;
  expired_at: string;
  admin_name: string;
  pricelist_data: PricelistData;
}
