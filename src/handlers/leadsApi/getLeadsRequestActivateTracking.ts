import { Request, Response } from 'express';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import successResponse from '../../schema/successResponse';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { param, query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';

const getLeadsRequestActivateTracking = {
  middlewares: [
    leadsAuthorizer,
    param('organization').notEmpty().trim(),
    query('phoneNumber').optional().trim(),
    requestValidator,
  ],
  handler: async function (req: Request, res: Response) {
    const collections = myFirestore.collection('leads');
    let getQuery = collections
      .where('organization', '==', req.params.organization)
      .where('isTracking', '==', false)
      .where('pendingRequestReactivateTracking.isPending', '==', true)
      .orderBy('updatedAt', 'desc');
    if (req.query.phoneNumber) {
      getQuery = getQuery.where('phoneNumber', '==', req.query.phoneNumber);
    }

    const get = await getQuery.withConverter(LeadsModel.converter).get();

    const leads: LeadsModel[] = [];

    get.forEach(result => {
      const data = result.data();
      leads.push(data);
    });

    res.send(
      successResponse({
        data: leads.map(value => value.toJsonResponse()),
        type: 'FETCHED',
      })
    );
  },
};

export default getLeadsRequestActivateTracking;
