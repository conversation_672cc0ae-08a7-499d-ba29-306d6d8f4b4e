import axios from 'axios';

export interface FetchAgentResponseSuccess {
  data: {
    name: string;
    phones: { phone: string }[];
    user_type: string;
  };
}

async function fetchAgent(agentCode: string): Promise<FetchAgentResponseSuccess['data']> {
  const get = await axios.get<FetchAgentResponseSuccess>(
    'https://3krjkubmjk.execute-api.ap-southeast-3.amazonaws.com/v1/user/agent-code/' + agentCode,
    {
      headers: {
        'X-Api-Key': 'NqWjTj6qpI7LK8JUKvkQz3isRY9NGU7faeWwv2ff',
      },
    }
  );
  return get.data.data;
}

async function getAgent(agentCode: string) {
  try {
    const fetch = await fetchAgent(agentCode);
    return {
      name: fetch.name,
      phoneNumbers: fetch.phones,
      type: fetch.user_type,
    };
  } catch (e: any) {
    return null;
  }
}

export default fetchAgent;
export { getAgent };
