import { raw, Router } from 'express';
import addFreeLeadsFromRawLeads from '../handlers/webhook_and_cron/leads/addFreeLeadsFromRawLeads';
import sendRawFreeLeadsToBigQuery from '../handlers/webhook_and_cron/leads/sendRawFreeLeadsToBigQuery';
import notifyAvailableFreeLeadsAmartachery from '../handlers/webhook_and_cron/leads/notifyAvailableFreeLeadsAmartachery';
import moveToCloudUnactiveLeadsAllAgent from '../handlers/webhook_and_cron/leads/moveToCloudUnactiveLeadsAllAgent';
import notifyAvailableFreeLeadsAmartahonda from '../handlers/webhook_and_cron/leads/notifyAvailableFreeLeadsAmartahonda';
import leadsAmartahondaDontHaveFeedback from '../handlers/webhook_and_cron/leads/leadsAmartahondaDontHaveFeedback';
import leadsAmartahondaMoreThanXDays from '../handlers/webhook_and_cron/leads/leadsAmartahondaMoreThanXDays';
import moveToColdFreeLeads from '../handlers/webhook_and_cron/freeLeads/moveToColdFreeLeads';
import logSourceIdTodayHandler from '../handlers/webhook_and_cron/logSourceId/logSourceIdTodayHandler';
import leadsFromFreeLeadsDontHaveFileChatHandler from '../handlers/webhook_and_cron/leads/leadsFromFreeLeadsDontHaveFileChat.handler';
import notifyActiveLeadsAmartamobil from '../handlers/webhook_and_cron/leads/notifyActiveLeadsAmartamobil';

const router = Router();

export default function (parent: Router) {
  parent.use('/cron', router);

  router.post('/add-free-leads-from-raw-leads', addFreeLeadsFromRawLeads.handler); // Dihapus
  router.post('/raw-leads-to-bigquery', sendRawFreeLeadsToBigQuery.handler);
  router.post('/notify-total-free-leads-amartachery', notifyAvailableFreeLeadsAmartachery.handler);
  router.post('/notify-total-free-leads-amartahonda', notifyAvailableFreeLeadsAmartahonda.handler);
  router.post('/move-to-cold-untracked-leads', moveToCloudUnactiveLeadsAllAgent.handler);

  router.post(
    '/notify-total-leads-amartahonda-dont-have-feedback',
    leadsAmartahondaDontHaveFeedback.handler
  ); // Dihapus
  router.post(
    '/notify-total-leads-amartahonda-more-than-x-days',
    leadsAmartahondaMoreThanXDays.handler
  ); // Dihapus

  router.post(
    '/create-bill-free-leads-not-update-file-whatsapp',
    leadsFromFreeLeadsDontHaveFileChatHandler.middlewares,
    leadsFromFreeLeadsDontHaveFileChatHandler.handler
  );

  router.post(
    '/move-free-leads-to-cold',
    moveToColdFreeLeads.middlewares,
    moveToColdFreeLeads.handler
  );

  router.post(
    '/send-log-source-id',
    logSourceIdTodayHandler.middlewares,
    logSourceIdTodayHandler.handler
  );

  router.post('/notify-total-leads-amartamobil', notifyActiveLeadsAmartamobil.handler);
}
