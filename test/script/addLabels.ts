import { myFirestore } from '../../src/services/firebaseAdmin';

const datas = [
  {
    active: true,
    description: null,
    group: 'vip',
    name: '1 TAHUN VIP',
    order: 401,
  },
  {
    active: true,
    description: null,
    group: 'vip',
    name: '2 TAHUN VIP',
    order: 402,
  },
  {
    active: true,
    description: null,
    group: 'vip',
    name: '3 TAHUN VIP',
    order: 403,
  },
  {
    active: true,
    description: null,
    group: 'vip',
    name: '4 TAHUN UP VIP',
    order: 404,
  },
  {
    active: true,
    description: null,
    group: 'vip',
    name: 'NON VIP',
    order: 404,
  },
];

const addLabels = {
  add: async () => {
    const collection = myFirestore.collection('/projects/B6hfkRsnmcXsKUpFYjN8/labels');

    const batch = myFirestore.batch();

    for (const datum of datas) {
      batch.set(collection.doc(), {
        ...datum,
      });
    }

    await batch.commit();
  },

  run: () => {
    addLabels.add();
  },
};

addLabels.run();
