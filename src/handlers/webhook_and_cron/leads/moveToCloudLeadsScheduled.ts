import { Request, Response } from 'express';
import { myFirestore } from '../../../services/firebaseAdmin';
import moment from 'moment';
import ColdLeadsModel from '../../../model/ColdLeadsModel';
import LeadsModel from '../../../model/LeadsModel';
import { ILeadsNotesDocument } from '../../../types/firestore/leads_notes.types';
import successResponse from '../../../schema/successResponse';
import { leadsNotesCounter } from '../../../helpers/leads/leadsNotes';

const moveToCloudLeadsScheduled = {
  middlewares: [],
  handler: async (req: Request, res: Response) => {
    const leadsCollection = myFirestore.collection('leads');
    const coldLeadsCollection = myFirestore.collection('cold_leads');
    const coldLeadsHistoriesCollection = myFirestore.collection('cold_leads_histories');
    const today = moment();

    const find = await leadsCollection
      .where('isTracking', '==', false)
      .where('needToMoveToColdAt', '>=', today.toDate())
      .where('needToMoveToColdAt', '<=', today.toDate())
      .withConverter(LeadsModel.converter)
      .get();

    const batch = myFirestore.batch();

    const leadsData: LeadsModel[] = [];

    find.forEach(result => {
      const data = result.data();
      leadsData.push(data);
    });

    for (const leads of leadsData) {
      const notesCounter = await leadsNotesCounter(leads);

      let notes: ILeadsNotesDocument<Date> = {
        agentCode: leads.agentCode,
        event: 'moveToCold',
        notes: 'Move to Cold System',
        organization: leads.organization,
        phoneNumber: leads.phoneNumber,
        statusLevel: leads.statusLevel,
        updatedAt: new Date(),
        updatedByUser: null,
        firstName: leads.firstName,
        lastName: leads.lastName,
        agentName: leads.agentName,
        moveToCold: true,
        reactivate: {
          currentTotal: notesCounter.totalReactivate,
        },
        totalUpdateNotes: notesCounter.totalNotes + 1,
      };

      const coldLeads = new ColdLeadsModel({
        ...leads,
        tradeIn: null,
        moveToColdAt: new Date(),
        moveToColdBy: req.body.movedBy,
        coldNotes: req.body.notes,
        moveToColdByUserType: 'admin',
      });

      const docName = `${leads.organization}-${leads.phoneNumber}`;

      batch.set(
        leadsCollection.doc(leads.ref.id).withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.set(
        coldLeadsCollection.doc(docName).withConverter(ColdLeadsModel.converter),
        coldLeads
      );
      batch.set(
        coldLeadsHistoriesCollection.doc().withConverter(ColdLeadsModel.converter),
        coldLeads
      );

      batch.set(myFirestore.collection('leads_notes').doc(), notes);
      batch.delete(leads.ref);
    }

    try {
      await batch.commit();
      res.send(
        successResponse({
          data: null,
        })
      );
    } catch (e) {
      res.send(
        successResponse({
          data: null,
          type: 'CREATED',
        })
      );
    }
  },
};

export default moveToCloudLeadsScheduled;
