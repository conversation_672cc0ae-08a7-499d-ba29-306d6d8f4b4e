import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import { firestore } from 'firebase-admin';
import fetchAgent from '../../services/agent/fetchAgent';
import { LeadsOrganization } from '../../types/firestore/leads_model.types';
import ColdLeadsModel from '../../model/ColdLeadsModel';

async function aggregatesAgent(
  agentCode: string,
  organization: LeadsOrganization,
  batch?: firestore.WriteBatch
) {
  const leadsCollection = myFirestore.collection('leads');
  const coldLeadsCollection = myFirestore.collection('cold_leads');

  const leads: LeadsModel[] = [];
  const findLeadsQuery = await leadsCollection
    .where('agentCode', '==', agentCode)
    .withConverter(LeadsModel.converter)
    .where('organization', '==', organization)
    .get();
  findLeadsQuery.forEach(result => {
    const data = result.data();
    leads.push(data);
  });

  const findColdLeadsQuery = await coldLeadsCollection
    .where('agentCode', '==', agentCode)
    .withConverter(ColdLeadsModel.converter)
    .where('organization', '==', organization)
    .get();
  const coldLeads: ColdLeadsModel[] = [];
  findColdLeadsQuery.forEach(result => {
    const data = result.data();
    coldLeads.push(data);
  });

  const collectionCounter = myFirestore.collection('leads_agent_counter');
  const getCounterAgent = await collectionCounter.doc(`${organization}-${agentCode}`).get();

  const trackingLeads = leads.filter(value => {
    return value.isTracking;
  });

  const dataToUpdate: any = {
    updatedAt: firestore.Timestamp.now(),
  };
  dataToUpdate['leads'] = trackingLeads.length;

  dataToUpdate['totalTrackedLeads'] = trackingLeads.length;
  dataToUpdate['totalAllLeads'] = leads.length;
  dataToUpdate['totalLeadsWithCold'] = leads.length + coldLeads.length;

  const ref = getCounterAgent.ref;

  for (let i = 0; i <= 5; i++) {
    dataToUpdate['statusLevel_' + i] = leads.reduce((previousValue, currentValue) => {
      if (currentValue.statusLevel === i) {
        return previousValue + 1;
      } else {
        return previousValue;
      }
    }, 0);
  }

  let agentName: string | null = null;
  try {
    const fetch = await fetchAgent(agentCode);
    agentName = fetch.name;
  } catch {
    agentName = 'NO_NAME';
  }

  const dataToSet = {
    agentName: agentName,
    organization: organization,
    agentId: agentCode,
    ...dataToUpdate,
  };

  if (batch) {
    batch.set(ref, dataToSet);
  } else {
    await ref.set(dataToSet);
  }
}

export default aggregatesAgent;
