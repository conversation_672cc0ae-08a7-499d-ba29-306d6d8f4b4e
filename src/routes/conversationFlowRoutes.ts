import { Router } from 'express';
import addConversationFlow from '../handlers/conversationFlow/addConversationFlow';
import updateConversationFlow from '../handlers/conversationFlow/updateConversationFlow';

const router = Router();

export default function (parent: Router) {
  parent.use('/conversation-flow', router);
  router.post('/add-new-flow', addConversationFlow.middlewares, addConversationFlow.handler);
  router.post('/update-flow', updateConversationFlow.middlewares, updateConversationFlow.handler);
}
