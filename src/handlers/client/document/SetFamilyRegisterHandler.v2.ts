import { IFamilyRegister } from '../../../types/firestore/family_registration_document_types';
import { Request, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import { body } from 'express-validator';
import multer from 'multer';
import requestChecker from '../../../middlewares/requestValidator';
import successResponse from '../../../schema/successResponse';
import { myFirestore } from '../../../services/firebaseAdmin';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import errorResponse from '../../../schema/errorResponse';
import { File } from '@google-cloud/storage';
import ClientModel from '../../../model/ClientModel';
import moment from 'moment';

export interface IFamilyRegisterCaptureFields {
  familyRegisterNumber: string;
}

export interface FamilyRegisterReqBody extends IFamilyRegisterCaptureFields {
  clientRef: string;
}

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export default class SetFamilyRegisterV2Handler {
  public static middlewares = [
    formDataParse.single('image'),
    body('familyRegisterNumber')
      .notEmpty()
      .withMessage('Nomor Register Keluarga tidak boleh kosong')
      .trim()
      .isNumeric()
      .isLength({ min: 16, max: 16 })
      .withMessage('Nomor KK harus 16 digit'),
    body('clientRef').notEmpty(),
    requestChecker,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, FamilyRegisterReqBody>,
    res: Response<ResponseSchema>
  ) {
    const globalVar: {
      file: File | null;
      currentFamilyRegisterData: IFamilyRegister | null;
    } = {
      file: null,
      currentFamilyRegisterData: null,
    };

    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.withConverter(ClientModel.converter).get();
    const dataClient = getClient.data()!;
    globalVar.currentFamilyRegisterData = dataClient.details?.familyRegister || null;

    if (req.file?.buffer) {
      globalVar.file = bucketTrimitraIdeal.file(
        `client-documents/${clientDocRef.id}/family-register.jpg`
      );
      await globalVar.file.save(req.file.buffer, {
        public: true,
      });
    }

    const now = moment();

    const dataFamilyRegister: IFamilyRegister = {
      familyRegisterImage:
        globalVar.file?.publicUrl() ||
        globalVar.currentFamilyRegisterData?.familyRegisterImage ||
        '',
      familyRegisterNumber: req.body.familyRegisterNumber,
      updatedAt: now.toDate(),
    };

    try {
      await clientDocRef.update({
        'details.familyRegister': dataFamilyRegister,
      });
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }

    res.send(
      successResponse({
        type: 'UPDATED',
        data: dataFamilyRegister,
      })
    );
  }
}
