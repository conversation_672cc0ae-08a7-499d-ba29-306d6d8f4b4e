import { Request, Response } from 'express';
import { ResponseSchema } from '../../../schema/types/ResponseSchema';
import multer from 'multer';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import { bucketTrimitraIdeal } from '../../../services/cloudStorage';
import successResponse from '../../../schema/successResponse';
import errorResponse from '../../../schema/errorResponse';
import { File } from '@google-cloud/storage';
import { IPlaceOfBusinessFields } from '../../../types/services/profile_service_types';
import { profileService } from '../../../services/profileService';

export interface SetPlaceOfBusinessFields {
  fullAddress: string;
  latitude: string;
  longitude: string;
  businessSize: string;
  businessType: string;
}

export interface SetPlaceOfBusinessReqBody extends SetPlaceOfBusinessFields {
  imageUrl: string;
  clientRef: string;
}

const formDataParse = multer({
  // @ts-ignore
  startProcessing(req, busboy) {
    if (req.rawBody) {
      // indicates the request was pre-processed
      busboy.end(req.rawBody);
    } else {
      req.pipe(busboy);
    }
  },
});

export default class SetPlaceOfBusiness {
  public static middlewares = [
    formDataParse.single('image'),
    body('fullAddress').notEmpty(),
    body('latitude').optional(),
    body('longitude').optional(),
    body('businessSize').notEmpty(),
    body('businessType').notEmpty(),

    body('imageUrl').optional(),

    body('clientRef').notEmpty(),
    requestValidator,
  ];

  public static async handler(
    req: Request<any, ResponseSchema, SetPlaceOfBusinessReqBody>,
    res: Response<ResponseSchema>
  ) {
    const clientDocRef = myFirestore.doc(req.body.clientRef);
    const getClient = await clientDocRef.get();

    const payload: IPlaceOfBusinessFields = {
      FullAddress: req.body.fullAddress,
      Latitude: req.body.latitude ?? null,
      Longitude: req.body.longitude ?? null,
      BusinessSize: req.body.businessSize,
      BusinessType: req.body.businessType,
    };

    let file: File | null = null;
    if (req.file?.buffer) {
      file = bucketTrimitraIdeal.file(`client-documents/${clientDocRef.id}/place-of-business.jpg`);
    }

    try {
      if (file) {
        if (req.file)
          await file.save(req.file.buffer, {
            public: true,
          });

        payload.PlaceOfBusinessImage = file.publicUrl();
      } else {
        payload.PlaceOfBusinessImage = req.body.imageUrl;
      }

      const update = await profileService.updatePlaceOfBusiness(
        payload,
        getClient.data()!.details.owner_phone_number
      );

      res.send(
        successResponse({
          type: 'CREATED',
          data: update?.data,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
        })
      );
    }
  }
}
