import LeadsModel from '../../model/LeadsModel';
import sendTemplate from '../../services/sendTemplate/sendTemplate';

export default async function afterAddLeads(leadsDoc: LeadsModel): Promise<boolean> {
  if (['amartachery', 'amartaneta', 'amartavinfast'].indexOf(leadsDoc.organization) > 0) {
    await sendTemplate.sendTemplateMetaV2({
      projectId: 'kpsDdEcRtReOQrCYkzq2',
      bindDocuments: [
        {
          path: leadsDoc.ref.path,
          context: 'add_leads',
        },
      ],
      target: leadsDoc.phoneNumber,
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'image',
              image: {
                link: 'https://trimitra-cdn.s3.ap-southeast-1.amazonaws.com/assets/image/vinfastheader.jpeg',
              },
            },
          ],
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: `Kak ${leadsDoc.firstName}`,
            },
            {
              type: 'text',
              text: `${leadsDoc.agentName.split('.')[leadsDoc.agentName.split('.').length - 1]}`,
            },
          ],
        },
      ],
      template_name: 'lead_follow_up1',
      contactName: `${leadsDoc.firstName} ${leadsDoc.lastName}`,
    });
  }

  return true;
}
