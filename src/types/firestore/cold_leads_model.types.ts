import { ILeadsDocument } from './leads_model.types';
import { firestore } from 'firebase-admin';

export interface IColdLeadsDocument<TIMESTAMP = firestore.Timestamp>
  extends Omit<ILeadsDocument<TIMESTAMP>, 'needToMoveToColdAt' | 'agentCode' | 'agentName'> {
  agentCode: null | string;
  agentName: null | string;
  moveToColdAt: TIMESTAMP;
  moveToColdBy: string;
  coldNotes: string;
  moveToColdByUserType: 'agent' | 'admin' | 'system';
}
