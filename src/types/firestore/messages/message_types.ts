import {
  <PERSON><PERSON>ontext,
  <PERSON>R<PERSON>ly<PERSON><PERSON>xt,
  IStickerContext,
  MessageContext,
  TextContext,
} from '../../webhook/webhook_message_types';
import { firestore } from 'firebase-admin';
import { IVariant } from '../../services/autotrimitra_service_types';

export interface IFirestoreMessageEntity {
  origin: {
    id: string;
    display_name: string;
    reference: string | firestore.DocumentReference;
  };
  message: {
    id: string;
    audio?: FileContext;
    document?: FileContext;
    image?: FileContext;
    video?: FileContext;
    voice?: FileContext;
    text?: TextContext;
    sticker?: IStickerContext;
    context?: IReplyContext;
    unixtime: number;
    timestamp: firestore.Timestamp | Date | string;
    type: MessageContext;
    direction: EMessageDirection;
  };
  statuses?: any;
  error?: {
    code: number;
    href: string;
    title: string;
  };
  session_id: string;
}

type EMessageDirection = 'IN' | 'OUT';
type EAdminRole = 'admin' | 'owner';

export interface IFirestoreAdminEntity {
  name: string;
  email: string;
  phone_number: string;
  doc_admin: any;
  doc_department: any;
  count_clients: number;
  doc_project: any;
  address: string;
  level: string;
  active: boolean;
  created_time: Date;
  ref?: firestore.DocumentReference;
}

export interface IFirestoreDistributedRoom {
  admin: null;
  contacts: any;
  clients: any;
  group: boolean;
  headers: {
    subtitle?: string;
    title?: string;
  };
  recent_chat: {
    contact?: string;
    statuses?: any;
    type: string;
    direction: string;
    display_name: string;
    read: boolean;
    unixtime: number;
    timestamp: Date;
    text: string;
  };
  type: string;
}

export type TStatuses = Record<EStatuses, number>;

export type EStatuses = 'sent' | 'delivered' | 'read';
