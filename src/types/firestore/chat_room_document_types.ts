import { firestore } from 'firebase-admin';

export interface IChatRoomDocument {
  admin: null;
  contacts: string[];
  clients: firestore.DocumentReference[];
  cityGroup: string | null;
  cityGroupUpdatedAt: firestore.Timestamp | null;
  organization: string | null;
  organizationGroup: string | null;
  organizationUpdatedAt: firestore.Timestamp | null;
  group: boolean;
  headers: {
    subtitle: string;
    title: string;
  };
  recent_chat: {
    contact: string;
    statuses: TStatuses;
    type: string;
    direction: string;
    display_name: string;
    read: boolean;
    unixtime: number;
    timestamp: firestore.Timestamp;
    text: string;
  };
  provider: string;
  created_at: firestore.Timestamp;
  last_inbound: firestore.Timestamp;
  doc_department: firestore.DocumentReference | null;
  in_free_leads: boolean | null;
}

export type TStatuses = Record<EStatuses, firestore.Timestamp | null>;
export type EStatuses = 'sent' | 'delivered' | 'read' | 'failed';
