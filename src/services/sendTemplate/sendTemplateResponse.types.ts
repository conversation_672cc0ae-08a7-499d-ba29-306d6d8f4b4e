interface Contacts {
  input: string;
  wa_id: string;
}

interface Messages {
  id: string;
}

interface MetaResponse {
  messaging_product: string;
  contacts: Contacts[];
  messages: Messages[];
}

interface DataSuccessResponseMeta {
  metaResponse: MetaResponse;
  templateName: string;
}

export interface SuccessResponseMeta {
  success: boolean;
  data: DataSuccessResponseMeta[];
}

export interface SuccessResponseQiscus {
  data: MetaResponse;
}
