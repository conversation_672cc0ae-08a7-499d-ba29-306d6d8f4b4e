import { Router } from 'express';
import accountRoutes from './accountRoutes';
import messageRoutes from './message.routes';
import projectRoutes from './projectRoutes';
import clientDocumentRoutes from './clientDocumentRoutes';
import clientProfileRoutes from './clientProfileRoutes';
import triForceRoutes from './triforce.routes';
import apiRoutes from './api.routes';
import GetImageFromS3 from '../handlers/GetImageFromS3';
import leadsRoutes from './leads.routes';
import cronRoutes from './cron.routes';
import adminRoutes from './adminRoutes';
import conversationFlowRoutes from './conversationFlowRoutes';
import onboardingRoutes from './onboarding.routes';
import clientTrimobiRoutes from './clientTrimobiRoutes';

export default function () {
  const router = Router();
  const d = new Date();

  router.get('/', function (req, res) {
    res.send({
      version: 3.0,
      time: d.toLocaleTimeString(),
    });
  });

  router.get('/image-show-from-s3', GetImageFromS3.middlewares, GetImageFromS3.handler);

  accountRoutes(router);
  messageRoutes(router);
  projectRoutes(router);
  clientDocumentRoutes(router);
  clientProfileRoutes(router);
  clientTrimobiRoutes(router);
  triForceRoutes(router);
  apiRoutes(router);
  leadsRoutes(router);
  cronRoutes(router);
  adminRoutes(router);
  conversationFlowRoutes(router);
  onboardingRoutes(router);
  return router;
}
