import { Request, Response, NextFunction } from 'express';
import Busboy, { BusboyHeaders } from '@fastify/busboy';

export default function busboyMiddleware(req: Request, res: Response, next: NextFunction) {
  // <PERSON><PERSON> proses jika content-type adalah multipart/form-data
  if (!req.headers['content-type']?.includes('multipart/form-data')) {
    return next();
  }

  const busboy = Busboy({
    headers: req.headers as BusboyHeaders,
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB limit
      files: 1, // Maksimum 1 file
    },
  });

  const fields: Record<string, string> = {};
  let fileData: any = null;

  busboy.on('field', (fieldname: string, value: string) => {
    fields[fieldname] = value;
  });

  busboy.on(
    'file',
    (fieldname: string, file: any, filename: any, encoding: string, mimetype: string) => {
      const chunks: Buffer[] = [];

      file.on('data', (chunk: Buffer) => {
        chunks.push(chunk);
      });

      file.on('end', () => {
        fileData = {
          fieldname,
          originalname: filename,
          encoding,
          mimetype,
          buffer: Buffer.concat(chunks),
        };
      });
    }
  );

  busboy.on('finish', () => {
    req.body = fields;
    if (fileData) {
      req.file = fileData;
    }
    next();
  });

  busboy.on('error', (error: Error) => {
    console.error('Busboy error:', error);
    next(error);
  });

  // Jika berada di Google Cloud Function, gunakan req.rawBody jika tersedia
  if ((req as any).rawBody) {
    busboy.end((req as any).rawBody);
  } else {
    req.pipe(busboy);
  }
}
