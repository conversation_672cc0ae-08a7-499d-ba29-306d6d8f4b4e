import { json, Request, Response } from 'express';
import { LeadsOrganization } from '../../types/firestore/leads_model.types';
import { body } from 'express-validator';
import phoneNumberCountryCodeSanitizer from '../../helpers/phoneNumberCountryCodeSanitizer';
import requestValidator from '../../middlewares/requestValidator';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { myFirestore } from '../../services/firebaseAdmin';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import fetchAgent from '../../services/agent/fetchAgent';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';
import successResponse from '../../schema/successResponse';
import { leadsNotesCounter } from '../../helpers/leads/leadsNotes';

interface ReqBody {
  leadsPhoneNumber: string;
  approvedBy: string;
  organization: LeadsOrganization;
}

const approveReactivateTracking = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('leadsPhoneNumber')
      .notEmpty()
      .withMessage('Phone number is required')
      .isMobilePhone('id-ID')
      .withMessage('Invalid phone number format')
      .customSanitizer(input => phoneNumberCountryCodeSanitizer(input, '62', '62')),
    body('notes')
      .optional()
      .isLength({ min: 30, max: 600 })
      .withMessage('Catatan Minimal 30 s.d 600 karakter.'),
    body('approvedBy').notEmpty(),
    body('organization').notEmpty(),
    requestValidator,
  ],
  handler: async (req: Request<any, Response, ReqBody>, res: Response) => {
    const collection = myFirestore.collection('leads');
    const getLeads = await collection
      .withConverter(LeadsModel.converter)
      .doc(`${req.body.organization}-${req.body.leadsPhoneNumber}`)
      .get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    if (leads.isTracking) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads masih dalam status tracking',
        })
      );
    }

    let agentName: string | null = null;
    if (!leads.agentName) {
      try {
        const fetch = await fetchAgent(leads.agentCode);
        agentName = fetch.name;
      } catch {
        agentName = 'NO_NAME';
      }
    } else {
      agentName = leads.agentName;
    }

    const notesCounter = await leadsNotesCounter(leads);
    const notes: ILeadsNotesDocument<Date> = {
      agentCode: leads.agentCode,
      event: req.params.type,
      notes: `Disetujui dengan catatan: ${leads.pendingRequestReactivateTracking?.notes ?? ''}`,
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: req.body.approvedBy,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.notes = notes.notes;
    leads.isTracking = true;
    leads.updatedAt = new Date();
    leads.updateHistories.push({
      ...notes,
    });

    if (leads.pendingRequestReactivateTracking) {
      leads.pendingRequestReactivateTracking.isPending = false;
      leads.pendingRequestReactivateTracking.response = {
        positive: true,
        updatedAt: new Date(),
      };
    }

    try {
      const batch = myFirestore.batch();

      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      if (notes) {
        batch.set(myFirestore.collection('leads_notes').doc(), notes);
      }

      await batch.commit();

      await aggregatesAgent(leads.agentCode, req.body.organization).then();
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: `Error: ${e.toString()}`,
        })
      );
    }
  },
};

export default approveReactivateTracking;
