import * as fs from 'node:fs';
import { bucketKataAi } from '../../../src/services/cloudStorage';

const uploadFile = () => {
  const fileName = 'template_service_thanks_image_header.jpg';
  const filePath = './' + fileName;
  const imageBuffer = fs.readFileSync(filePath);
  console.log(imageBuffer);

  const file = bucketKataAi.file('templateImages/' + fileName);
  file.save(imageBuffer, {
    public: true,
  });
};

uploadFile();
