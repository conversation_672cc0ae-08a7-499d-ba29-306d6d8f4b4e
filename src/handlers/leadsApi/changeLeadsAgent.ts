import { json, Request, Response } from 'express';
import { body } from 'express-validator';
import fetchAgent, { FetchAgentResponseSuccess } from '../../services/agent/fetchAgent';
import { myFirestore } from '../../services/firebaseAdmin';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import LeadsModel from '../../model/LeadsModel';
import errorResponse from '../../schema/errorResponse';
import { ILeadsNotesDocument } from '../../types/firestore/leads_notes.types';
import aggregatesAgent from '../../helpers/leads/aggregatesAgent';
import successResponse from '../../schema/successResponse';
import { LeadsOrganization } from '../../types/firestore/leads_model.types';
import leadsAuthorizer from '../../middlewares/leadsAuthorizer';
import { leadsNotesCounter } from '../../helpers/leads/leadsNotes';

interface ReqBody {
  phoneNumber: string;
  organization: LeadsOrganization;
  changedAgentCode: string;
}

const changeLeadsAgent = {
  middlewares: [
    leadsAuthorizer,
    json(),
    body('phoneNumber').notEmpty(),
    body('organization').notEmpty(),
    body('changedAgentCode')
      .notEmpty()
      .trim()
      .withMessage('Kode Agen Pengganti dibutuhkan')
      .custom((input, { req }) => {
        return new Promise<FetchAgentResponseSuccess['data']>(async (resolve, reject) => {
          try {
            const fetch = await fetchAgent(input);
            resolve(fetch);
          } catch (e) {
            reject('Kode Agen tidak terdaftar.');
          }
        })
          .then(value => {
            req.res.locals.agent = value;
          })
          .catch(reason => {
            throw new Error(reason);
          });
      }),
  ],
  handler: async function (
    req: Request<any, ResponseSchema, ReqBody>,
    res: Response<ResponseSchema, { agent: FetchAgentResponseSuccess['data'] }>
  ) {
    const collection = myFirestore.collection('leads_notes');
    const getLeads = await collection
      .doc(`${req.body.organization}-${req.body.phoneNumber}`)
      .withConverter(LeadsModel.converter)
      .get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const leads = getLeads.data()!;

    if (!leads.isTracking) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak dalam posisi track aktif',
        })
      );
    }

    const prevAgentCode = leads.agentCode;

    const { agent } = res.locals;

    leads.agentCode = req.body.changedAgentCode;
    leads.agentName = agent.name;
    leads.phoneNumberAgent = agent.phones.map(value => value.phone);

    const notesCounter = await leadsNotesCounter(leads);
    let notes: ILeadsNotesDocument<Date> = {
      agentCode: leads.agentCode,
      event: req.params.type,
      notes: `Perubahan kode agen`,
      organization: leads.organization,
      phoneNumber: leads.phoneNumber,
      statusLevel: leads.statusLevel,
      updatedAt: new Date(),
      updatedByUser: null,
      firstName: leads.firstName,
      lastName: leads.lastName,
      agentName: leads.agentName,
      moveToCold: false,
      reactivate: {
        currentTotal: notesCounter.totalReactivate,
      },
      totalUpdateNotes: notesCounter.totalNotes + 1,
    };

    leads.notes = notes.notes;
    leads.isTracking = true;
    leads.updatedAt = new Date();
    leads.updateHistories.push({
      ...notes,
    });

    try {
      const batch = myFirestore.batch();
      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      if (notes) {
        batch.set(myFirestore.collection('leads_notes').doc(), notes);
      }
      await batch.commit();

      await aggregatesAgent(leads.agentCode, req.body.organization).then();
      await aggregatesAgent(prevAgentCode, req.body.organization).then();
      res.send(
        successResponse({
          type: 'UPDATED',
          data: null,
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: `Error: ${e.toString()}`,
        })
      );
    }
  },
};

export default changeLeadsAgent;
