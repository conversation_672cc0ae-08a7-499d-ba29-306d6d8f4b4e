import { firestore } from 'firebase-admin';
import { CommonRegionFields } from '../client/document/region-types';

export interface IIdCardDocument {
  id_card_number: string;
  name: string;
  place_of_birth: string;
  date_of_birth: firestore.Timestamp | null;
  gender: 'male' | 'female' | null;
  address: string;
  occupation: string;
  marital_status: string | null;
  id_card_image?: null | string;
  guarantor_id_card_name: string;
  guarantor_id_card_number: string;
}

export interface IIdCard<DATE = Date> {
  idCardImage: string;
  idCardNumber: string;
  fullName: string;
  birthMother: string;
  placeOfBirth: string;
  dateOfBirth: DATE;

  occupation: string;
  occupationCode: string;

  maritalStatus: string;
  maritalStatusCode: string;

  lastEducation: string;
  lastEducationCode: string;

  fullAddress: string;
  zipCode: string;
  province: CommonRegionFields;
  city: CommonRegionFields;
  district: CommonRegionFields;
  subDistrict: CommonRegionFields;
  hamlet: string;
  neighbourhood: string;

  domicileFullAddress: string;
  domicileZipCode: string;
  domicileProvince: CommonRegionFields;
  domicileCity: CommonRegionFields;
  domicileDistrict: CommonRegionFields;
  domicileSubDistrict: CommonRegionFields;
  domicileHamlet: string;
  domicileNeighbourhood: string;
  updatedAt: DATE;
}
