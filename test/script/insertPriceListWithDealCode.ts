import axios from 'axios';
import collect from 'collect.js';
import { myFirestore } from '../../src/services/firebaseAdmin';
import { firestore } from 'firebase-admin';
import DocumentReference = firestore.DocumentReference;

interface DefaultAddress {
  city_code: string;
  city_name: string;
  province_code: string;
  province_name: string;
}

interface Variant {
  code: string;
  custom_thumb_image: string;
  custom_image: string;
  variant_name: string;
  variant_color_code: string;
  variant_code: string;
  variant_color_name: string;
}

interface Vehicle {
  variant_custom: Variant[];
  variant_alternative_color: {
    name: string;
    code: string;
  };
  brand_name: string;
  model_name: string;
  models_name: string[];
  brand_uuid: string;
}

interface CreditOption {
  dp_amount: string;
  tenor: string[];
  installment_amount: string;
  finco_name: string;
  finco_branch: string;
  finco_code: string;
  otr: number;
}

interface DataEntryOption {
  name: string;
  show: boolean;
  require: boolean;
  type: string;
  validation: boolean;
}

interface PromoCode {
  promo_codes: string[];
  total_promo_discount: number;
}

interface EventPoint {
  name: string;
  key: string;
  point: number;
}

interface CampaignData {
  success: boolean;
  data: {
    company: string;
    deal_code: string;
    url_image: string;
    url_thumb_image: string;
    start_period: string;
    end_period: string;
    active: boolean;
    show: boolean;
    caption: string;
    notes: string;
    purchase_method: string;
    area: string[];
    default_address: DefaultAddress;
    custom_price: number;
    custom_price_range_up: number;
    custom_price_range_down: number;
    take_vehicle_in_dealer: string;
    agent_code: string;
    promo_codes: string[];
    total_promo_discount: number;
    vehicle: Vehicle;
    credit: CreditOption[];
    data_entry_option: DataEntryOption[];
    image_campaign_custom: string[];
    event_points: EventPoint[];
    default_finco_id: string;
    default_dealer_code: string;
    default_dealer_name: string;
  };
}

const fetch = (dealCode: string) => {
  return axios.get<CampaignData>(
    'https://zvu1c5uoue.execute-api.ap-southeast-1.amazonaws.com/v1/deal/amarta/' + dealCode,
    {
      headers: {
        'x-api-key': '5ukoYcllpl6lIeKsbeIPI4hOZGDszFVk1dDBddHi',
      },
    }
  );
};

const insertPriceListWithDealCode = async (_dealCode: string) => {
  const get = await fetch(_dealCode);
  const { data } = get.data;

  const promoCode = data.promo_codes.length > 0 ? data.promo_codes[0] : '';
  const promoAmount = data.total_promo_discount;

  const cityGroup = data.area[0].toUpperCase();
  const dealCode = data.deal_code;
  const variantCode = data.vehicle.variant_custom[0].variant_code;
  const modelName = data.vehicle.model_name.toUpperCase();
  const collectPriceList = collect(data.credit);
  const downPayments = collectPriceList.groupBy('dp_amount').keys().toArray();
  const tenors = collectPriceList.groupBy('tenor').keys().toArray();

  const _downPayments: any[] = [];

  downPayments.forEach(dp => {
    const _dp = {
      downPayment: parseInt(dp as any),
      tenors: [] as any[],
    };
    tenors.forEach(t => {
      const _tenor = {
        tenor: parseInt(t as any),
        installment: 0,
      };

      const find = data.credit.find(v => {
        return v.dp_amount === dp && v.tenor[0] === t;
      });
      if (find) _tenor.installment = parseInt(find.installment_amount as any);
      _dp.tenors.push(_tenor);
    });

    _downPayments.push(_dp);
  });

  console.log(JSON.stringify(_downPayments));

  const dataToInsert: any = {
    cityGroup,
    dealCode,
    downPayments: _downPayments,
    modelName: modelName,
    promoCode: {
      code: promoCode,
      discount: promoAmount,
    },
    variantCode: variantCode,
  };

  const collectionRef = myFirestore.collection('vehicle_price_list_conversation_flow');
  const findDoc = await collectionRef
    .where('cityGroup', '==', cityGroup)
    .where('dealCode', '==', dealCode)
    .where('modelName', '==', modelName)
    .get();

  let docRef: DocumentReference | null = null;

  if (findDoc.size > 0) {
    console.log('DOKUMEN SUDAH ADA');
    findDoc.forEach(result => {
      docRef = result.ref;
    });
  } else {
    console.log('DOKUMEN BELUM ADA');
    docRef = collectionRef.doc();
  }

  if (!docRef) {
    return;
  }

  await docRef.set(dataToInsert);
};

insertPriceListWithDealCode('ONLPCXABSBANDUNG');
