import { json, RequestHandler } from 'express';
import { body } from 'express-validator';
import requestValidator from '../../../middlewares/requestValidator';
import { myFirestore } from '../../../services/firebaseAdmin';
import LeadsModel from '../../../model/LeadsModel';
import errorResponse from '../../../schema/errorResponse';
import successResponse from '../../../schema/successResponse';
import moment from 'moment';

interface ReqBody {
  organization: string;
  phoneNumber: string;
  offerCode: string;
}

const setLeadsOfferCode: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, any, ReqBody>;
} = {
  middlewares: [
    json(),
    body('organization').notEmpty(),
    body('phoneNumber').notEmpty(),
    body('offerCode').notEmpty(),
    requestValidator,
  ],
  handler: async (req, res) => {
    const collection = myFirestore.collection('leads');
    const docName = `${req.body.organization}-${req.body.phoneNumber}`;
    const getLeads = await collection.withConverter(LeadsModel.converter).doc(docName).get();

    if (!getLeads.exists) {
      return res.status(500).send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
          messages: 'Leads tidak ditemukan',
        })
      );
    }

    const now = moment();
    const leads = getLeads.data()!;

    leads.requestPromo = {
      offerCode: req.body.offerCode,
      createdAt: now.toDate(),
    };

    try {
      const batch = myFirestore.batch();
      batch.set(leads.ref.withConverter(LeadsModel.converter), leads);
      await batch.commit();

      res.send(
        successResponse({
          data: leads.toJsonResponse(),
          type: 'CREATED',
        })
      );
    } catch (e: any) {
      res.status(500).send(
        errorResponse({
          type: 'SERVER_ERROR',
          messages: e.toString(),
          data: JSON.stringify(e),
        })
      );
    }
  },
};

export default setLeadsOfferCode;
