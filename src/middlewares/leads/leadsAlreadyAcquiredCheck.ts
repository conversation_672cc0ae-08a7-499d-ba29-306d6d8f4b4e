import { NextFunction, Request, Response } from 'express';
import { myFirestore } from '../../services/firebaseAdmin';
import { errorLogsAddLeads } from '../../helpers/leads/errorLogs';
import errorResponse from '../../schema/errorResponse';

async function leadsAlreadyAcquiredCheck(req: Request, res: Response, next: NextFunction) {
  const collections = myFirestore.collection('leads');
  const find = await collections.doc(`${req.body.source2}-${req.body.phoneNumber}`).get();

  if (find.exists) {
    const messages = `Nomor telepon ini sudah didaftarkan untuk ${req.body.source2}`;
    errorLogsAddLeads(req, messages);
    return res.status(500).send(
      errorResponse({
        type: 'DUPLICATED_DATA',
        messages: messages,
        data: JSON.stringify({
          phoneNumber: req.body.phoneNumber,
          organization: req.body.source2,
        }),
      })
    );
  } else {
    next();
  }
}

export default leadsAlreadyAcquiredCheck;
