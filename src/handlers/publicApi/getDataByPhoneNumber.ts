import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'express';
import { query } from 'express-validator';
import requestValidator from '../../middlewares/requestValidator';
import { ResponseSchema } from '../../schema/types/ResponseSchema';
import { myFirestore } from '../../services/firebaseAdmin';
import errorResponse from '../../schema/errorResponse';
import ClientModel from '../../model/ClientModel';
import { profileService } from '../../services/profileService';
import { TProfileResponse } from '../../types/services/profile_service_types';
import successResponse from '../../schema/successResponse';

interface ReqQuery {
  phoneNumber: string;
}

const getDataByPhoneNumber: {
  middlewares: RequestHandler[];
  handler: RequestHandler<any, ResponseSchema, any, ReqQuery>;
} = {
  middlewares: [query('phoneNumber'), requestValidator],
  handler: async function (req, res) {
    const clientCollection = myFirestore.collection('clients');
    const findClient = await clientCollection
      .where('contacts.whatsapp', '==', req.query.phoneNumber)
      .withConverter(ClientModel.converter)
      .get();

    if (findClient.size < 1) {
      return res.send(
        errorResponse({
          type: 'ENTITY_NOT_FOUND',
        })
      );
    }

    let dataClientFromFirestore!: ClientModel;
    findClient.forEach(s => {
      dataClientFromFirestore = s.data() as any;
    });

    let dataOwner: TProfileResponse | null = null;
    if (dataClientFromFirestore.details?.owner_phone_number) {
      const get = await profileService.getProfile(
        dataClientFromFirestore.details.owner_phone_number
      );
      if (get.data.length > 0) {
        dataOwner = get.data[0];
      }
    }

    let dataGuarantor: TProfileResponse | null = null;
    if (dataClientFromFirestore.details?.guarantor_phone_number) {
      const get = await profileService.getProfile(
        dataClientFromFirestore.details.guarantor_phone_number
      );
      if (get.data.length > 0) {
        dataGuarantor = get.data[0];
      }
    }

    let dataOrderMaker: TProfileResponse | null = null;
    if (dataClientFromFirestore.details?.order_maker_phone_number) {
      const get = await profileService.getProfile(
        dataClientFromFirestore.details.order_maker_phone_number
      );
      if (get.data.length > 0) {
        dataOrderMaker = get.data[0];
      }
    }

    return res.send(
      successResponse({
        type: 'FETCHED',
        data: {
          dataOwner,
          dataGuarantor,
          dataOrderMaker,
        },
      })
    );
  },
};

export default getDataByPhoneNumber;
